import{r as s,I as ii,f as X,R as a,B as fe,W as R,H as A,J as O,M as Ti,v as _i,d as Hi,X as Di,$ as Ri,a1 as v,ad as Ai,a0 as Fe,Z as Li,ae as Ki,a5 as Wi,a4 as Vi,af as qi}from"./index-BNo7PP7q.js";import{h as Xi,R as Ge,i as Ue}from"./index-C2aV_UZj.js";import{n as Qe,S as Se,A as Ji,G as Fi,H as Gi,J as Ui,<PERSON> as Qi,M as Zi,N as Yi,w as ki}from"./rules-B0Vq_sHo.js";var et={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M272.9 512l265.4-339.1c4.1-5.2.4-12.9-6.3-12.9h-77.3c-4.9 0-9.6 2.3-12.6 6.1L186.8 492.3a31.99 31.99 0 000 39.5l255.3 326.1c3 3.9 7.7 6.1 12.6 6.1H532c6.7 0 10.4-7.7 6.3-12.9L272.9 512zm304 0l265.4-339.1c4.1-5.2.4-12.9-6.3-12.9h-77.3c-4.9 0-9.6 2.3-12.6 6.1L490.8 492.3a31.99 31.99 0 000 39.5l255.3 326.1c3 3.9 7.7 6.1 12.6 6.1H836c6.7 0 10.4-7.7 6.3-12.9L576.9 512z"}}]},name:"double-left",theme:"outlined"},it=function(i,l){return s.createElement(ii,X({},i,{ref:l,icon:et}))},Ze=s.forwardRef(it),tt={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M533.2 492.3L277.9 166.1c-3-3.9-7.7-6.1-12.6-6.1H188c-6.7 0-10.4 7.7-6.3 12.9L447.1 512 181.7 851.1A7.98 7.98 0 00188 864h77.3c4.9 0 9.6-2.3 12.6-6.1l255.3-326.1c9.1-11.7 9.1-27.9 0-39.5zm304 0L581.9 166.1c-3-3.9-7.7-6.1-12.6-6.1H492c-6.7 0-10.4 7.7-6.3 12.9L751.1 512 485.7 851.1A7.98 7.98 0 00492 864h77.3c4.9 0 9.6-2.3 12.6-6.1l255.3-326.1c9.1-11.7 9.1-27.9 0-39.5z"}}]},name:"double-right",theme:"outlined"},nt=function(i,l){return s.createElement(ii,X({},i,{ref:l,icon:tt}))},Ye=s.forwardRef(nt),at={items_per_page:"条/页",jump_to:"跳至",jump_to_confirm:"确定",page:"页",prev_page:"上一页",next_page:"下一页",prev_5:"向前 5 页",next_5:"向后 5 页",prev_3:"向前 3 页",next_3:"向后 3 页",page_size:"页码"},rt=["10","20","50","100"],ot=function(i){var l=i.pageSizeOptions,u=l===void 0?rt:l,t=i.locale,x=i.changeSize,w=i.pageSize,B=i.goButton,S=i.quickGo,M=i.rootPrefixCls,z=i.selectComponentClass,P=i.selectPrefixCls,j=i.disabled,d=i.buildOptionText,L=a.useState(""),N=fe(L,2),h=N[0],f=N[1],c=function(){return!h||Number.isNaN(h)?void 0:Number(h)},Y=typeof d=="function"?d:function(g){return"".concat(g," ").concat(t.items_per_page)},J=function(m){x==null||x(Number(m))},U=function(m){f(m.target.value)},k=function(m){B||h===""||(f(""),!(m.relatedTarget&&(m.relatedTarget.className.indexOf("".concat(M,"-item-link"))>=0||m.relatedTarget.className.indexOf("".concat(M,"-item"))>=0))&&(S==null||S(c())))},E=function(m){h!==""&&(m.keyCode===R.ENTER||m.type==="click")&&(f(""),S==null||S(c()))},T=function(){return u.some(function(m){return m.toString()===w.toString()})?u:u.concat([w.toString()]).sort(function(m,F){var ee=Number.isNaN(Number(m))?0:Number(m),V=Number.isNaN(Number(F))?0:Number(F);return ee-V})},_="".concat(M,"-options");if(!x&&!S)return null;var y=null,K=null,W=null;if(x&&z){var Q=T().map(function(g,m){return a.createElement(z.Option,{key:m,value:g.toString()},Y(g))});y=a.createElement(z,{disabled:j,prefixCls:P,showSearch:!1,className:"".concat(_,"-size-changer"),optionLabelProp:"children",popupMatchSelectWidth:!1,value:(w||u[0]).toString(),onChange:J,getPopupContainer:function(m){return m.parentNode},"aria-label":t.page_size,defaultOpen:!1},Q)}return S&&(B&&(W=typeof B=="boolean"?a.createElement("button",{type:"button",onClick:E,onKeyUp:E,disabled:j,className:"".concat(_,"-quick-jumper-button")},t.jump_to_confirm):a.createElement("span",{onClick:E,onKeyUp:E},B)),K=a.createElement("div",{className:"".concat(_,"-quick-jumper")},t.jump_to,a.createElement("input",{disabled:j,type:"text",value:h,onChange:U,onKeyUp:E,onBlur:k,"aria-label":t.page}),t.page,W)),a.createElement("li",{className:_},y,K)},ce=function(i){var l,u=i.rootPrefixCls,t=i.page,x=i.active,w=i.className,B=i.showTitle,S=i.onClick,M=i.onKeyPress,z=i.itemRender,P="".concat(u,"-item"),j=A(P,"".concat(P,"-").concat(t),(l={},O(l,"".concat(P,"-active"),x),O(l,"".concat(P,"-disabled"),!t),l),w),d=function(){S(t)},L=function(f){M(f,S,t)},N=z(t,"page",a.createElement("a",{rel:"nofollow"},t));return N?a.createElement("li",{title:B?String(t):null,className:j,onClick:d,onKeyDown:L,tabIndex:0},N):null},lt=function(i,l,u){return u};function ke(){}function ei(e){var i=Number(e);return typeof i=="number"&&!Number.isNaN(i)&&isFinite(i)&&Math.floor(i)===i}function G(e,i,l){var u=typeof e>"u"?i:e;return Math.floor((l-1)/u)+1}var ct=function(i){var l,u=i.prefixCls,t=u===void 0?"rc-pagination":u,x=i.selectPrefixCls,w=x===void 0?"rc-select":x,B=i.className,S=i.selectComponentClass,M=i.current,z=i.defaultCurrent,P=z===void 0?1:z,j=i.total,d=j===void 0?0:j,L=i.pageSize,N=i.defaultPageSize,h=N===void 0?10:N,f=i.onChange,c=f===void 0?ke:f,Y=i.hideOnSinglePage,J=i.align,U=i.showPrevNextJumpers,k=U===void 0?!0:U,E=i.showQuickJumper,T=i.showLessItems,_=i.showTitle,y=_===void 0?!0:_,K=i.onShowSizeChange,W=K===void 0?ke:K,Q=i.locale,g=Q===void 0?at:Q,m=i.style,F=i.totalBoundaryShowSizeChanger,ee=F===void 0?50:F,V=i.disabled,H=i.simple,ze=i.showTotal,Ce=i.showSizeChanger,oi=i.pageSizeOptions,Ne=i.itemRender,ie=Ne===void 0?lt:Ne,Ee=i.jumpPrevIcon,Ie=i.jumpNextIcon,li=i.prevIcon,ci=i.nextIcon,si=a.useRef(null),ui=Qe(10,{value:L,defaultValue:h}),Oe=fe(ui,2),$=Oe[0],mi=Oe[1],di=Qe(1,{value:M,defaultValue:P,postState:function(o){return Math.max(1,Math.min(o,G(void 0,$,d)))}}),Be=fe(di,2),r=Be[0],je=Be[1],gi=a.useState(r),we=fe(gi,2),Z=we[0],se=we[1];s.useEffect(function(){se(r)},[r]);var Me=Math.max(1,r-(T?3:5)),Te=Math.min(G(void 0,$,d),r+(T?3:5));function ue(n,o){var p=n||a.createElement("button",{type:"button","aria-label":o,className:"".concat(t,"-item-link")});return typeof n=="function"&&(p=a.createElement(n,Hi({},i))),p}function _e(n){var o=n.target.value,p=G(void 0,$,d),q;return o===""?q=o:Number.isNaN(Number(o))?q=Z:o>=p?q=p:q=Number(o),q}function pi(n){return ei(n)&&n!==r&&ei(d)&&d>0}var vi=d>$?E:!1;function bi(n){(n.keyCode===R.UP||n.keyCode===R.DOWN)&&n.preventDefault()}function He(n){var o=_e(n);switch(o!==Z&&se(o),n.keyCode){case R.ENTER:I(o);break;case R.UP:I(o-1);break;case R.DOWN:I(o+1);break}}function hi(n){I(_e(n))}function fi(n){var o=G(n,$,d),p=r>o&&o!==0?o:r;mi(n),se(p),W==null||W(r,n),je(p),c==null||c(p,n)}function I(n){if(pi(n)&&!V){var o=G(void 0,$,d),p=n;return n>o?p=o:n<1&&(p=1),p!==Z&&se(p),je(p),c==null||c(p,$),p}return r}var me=r>1,de=r<G(void 0,$,d),Si=Ce??d>ee;function De(){me&&I(r-1)}function Re(){de&&I(r+1)}function Ae(){I(Me)}function Le(){I(Te)}function te(n,o){if(n.key==="Enter"||n.charCode===R.ENTER||n.keyCode===R.ENTER){for(var p=arguments.length,q=new Array(p>2?p-2:0),he=2;he<p;he++)q[he-2]=arguments[he];o.apply(void 0,q)}}function Ci(n){te(n,De)}function $i(n){te(n,Re)}function yi(n){te(n,Ae)}function xi(n){te(n,Le)}function Pi(n){var o=ie(n,"prev",ue(li,"prev page"));return a.isValidElement(o)?a.cloneElement(o,{disabled:!me}):o}function zi(n){var o=ie(n,"next",ue(ci,"next page"));return a.isValidElement(o)?a.cloneElement(o,{disabled:!de}):o}function ge(n){(n.type==="click"||n.keyCode===R.ENTER)&&I(Z)}var Ke=null,Ni=Ti(i,{aria:!0,data:!0}),Ei=ze&&a.createElement("li",{className:"".concat(t,"-total-text")},ze(d,[d===0?0:(r-1)*$+1,r*$>d?d:r*$])),We=null,b=G(void 0,$,d);if(Y&&d<=$)return null;var C=[],ne={rootPrefixCls:t,onClick:I,onKeyPress:te,showTitle:y,itemRender:ie,page:-1},Ii=r-1>0?r-1:0,Oi=r+1<b?r+1:b,pe=E&&E.goButton,Bi=_i(H)==="object"?H.readOnly:!H,ae=pe,Ve=null;H&&(pe&&(typeof pe=="boolean"?ae=a.createElement("button",{type:"button",onClick:ge,onKeyUp:ge},g.jump_to_confirm):ae=a.createElement("span",{onClick:ge,onKeyUp:ge},pe),ae=a.createElement("li",{title:y?"".concat(g.jump_to).concat(r,"/").concat(b):null,className:"".concat(t,"-simple-pager")},ae)),Ve=a.createElement("li",{title:y?"".concat(r,"/").concat(b):null,className:"".concat(t,"-simple-pager")},Bi?Z:a.createElement("input",{type:"text",value:Z,disabled:V,onKeyDown:bi,onKeyUp:He,onChange:He,onBlur:hi,size:3}),a.createElement("span",{className:"".concat(t,"-slash")},"/"),b));var D=T?1:2;if(b<=3+D*2){b||C.push(a.createElement(ce,X({},ne,{key:"noPager",page:1,className:"".concat(t,"-item-disabled")})));for(var re=1;re<=b;re+=1)C.push(a.createElement(ce,X({},ne,{key:re,page:re,active:r===re})))}else{var ji=T?g.prev_3:g.prev_5,wi=T?g.next_3:g.next_5,qe=ie(Me,"jump-prev",ue(Ee,"prev page")),Xe=ie(Te,"jump-next",ue(Ie,"next page"));k&&(Ke=qe?a.createElement("li",{title:y?ji:null,key:"prev",onClick:Ae,tabIndex:0,onKeyDown:yi,className:A("".concat(t,"-jump-prev"),O({},"".concat(t,"-jump-prev-custom-icon"),!!Ee))},qe):null,We=Xe?a.createElement("li",{title:y?wi:null,key:"next",onClick:Le,tabIndex:0,onKeyDown:xi,className:A("".concat(t,"-jump-next"),O({},"".concat(t,"-jump-next-custom-icon"),!!Ie))},Xe):null);var $e=Math.max(1,r-D),ye=Math.min(r+D,b);r-1<=D&&(ye=1+D*2),b-r<=D&&($e=b-D*2);for(var oe=$e;oe<=ye;oe+=1)C.push(a.createElement(ce,X({},ne,{key:oe,page:oe,active:r===oe})));if(r-1>=D*2&&r!==3&&(C[0]=a.cloneElement(C[0],{className:A("".concat(t,"-item-after-jump-prev"),C[0].props.className)}),C.unshift(Ke)),b-r>=D*2&&r!==b-2){var Je=C[C.length-1];C[C.length-1]=a.cloneElement(Je,{className:A("".concat(t,"-item-before-jump-next"),Je.props.className)}),C.push(We)}$e!==1&&C.unshift(a.createElement(ce,X({},ne,{key:1,page:1}))),ye!==b&&C.push(a.createElement(ce,X({},ne,{key:b,page:b})))}var ve=Pi(Ii);if(ve){var xe=!me||!b;ve=a.createElement("li",{title:y?g.prev_page:null,onClick:De,tabIndex:xe?null:0,onKeyDown:Ci,className:A("".concat(t,"-prev"),O({},"".concat(t,"-disabled"),xe)),"aria-disabled":xe},ve)}var be=zi(Oi);if(be){var le,Pe;H?(le=!de,Pe=me?0:null):(le=!de||!b,Pe=le?null:0),be=a.createElement("li",{title:y?g.next_page:null,onClick:Re,tabIndex:Pe,onKeyDown:$i,className:A("".concat(t,"-next"),O({},"".concat(t,"-disabled"),le)),"aria-disabled":le},be)}var Mi=A(t,B,(l={},O(l,"".concat(t,"-start"),J==="start"),O(l,"".concat(t,"-center"),J==="center"),O(l,"".concat(t,"-end"),J==="end"),O(l,"".concat(t,"-simple"),H),O(l,"".concat(t,"-disabled"),V),l));return a.createElement("ul",X({className:Mi,style:m,ref:si},Ni),Ei,ve,H?Ve:C,be,a.createElement(ot,{locale:g,rootPrefixCls:t,disabled:V,selectComponentClass:S,selectPrefixCls:w,changeSize:Si?fi:null,pageSize:$,pageSizeOptions:oi,quickGo:vi?I:null,goButton:ae}))};const ti=e=>s.createElement(Se,Object.assign({},e,{showSearch:!0,size:"small"})),ni=e=>s.createElement(Se,Object.assign({},e,{showSearch:!0,size:"middle"}));ti.Option=Se.Option;ni.Option=Se.Option;const st=e=>{const{componentCls:i}=e;return{[`${i}-disabled`]:{"&, &:hover":{cursor:"not-allowed",[`${i}-item-link`]:{color:e.colorTextDisabled,cursor:"not-allowed"}},"&:focus-visible":{cursor:"not-allowed",[`${i}-item-link`]:{color:e.colorTextDisabled,cursor:"not-allowed"}}},[`&${i}-disabled`]:{cursor:"not-allowed",[`${i}-item`]:{cursor:"not-allowed","&:hover, &:active":{backgroundColor:"transparent"},a:{color:e.colorTextDisabled,backgroundColor:"transparent",border:"none",cursor:"not-allowed"},"&-active":{borderColor:e.colorBorder,backgroundColor:e.itemActiveBgDisabled,"&:hover, &:active":{backgroundColor:e.itemActiveBgDisabled},a:{color:e.itemActiveColorDisabled}}},[`${i}-item-link`]:{color:e.colorTextDisabled,cursor:"not-allowed","&:hover, &:active":{backgroundColor:"transparent"},[`${i}-simple&`]:{backgroundColor:"transparent","&:hover, &:active":{backgroundColor:"transparent"}}},[`${i}-simple-pager`]:{color:e.colorTextDisabled},[`${i}-jump-prev, ${i}-jump-next`]:{[`${i}-item-link-icon`]:{opacity:0},[`${i}-item-ellipsis`]:{opacity:1}}},[`&${i}-simple`]:{[`${i}-prev, ${i}-next`]:{[`&${i}-disabled ${i}-item-link`]:{"&:hover, &:active":{backgroundColor:"transparent"}}}}}},ut=e=>{const{componentCls:i}=e;return{[`&${i}-mini ${i}-total-text, &${i}-mini ${i}-simple-pager`]:{height:e.itemSizeSM,lineHeight:v(e.itemSizeSM)},[`&${i}-mini ${i}-item`]:{minWidth:e.itemSizeSM,height:e.itemSizeSM,margin:0,lineHeight:v(e.calc(e.itemSizeSM).sub(2).equal())},[`&${i}-mini:not(${i}-disabled) ${i}-item:not(${i}-item-active)`]:{backgroundColor:"transparent",borderColor:"transparent","&:hover":{backgroundColor:e.colorBgTextHover},"&:active":{backgroundColor:e.colorBgTextActive}},[`&${i}-mini ${i}-prev, &${i}-mini ${i}-next`]:{minWidth:e.itemSizeSM,height:e.itemSizeSM,margin:0,lineHeight:v(e.itemSizeSM)},[`&${i}-mini:not(${i}-disabled)`]:{[`${i}-prev, ${i}-next`]:{[`&:hover ${i}-item-link`]:{backgroundColor:e.colorBgTextHover},[`&:active ${i}-item-link`]:{backgroundColor:e.colorBgTextActive},[`&${i}-disabled:hover ${i}-item-link`]:{backgroundColor:"transparent"}}},[`
    &${i}-mini ${i}-prev ${i}-item-link,
    &${i}-mini ${i}-next ${i}-item-link
    `]:{backgroundColor:"transparent",borderColor:"transparent","&::after":{height:e.itemSizeSM,lineHeight:v(e.itemSizeSM)}},[`&${i}-mini ${i}-jump-prev, &${i}-mini ${i}-jump-next`]:{height:e.itemSizeSM,marginInlineEnd:0,lineHeight:v(e.itemSizeSM)},[`&${i}-mini ${i}-options`]:{marginInlineStart:e.paginationMiniOptionsMarginInlineStart,"&-size-changer":{top:e.miniOptionsSizeChangerTop},"&-quick-jumper":{height:e.itemSizeSM,lineHeight:v(e.itemSizeSM),input:Object.assign(Object.assign({},Gi(e)),{width:e.paginationMiniQuickJumperInputWidth,height:e.controlHeightSM})}}}},mt=e=>{const{componentCls:i}=e;return{[`
    &${i}-simple ${i}-prev,
    &${i}-simple ${i}-next
    `]:{height:e.itemSizeSM,lineHeight:v(e.itemSizeSM),verticalAlign:"top",[`${i}-item-link`]:{height:e.itemSizeSM,backgroundColor:"transparent",border:0,"&:hover":{backgroundColor:e.colorBgTextHover},"&:active":{backgroundColor:e.colorBgTextActive},"&::after":{height:e.itemSizeSM,lineHeight:v(e.itemSizeSM)}}},[`&${i}-simple ${i}-simple-pager`]:{display:"inline-block",height:e.itemSizeSM,marginInlineEnd:e.marginXS,input:{boxSizing:"border-box",height:"100%",padding:`0 ${v(e.paginationItemPaddingInline)}`,textAlign:"center",backgroundColor:e.itemInputBg,border:`${v(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadius,outline:"none",transition:`border-color ${e.motionDurationMid}`,color:"inherit","&:hover":{borderColor:e.colorPrimary},"&:focus":{borderColor:e.colorPrimaryHover,boxShadow:`${v(e.inputOutlineOffset)} 0 ${v(e.controlOutlineWidth)} ${e.controlOutline}`},"&[disabled]":{color:e.colorTextDisabled,backgroundColor:e.colorBgContainerDisabled,borderColor:e.colorBorder,cursor:"not-allowed"}}}}},dt=e=>{const{componentCls:i,antCls:l}=e;return{[`${i}-jump-prev, ${i}-jump-next`]:{outline:0,[`${i}-item-container`]:{position:"relative",[`${i}-item-link-icon`]:{color:e.colorPrimary,fontSize:e.fontSizeSM,opacity:0,transition:`all ${e.motionDurationMid}`,"&-svg":{top:0,insetInlineEnd:0,bottom:0,insetInlineStart:0,margin:"auto"}},[`${i}-item-ellipsis`]:{position:"absolute",top:0,insetInlineEnd:0,bottom:0,insetInlineStart:0,display:"block",margin:"auto",color:e.colorTextDisabled,fontFamily:"Arial, Helvetica, sans-serif",letterSpacing:e.paginationEllipsisLetterSpacing,textAlign:"center",textIndent:e.paginationEllipsisTextIndent,opacity:1,transition:`all ${e.motionDurationMid}`}},"&:hover":{[`${i}-item-link-icon`]:{opacity:1},[`${i}-item-ellipsis`]:{opacity:0}}},[`
    ${i}-prev,
    ${i}-jump-prev,
    ${i}-jump-next
    `]:{marginInlineEnd:e.marginXS},[`
    ${i}-prev,
    ${i}-next,
    ${i}-jump-prev,
    ${i}-jump-next
    `]:{display:"inline-block",minWidth:e.itemSize,height:e.itemSize,color:e.colorText,fontFamily:e.fontFamily,lineHeight:v(e.itemSize),textAlign:"center",verticalAlign:"middle",listStyle:"none",borderRadius:e.borderRadius,cursor:"pointer",transition:`all ${e.motionDurationMid}`},[`${i}-prev, ${i}-next`]:{fontFamily:"Arial, Helvetica, sans-serif",outline:0,button:{color:e.colorText,cursor:"pointer",userSelect:"none"},[`${i}-item-link`]:{display:"block",width:"100%",height:"100%",padding:0,fontSize:e.fontSizeSM,textAlign:"center",backgroundColor:"transparent",border:`${v(e.lineWidth)} ${e.lineType} transparent`,borderRadius:e.borderRadius,outline:"none",transition:`all ${e.motionDurationMid}`},[`&:hover ${i}-item-link`]:{backgroundColor:e.colorBgTextHover},[`&:active ${i}-item-link`]:{backgroundColor:e.colorBgTextActive},[`&${i}-disabled:hover`]:{[`${i}-item-link`]:{backgroundColor:"transparent"}}},[`${i}-slash`]:{marginInlineEnd:e.paginationSlashMarginInlineEnd,marginInlineStart:e.paginationSlashMarginInlineStart},[`${i}-options`]:{display:"inline-block",marginInlineStart:e.margin,verticalAlign:"middle","&-size-changer":{display:"inline-block",width:"auto",[`${l}-select-arrow:not(:last-child)`]:{opacity:1}},"&-quick-jumper":{display:"inline-block",height:e.controlHeight,marginInlineStart:e.marginXS,lineHeight:v(e.controlHeight),verticalAlign:"top",input:Object.assign(Object.assign(Object.assign({},Ui(e)),Qi(e,{borderColor:e.colorBorder,hoverBorderColor:e.colorPrimaryHover,activeBorderColor:e.colorPrimary,activeShadow:e.activeShadow})),{"&[disabled]":Object.assign({},Zi(e)),width:e.calc(e.controlHeightLG).mul(1.25).equal(),height:e.controlHeight,boxSizing:"border-box",margin:0,marginInlineStart:e.marginXS,marginInlineEnd:e.marginXS})}}}},gt=e=>{const{componentCls:i}=e;return{[`${i}-item`]:{display:"inline-block",minWidth:e.itemSize,height:e.itemSize,marginInlineEnd:e.marginXS,fontFamily:e.fontFamily,lineHeight:v(e.calc(e.itemSize).sub(2).equal()),textAlign:"center",verticalAlign:"middle",listStyle:"none",backgroundColor:e.itemBg,border:`${v(e.lineWidth)} ${e.lineType} transparent`,borderRadius:e.borderRadius,outline:0,cursor:"pointer",userSelect:"none",a:{display:"block",padding:`0 ${v(e.paginationItemPaddingInline)}`,color:e.colorText,"&:hover":{textDecoration:"none"}},[`&:not(${i}-item-active)`]:{"&:hover":{transition:`all ${e.motionDurationMid}`,backgroundColor:e.colorBgTextHover},"&:active":{backgroundColor:e.colorBgTextActive}},"&-active":{fontWeight:e.fontWeightStrong,backgroundColor:e.itemActiveBg,borderColor:e.colorPrimary,a:{color:e.colorPrimary},"&:hover":{borderColor:e.colorPrimaryHover},"&:hover a":{color:e.colorPrimaryHover}}}}},pt=e=>{const{componentCls:i}=e;return{[i]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},Ri(e)),{display:"flex","&-start":{justifyContent:"start"},"&-center":{justifyContent:"center"},"&-end":{justifyContent:"end"},"ul, ol":{margin:0,padding:0,listStyle:"none"},"&::after":{display:"block",clear:"both",height:0,overflow:"hidden",visibility:"hidden",content:'""'},[`${i}-total-text`]:{display:"inline-block",height:e.itemSize,marginInlineEnd:e.marginXS,lineHeight:v(e.calc(e.itemSize).sub(2).equal()),verticalAlign:"middle"}}),gt(e)),dt(e)),mt(e)),ut(e)),st(e)),{[`@media only screen and (max-width: ${e.screenLG}px)`]:{[`${i}-item`]:{"&-after-jump-prev, &-before-jump-next":{display:"none"}}},[`@media only screen and (max-width: ${e.screenSM}px)`]:{[`${i}-options`]:{display:"none"}}}),[`&${e.componentCls}-rtl`]:{direction:"rtl"}}},vt=e=>{const{componentCls:i}=e;return{[`${i}:not(${i}-disabled)`]:{[`${i}-item`]:Object.assign({},Ai(e)),[`${i}-jump-prev, ${i}-jump-next`]:{"&:focus-visible":Object.assign({[`${i}-item-link-icon`]:{opacity:1},[`${i}-item-ellipsis`]:{opacity:0}},Fe(e))},[`${i}-prev, ${i}-next`]:{[`&:focus-visible ${i}-item-link`]:Object.assign({},Fe(e))}}}},ai=e=>Object.assign({itemBg:e.colorBgContainer,itemSize:e.controlHeight,itemSizeSM:e.controlHeightSM,itemActiveBg:e.colorBgContainer,itemLinkBg:e.colorBgContainer,itemActiveColorDisabled:e.colorTextDisabled,itemActiveBgDisabled:e.controlItemBgActiveDisabled,itemInputBg:e.colorBgContainer,miniOptionsSizeChangerTop:0},Ji(e)),ri=e=>Li(e,{inputOutlineOffset:0,paginationMiniOptionsMarginInlineStart:e.calc(e.marginXXS).div(2).equal(),paginationMiniQuickJumperInputWidth:e.calc(e.controlHeightLG).mul(1.1).equal(),paginationItemPaddingInline:e.calc(e.marginXXS).mul(1.5).equal(),paginationEllipsisLetterSpacing:e.calc(e.marginXXS).div(2).equal(),paginationSlashMarginInlineStart:e.marginSM,paginationSlashMarginInlineEnd:e.marginSM,paginationEllipsisTextIndent:"0.13em"},Fi(e)),bt=Di("Pagination",e=>{const i=ri(e);return[pt(i),vt(i)]},ai),ht=e=>{const{componentCls:i}=e;return{[`${i}${i}-bordered${i}-disabled:not(${i}-mini)`]:{"&, &:hover":{[`${i}-item-link`]:{borderColor:e.colorBorder}},"&:focus-visible":{[`${i}-item-link`]:{borderColor:e.colorBorder}},[`${i}-item, ${i}-item-link`]:{backgroundColor:e.colorBgContainerDisabled,borderColor:e.colorBorder,[`&:hover:not(${i}-item-active)`]:{backgroundColor:e.colorBgContainerDisabled,borderColor:e.colorBorder,a:{color:e.colorTextDisabled}},[`&${i}-item-active`]:{backgroundColor:e.itemActiveBgDisabled}},[`${i}-prev, ${i}-next`]:{"&:hover button":{backgroundColor:e.colorBgContainerDisabled,borderColor:e.colorBorder,color:e.colorTextDisabled},[`${i}-item-link`]:{backgroundColor:e.colorBgContainerDisabled,borderColor:e.colorBorder}}},[`${i}${i}-bordered:not(${i}-mini)`]:{[`${i}-prev, ${i}-next`]:{"&:hover button":{borderColor:e.colorPrimaryHover,backgroundColor:e.itemBg},[`${i}-item-link`]:{backgroundColor:e.itemLinkBg,borderColor:e.colorBorder},[`&:hover ${i}-item-link`]:{borderColor:e.colorPrimary,backgroundColor:e.itemBg,color:e.colorPrimary},[`&${i}-disabled`]:{[`${i}-item-link`]:{borderColor:e.colorBorder,color:e.colorTextDisabled}}},[`${i}-item`]:{backgroundColor:e.itemBg,border:`${v(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,[`&:hover:not(${i}-item-active)`]:{borderColor:e.colorPrimary,backgroundColor:e.itemBg,a:{color:e.colorPrimary}},"&-active":{borderColor:e.colorPrimary}}}}},ft=Ki(["Pagination","bordered"],e=>{const i=ri(e);return[ht(i)]},ai);var St=function(e,i){var l={};for(var u in e)Object.prototype.hasOwnProperty.call(e,u)&&i.indexOf(u)<0&&(l[u]=e[u]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var t=0,u=Object.getOwnPropertySymbols(e);t<u.length;t++)i.indexOf(u[t])<0&&Object.prototype.propertyIsEnumerable.call(e,u[t])&&(l[u[t]]=e[u[t]]);return l};const xt=e=>{const{align:i,prefixCls:l,selectPrefixCls:u,className:t,rootClassName:x,style:w,size:B,locale:S,selectComponentClass:M,responsive:z,showSizeChanger:P}=e,j=St(e,["align","prefixCls","selectPrefixCls","className","rootClassName","style","size","locale","selectComponentClass","responsive","showSizeChanger"]),{xs:d}=Xi(z),[,L]=Wi(),{getPrefixCls:N,direction:h,pagination:f={}}=s.useContext(Vi),c=N("pagination",l),[Y,J,U]=bt(c),k=P??f.showSizeChanger,E=s.useMemo(()=>{const m=s.createElement("span",{className:`${c}-item-ellipsis`},"•••"),F=s.createElement("button",{className:`${c}-item-link`,type:"button",tabIndex:-1},h==="rtl"?s.createElement(Ge,null):s.createElement(Ue,null)),ee=s.createElement("button",{className:`${c}-item-link`,type:"button",tabIndex:-1},h==="rtl"?s.createElement(Ue,null):s.createElement(Ge,null)),V=s.createElement("a",{className:`${c}-item-link`},s.createElement("div",{className:`${c}-item-container`},h==="rtl"?s.createElement(Ye,{className:`${c}-item-link-icon`}):s.createElement(Ze,{className:`${c}-item-link-icon`}),m)),H=s.createElement("a",{className:`${c}-item-link`},s.createElement("div",{className:`${c}-item-container`},h==="rtl"?s.createElement(Ze,{className:`${c}-item-link-icon`}):s.createElement(Ye,{className:`${c}-item-link-icon`}),m));return{prevIcon:F,nextIcon:ee,jumpPrevIcon:V,jumpNextIcon:H}},[h,c]),[T]=Yi("Pagination",qi),_=Object.assign(Object.assign({},T),S),y=ki(B),K=y==="small"||!!(d&&!y&&z),W=N("select",u),Q=A({[`${c}-${i}`]:!!i,[`${c}-mini`]:K,[`${c}-rtl`]:h==="rtl",[`${c}-bordered`]:L.wireframe},f==null?void 0:f.className,t,x,J,U),g=Object.assign(Object.assign({},f==null?void 0:f.style),w);return Y(s.createElement(s.Fragment,null,L.wireframe&&s.createElement(ft,{prefixCls:c}),s.createElement(ct,Object.assign({},E,j,{style:g,prefixCls:c,selectPrefixCls:W,className:Q,selectComponentClass:M||(K?ti:ni),locale:_,showSizeChanger:k}))))};export{xt as P};
