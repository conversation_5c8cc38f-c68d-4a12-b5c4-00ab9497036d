import{r,j as t,l as N,L as S}from"./index-BNo7PP7q.js";import{F as b,u as p,B as k,h as A,a as E,e as B}from"./rules-B0Vq_sHo.js";import{a as P,c as D}from"./index-C2aV_UZj.js";import{U as R}from"./index-_7qsZaW1.js";import{C as v}from"./customtable-B8OJe14O.js";import"./index-BNdc7YlR.js";import"./notification-7LrlFXRF.js";import"./index-Dc53ayCB.js";import"./Pagination-Cl4XtiYj.js";const I=({onCancel:i,refreshDataTable:l,editData:s})=>{const[a]=b.useForm(),{loading:h,postData:x}=p("create_user_type",{type:"submit"}),{loading:f,postData:y}=p("edit_user_type",{type:"submit"});r.useEffect(()=>{s?a.setFieldsValue({title:s.title}):a.resetFields()},[s,a]);const u=n=>{const d=new FormData;for(const m in n)d.append(m,n[m]);s?y(d,o,s._id):x(d,o)},o=n=>{n.statusCode===200&&(i(),a.resetFields(),l())};return t.jsxs(b,{name:"userTypeForm",layout:"vertical",onFinish:u,initialValues:{remember:!0},form:a,autoComplete:"off",children:[t.jsx(k,{name:"title",placeholder:"",label:"Type Name",rules:A.title}),t.jsx("div",{className:"text-end mt-4",children:t.jsx(E,{title:s?"Update":"Save",className:"add-new-btn",htmlType:"submit",loading:s?f:h})})]})},L=r.memo(I),M=r.memo(({isModalOpen:i,onClose:l,editData:s,refreshDataTable:a})=>t.jsx(P,{title:s?"Edit User Type":"Add New User Type",width:500,open:i,onCancel:l,className:"custom-modal",footer:!1,children:t.jsx(L,{onCancel:l,refreshDataTable:a,editData:s})})),V=()=>{const[i,l]=r.useState(!1),[s,a]=r.useState(null),[h,x]=r.useState(""),{loading:f,data:y,fetchApi:u,pagination:o,setQueryParams:n}=p("user_type",{enablePagination:!0}),{postData:d}=p("delete_user_type",{type:"submit"}),{showAlert:m}=B(),w=r.useCallback(N.debounce(e=>{n({page:1,limit:10,keyword:e})},500),[]),g=e=>{w(e.target.value)},C=(e,c)=>{n({page:e,limit:c,keyword:h})},j=[{title:"User Type",dataIndex:"title",render:e=>t.jsx(S,{to:"#",children:e}),sorter:(e,c)=>e.title.length-c.title.length,width:"90%"},{title:"Action",dataIndex:"action",width:"",align:"",render:(e,c)=>t.jsx(D,{title:"Action",icon:"true",className:"table-dropdown",items:[{label:t.jsx("p",{className:"color-blue",onClick:()=>F(c),children:"Edit"}),key:"0"},{label:t.jsx("p",{className:"color-red",onClick:_=>T(c._id),children:"Delete"}),key:"1"}]})}],F=e=>{a(e),l(!0)},T=async(e,c)=>{(await m({title:"Are you sure?",text:"Do you want to proceed with this action?",icon:"warning",background:"#f6f6f6",showCancelButton:!0,confirmButtonText:"Yes, proceed",cancelButtonText:"No"})).isConfirmed&&d("",U,e)},U=e=>{e.statusCode===200&&u()};return t.jsxs(R,{handleSearch:g,handleAddNew:()=>{a(null),l(!0)},title:"User Types",children:[t.jsx("div",{className:"detail-table mt-4 mb-4",children:t.jsx(v,{columns:j,data:y,loading:f,pagination:{current:o==null?void 0:o.currentPage,total:o==null?void 0:o.count,pageSize:o==null?void 0:o.perPage},onChange:C,showPagination:!0,rowKey:"_id",scroll:{x:1e3}})}),t.jsx(M,{isModalOpen:i,onClose:()=>l(!1),editData:s,refreshDataTable:u})]})},W=r.memo(V);export{W as default};
