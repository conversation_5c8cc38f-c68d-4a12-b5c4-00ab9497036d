import{r as l,u as U,m as V,j as e,h as xe,R as _e,a as je,n as be}from"./index-BNo7PP7q.js";import{S as ne,a as we,d as ye,I as ve,P as Ne}from"./index-C2aV_UZj.js";import{B as Se}from"./backbutton-D7SJiPK2.js";import{F as te,u as E,d as J,B as R,f as $,a as q,E as Ce,e as Fe,I as Pe}from"./rules-B0Vq_sHo.js";import{P as Ae,_ as Ee,w as Te,a as Me}from"./assigntask-De7HzV7-.js";import{S as Re}from"./index-CO_apnu6.js";import"./index-BNdc7YlR.js";import"./notification-7LrlFXRF.js";const $e=(t,d)=>{const[i,s]=l.useState([]),[g,_]=l.useState(!0);return l.useEffect(()=>{if(!(!t||!d))return _(!0),t.emit("load_conversations",{task_id:d},o=>{(o==null?void 0:o.statusCode)===200?s(o.data):s([]),_(!1)}),()=>{s([]),_(!0)}},[t,d]),{messages:i,loading:g}},Ie=({data:t})=>{const d=U(),i=window.socket,s=window.user.user,{messages:g,loading:_}=$e(i,t==null?void 0:t._id),[o,u]=l.useState(g||[]),r=l.useRef(null),j=()=>{var a;(a=r.current)==null||a.scrollIntoView({behavior:"smooth"})};l.useEffect(()=>{g&&g.length>0&&u(g)},[g]),l.useEffect(()=>(i&&i.on("message",a=>{a.statusCode===200?u(c=>{const N={_id:a.data.task_id,message:a.data.message,type:a.data.type,url:a.data.url,user:a.data.user,created_at:a.data.created_at};return c.some(m=>m._id===a._id)?c:[...c,N]}):(V.error("This task has been deleted, you cannot send messages now."),d("/assign-task"))}),()=>{i==null||i.off("message")}),[i]),l.useEffect(()=>{j()},[o]);const h=a=>{const c=a.message;return a.type==="image"&&a.url?e.jsx("img",{src:a.url,alt:"attachment",className:"message-img",style:{maxWidth:"100%",maxHeight:"200px",borderRadius:"8px"}}):a.type==="text"?e.jsx("span",{className:"message-text",children:c}):null},f=a=>{var m,v;const c=((m=a.user)==null?void 0:m._id)===(s==null?void 0:s._id),N=xe(a.created_at).format("D/M/YY h:mm A"),F=`${a._id}-${a.created_at}`;return e.jsxs("div",{className:`message-wrapper ${c?"own":"other"}`,children:[!c&&e.jsx("div",{className:"avatar",children:window.helper.getInitials((v=a.user)==null?void 0:v.name)}),e.jsxs("div",{className:`message-bubble ${c?"own":"other"}`,children:[h(a),e.jsx("div",{className:"message-time",children:N})]})]},F)},w=[...o].sort((a,c)=>new Date(a.created_at)-new Date(c.created_at));return e.jsx("div",{className:"chat-container",children:_?e.jsx("div",{className:"skeleton-wrapper",children:Array.from({length:4}).map((a,c)=>e.jsx(ne,{avatar:!0,paragraph:{rows:1},active:!0},c))}):e.jsxs(e.Fragment,{children:[w.map(f),e.jsx("div",{ref:r})]})})},Be=l.memo(Ie),Ye=({isOpen:t,onClose:d,taskData:i,onSuccess:s,fetchApi:g})=>{var ee,se;const _=(se=(ee=window.user)==null?void 0:ee.user)==null?void 0:se._id,[o]=te.useForm(),[u,r]=l.useState(null),[j,h]=l.useState(null),[f,w]=l.useState(null),[a,c]=l.useState([]),[N,F]=l.useState(null),[m,v]=l.useState(null),[x,S]=l.useState(!1),[k,O]=l.useState([]),W=l.useRef([]),{loading:ae,postData:le}=E("update_task",{type:"submit"}),{loading:G,data:I}=E("project",{enablePagination:!0,defaultQueryParams:{page:1,limit:1e3}}),z={type:"delay"},{loading:oe,data:T,fetchApi:Q}=E("directories",z),{loading:re,data:M,fetchApi:X}=E("directories",z),{loading:Z,data:P,fetchApi:H}=E("directories",z);l.useEffect(()=>{var n,p,y,A,B,K,D;if(i){const b=((p=(n=i.project.members)==null?void 0:n.filter(C=>C._id!==_))==null?void 0:p.map(C=>({value:C._id,label:C.name})))||[];O(b);const Y=(A=(y=i.assignees)==null?void 0:y.filter(C=>C._id._id!==_))==null?void 0:A.map(C=>({value:C._id._id,label:C._id.name})),he=(Y==null?void 0:Y.map(C=>C.value))||[];if(W.current=he,o.setFieldsValue({title:i.title,project_id:(B=i.project)==null?void 0:B._id,assignees:Y,description:i.description,start_at:i.start_at?J(i.start_at):null,end_at:i.end_at?J(i.end_at):null}),r((K=i.project)==null?void 0:K._id),i.root_directory&&F(i.root_directory),i.sub_directory&&v(i.sub_directory),(D=i==null?void 0:i.directories)!=null&&D.length){const C=i.directories.map(fe=>fe._id._id);c(C)}else c([])}},[i,o,_]),l.useEffect(()=>{u&&(Q(`/?project_id=${u}&page=1&limit=1000`),N||(h(null),L("root_directory",null,{sub_directory:null}),S(!1)))},[u,N,Q]),l.useEffect(()=>{u&&j&&(X(`/?project_id=${u}&parent_id=${j}&page=1&limit=1000`),m||(L("sub_directory",null),S(!1)))},[j,u,m,X]),l.useEffect(()=>{if(T!=null&&T.length&&N){const n=T.find(p=>p._id===N);n&&(h(n._id),o.setFieldsValue({root_directory:n._id}))}},[T,N,o]),l.useEffect(()=>{if(M!=null&&M.length&&m){const n=M.find(p=>p._id===m);n&&(w(n._id),o.setFieldsValue({sub_directory:n._id}),!x&&m&&u&&(H(`/?project_id=${u}&parent_id=${m}&page=1&limit=1000`),S(!0)))}},[M,m,o,u,x,H]);const L=(n,p,y={})=>{const A={[n]:p,...y};o.setFieldsValue(A)},ce=n=>{r(n),h(null),L("root_directory",null,{sub_directory:null}),F(null),v(null),c([]),S(!1),o.setFieldsValue({assignees:[]});const p=I==null?void 0:I.find(y=>y._id===n);p!=null&&p.members?O(p.members.filter(y=>y._id!==_).map(y=>({value:y._id,label:y.name}))):O([])},de=n=>{h(n),L("sub_directory",null),v(null),c([]),S(!1)},ue=n=>{var p;w(n),n&&(!(P!=null&&P.length)||((p=P[0])==null?void 0:p.parent_id)!==n)&&!Z&&u&&(H(`/?project_id=${u}&parent_id=${n}&page=1&limit=1000`),S(!0))},me=n=>p=>{c(y=>p.target.checked?[...y,n]:y.filter(A=>A!==n))},pe=async n=>{var D;const p=new FormData;["start_at","end_at"].forEach(b=>{n[b]&&p.append(b,J(n[b]).format("YYYY-MM-DD"))});const y=((D=n.assignees)==null?void 0:D.map(b=>typeof b=="object"?b.value:b).filter(Boolean))||[],A=W.current.includes(_)?[...y,_]:y,B=W.current||[];JSON.stringify(A.sort())!==JSON.stringify(B.sort())&&(A.forEach(b=>{B.includes(b)||p.append("assignees[]",b)}),B.forEach(b=>{A.includes(b)||p.append("_assignees[]",b)})),n.root_directory&&p.append("root_directory",n.root_directory),n.sub_directory&&p.append("sub_directory",n.sub_directory),a.length?a.forEach(b=>{p.append("directories[]",b)}):p.append("directories[]",""),Object.entries(n).forEach(([b,Y])=>{["start_at","end_at","assignees","root_directory","sub_directory"].includes(b)||p.append(b,Y)}),le(p,b=>{b.statusCode===200&&(d(),g(),s&&s())},i._id)},ge=()=>f?e.jsxs("div",{className:"col-12",children:[e.jsx("label",{className:"color-black font-600 mt-3 mb-3",children:"Select Drawing PDF"}),Z?e.jsx("div",{style:{display:"flex",flexWrap:"wrap"},children:[...Array(5)].map((n,p)=>e.jsx("div",{style:{margin:"10px"},children:e.jsx(ne.Image,{active:!0,style:{width:100,height:100}})},p))}):P!=null&&P.length?e.jsx("div",{style:{display:"flex",flexWrap:"wrap"},children:P==null?void 0:P.map(n=>e.jsx(Ae,{pdfUrl:n.file,checked:Array.isArray(a)&&a.includes(n._id),onChange:me(n._id)},n._id))}):e.jsx(Ce,{description:"No drawings found"})]}):null;return e.jsxs(te,{form:o,onFinish:pe,layout:"vertical",className:"row",children:[e.jsxs("div",{className:"row",children:[e.jsx("div",{className:"col-12 col-md-6",children:e.jsx(R,{name:"title",label:"Title",rules:$.title})}),e.jsx("div",{className:"col-12 col-md-6",children:e.jsx(R,{name:"project_id",type:"select",label:"Project",options:I==null?void 0:I.map(n=>({value:n._id,label:n.title})),onChange:ce,loading:G,rules:$.project_id,showSearch:!0,disabled:!0})}),e.jsx("div",{className:"col-12 col-md-6",children:e.jsx(R,{name:"root_directory",type:"select",label:"Select Folder",options:T==null?void 0:T.map(n=>({value:n._id,label:n.title})),rules:$.root_directory,loading:oe,onChange:de,disabled:!u})}),e.jsx("div",{className:"col-12 col-md-6",children:e.jsx(R,{name:"sub_directory",type:"select",label:"Select Sub Folder",options:M==null?void 0:M.map(n=>({value:n._id,label:n.title})),rules:$.sub_directory,loading:re,disabled:!j,onChange:ue})}),e.jsx("div",{className:"col-12 col-md-6",children:e.jsx(R,{name:"assignees",type:"select",label:"Assignee",options:k,mode:"multiple",loading:G,rules:$.assignees,disabled:!u})}),e.jsx("div",{className:"col-12 col-md-6",children:e.jsxs("div",{className:"row",children:[e.jsx("div",{className:"col-12 col-md-6",children:e.jsx(R,{name:"start_at",type:"datepiker",label:"Start Date",rules:$.start_at,disablePastDates:!0,format:"YYYY-MM-DD"})}),e.jsx("div",{className:"col-12 col-md-6",children:e.jsx(R,{name:"end_at",type:"datepiker",label:"End Date",rules:$.end_at,disablePastDates:!0,format:"YYYY-MM-DD"})})]})}),e.jsx("div",{className:"col-12",children:e.jsx(R,{name:"description",type:"textarea",label:"Description",rows:"5",rules:$.description})}),ge()]}),e.jsx("div",{className:"col-12 text-end mt-4",children:e.jsx(q,{title:"Update",className:"add-new-btn",loading:ae,htmlType:"submit"})})]})},ke=l.memo(Ye),De=({data:t,fetchApi:d})=>{var f,w,a;const i=U(),s=(f=window.user)==null?void 0:f.user,{data:g}=E("get_profile",{type:"mount",slug:`/${s._id}`,enablePagination:!1}),[_,o]=l.useState(!1),{postData:u}=E("delete_task",{type:"submit"}),{showAlert:r}=Fe(),j=async(c,N)=>{(await r({title:"Are you sure?",text:"Do you want to proceed with this action?",icon:"warning",background:"#f6f6f6",showCancelButton:!0,confirmButtonText:"Yes, proceed",cancelButtonText:"No"})).isConfirmed&&u("",h,c)},h=c=>{c.statusCode===200&&i("/assign-task")};return e.jsxs(_e.Fragment,{children:[e.jsxs("div",{className:"chat-header d-flex justify-content-between",children:[e.jsxs("div",{className:"d-flex align-items-center",children:[e.jsx("div",{className:"assign-user-avatar "+((t==null?void 0:t.status)==="in-process"?"avatar-process":(t==null?void 0:t.status)==="completed"?"avatar-complete":(t==null?void 0:t.status)==="verified"?"avatar-verified":""),children:e.jsx("p",{children:window.helper.getInitials(t==null?void 0:t.title)})}),e.jsxs("div",{children:[e.jsxs("p",{className:"font-12 color-light",children:["ID: ",t==null?void 0:t.short_id," | ",(w=t==null?void 0:t.project)==null?void 0:w.title]}),e.jsx("p",{className:"font-14 color-black",children:t==null?void 0:t.title})]})]}),((g==null?void 0:g.role)==="company"||((a=g==null?void 0:g.policies)==null?void 0:a.some(c=>c.module==="task"&&c.can_create)))&&e.jsxs("div",{className:"d-flex align-items-center",children:[e.jsx("img",{className:"me-4 cursor-pointer",src:"/admin/assets/img/chat-delete-icon.png",alt:"Delete",onClick:c=>j(t._id)}),e.jsx("img",{className:"cursor-pointer",src:"/admin/assets/img/chat-edit-icon.png",alt:"Edit",onClick:c=>o(!0)})]})]}),e.jsx(we,{width:900,title:"Edit Task",onCancel:c=>o(!1),open:_,className:"custom-modal",footer:!1,children:e.jsx(ke,{onClose:c=>o(!1),taskData:t,fetchApi:d})})]})},Le=(t,d,i)=>l.useCallback(({message:g,type:_="text",url:o=""})=>{if(!t||!d||!i)return;const u={room_id:d,message:g,task_id:i,type:_,url:o};t.emit("message",u,r=>{console.log("✅ Server response:",r)})},[t,d,i]),{TextArea:Ve}=Pe,Ue=({taskId:t})=>{const d=l.useRef(),{loading:i,postData:s}=E("upload_media",{type:"submit",skipNotification:!0}),g=window.socket,_=`task_${t}`,o=Le(g,_,t),[u,r]=l.useState(""),[j,h]=l.useState(null),[f,w]=l.useState(null),a=async()=>{if(u.trim()){const x=u.trim();o({message:x,type:"text"}),r("")}if(j){const x=new FormData;x.append("file",j),x.append("type","image"),s(x,F)}},c=()=>{var x;(x=d.current)==null||x.click()},N=x=>{const S=x.target.files[0];if(S)if(S.type.startsWith("image/")){h(S);const k=new FileReader;k.onloadend=()=>w(k.result),k.readAsDataURL(S)}else alert("Only image files are accepted");x.target.value=""},F=x=>{var S;(x==null?void 0:x.statusCode)===200&&((S=x.data)!=null&&S.url)&&o({type:"image",url:x.data.url,message:""}),h(null),w(null)},m=x=>{if(x.key==="Enter"){if(x.shiftKey)return;x.preventDefault(),a()}},v=()=>{h(null),w(null)};return e.jsxs("div",{className:"chat-footer d-flex flex-column w-100",children:[f&&e.jsxs("div",{className:"preview-box d-flex align-items-center justify-content-center mb-2 p-2",style:{position:"relative",width:200,height:200},children:[e.jsx("button",{onClick:v,style:{position:"absolute",top:0,right:0,background:"#fff",border:"1px solid #ccc",borderRadius:"50%",width:20,height:20,fontSize:12,lineHeight:"16px",textAlign:"center",cursor:"pointer",zIndex:10},children:"✕"}),e.jsx("img",{src:f,alt:"preview",style:{maxHeight:"100%",maxWidth:"100%",objectFit:"contain"}})]}),e.jsx("input",{type:"file",accept:"image/*",ref:d,style:{display:"none"},onChange:N}),e.jsxs("div",{className:"d-flex align-items-center w-100",children:[e.jsxs("div",{style:{position:"relative",width:"100%"},children:[e.jsx(Ve,{placeholder:j&&!f?j.name:"Enter message here....",value:j?"":u,onChange:x=>r(x.target.value),onKeyDown:m,disabled:i,autoSize:{minRows:3,maxRows:3},style:{paddingRight:"30px"}}),e.jsx("img",{src:"/admin/assets/img/input-icon.png",alt:"input icon",style:{position:"absolute",right:"8px",top:"50%",transform:"translateY(-50%)",cursor:"pointer"},onClick:c})]}),e.jsx(q,{title:i?"Sending...":"Send",className:"add-new-btn ms-2",onClick:a,disabled:i})]})]})},Oe=l.memo(Ue),We=({data:t,fetchApi:d})=>e.jsxs("div",{className:"chat-box",children:[e.jsx(De,{data:t,fetchApi:d}),e.jsx("div",{className:"chat-body",children:e.jsx(Be,{data:t})}),e.jsx(Oe,{taskId:t==null?void 0:t._id})]}),ze=l.memo(We);Ee.workerSrc=Te;const He=({pdfUrl:t})=>{const d=l.useRef(null),[i,s]=l.useState(""),[g,_]=l.useState(!1);return l.useEffect(()=>{(async()=>{const r=await(await Me(t).promise).getPage(1),j=r.getViewport({scale:.3}),h=d.current,f=h.getContext("2d");h.width=j.width,h.height=j.height,await r.render({canvasContext:f,viewport:j}).promise,s(h.toDataURL())})()},[t]),e.jsxs("div",{children:[e.jsx("canvas",{ref:d,style:{display:"none"}}),i&&e.jsx(ye,{preview:{visible:g,src:i,alt:"PDF Page 1",onVisibleChange:o=>_(o)},src:i,alt:"PDF Page 1"})]})},Ke=l.memo(He),ie=({direction:t,onClick:d})=>e.jsx("button",{className:`custom-arrow-btn custom-arrow-${t}`,onClick:d,children:t==="prev"?"<":">"}),Je=({children:t,attributes_img:d,id:i,btn_title:s,btn_className:g,postData:_,fetchApi:o,status:u,directories:r,pdfTitle:j})=>{const h=U(),[f,w]=l.useState(0),a=()=>{switch(u){case"pending":return"in-process";case"in-process":return"completed";default:return null}},c={dots:!1,arrows:!0,infinite:!1,speed:500,slidesToShow:1,slidesToScroll:1,arrows:!0,adaptiveHeight:!0,beforeChange:(m,v)=>w(v),prevArrow:(r==null?void 0:r.length)>1&&f>0&&e.jsx(ie,{direction:"prev"}),nextArrow:(r==null?void 0:r.length)>1&&f<r.length-1&&e.jsx(ie,{direction:"next"})},N=async()=>{const m=a();if(!m)return;const v=new FormData;v.append("status",m),_(v,F,i)},F=m=>{(m==null?void 0:m.statusCode)===200&&o(),(m==null?void 0:m.code)==404&&(V.error("This content is no longer available."),o(),h("/assign-task"))};return e.jsxs("div",{className:"task-attributes",children:[e.jsx("div",{className:"attributes-header",children:e.jsx("p",{children:"Task Attributes"})}),e.jsxs("div",{className:"attributes-body",children:[(r==null?void 0:r.length)>0&&e.jsx("div",{className:"attributes-slider",children:e.jsx(Re,{...c,children:r.map((m,v)=>e.jsxs("div",{className:"slider-item",children:[e.jsx("div",{className:"attributes-img text-center",children:e.jsx(Ke,{pdfUrl:m._id.file})}),e.jsx("p",{className:"font-16 color-light text-center",children:m._id.title}),e.jsx("div",{className:"text-center mt-3",children:e.jsx("a",{href:m._id.file,title:"View PDF",className:"view-pdf-btn text-white",target:"_blank",children:"View Pdf"})})]},m._id._id||v))})}),t,a()&&e.jsx("div",{className:"text-center mt-3",children:e.jsx(q,{title:s,onClick:N,className:`attributes-btn ${g}`})})]})]})},qe=l.memo(Je),Ge=({iconSrc:t,label:d,value:i})=>e.jsxs("div",{className:"attributes-items d-flex align-items-center justify-content-between",children:[e.jsxs("div",{className:"d-flex align-items-center",children:[e.jsx("img",{src:t,alt:""}),e.jsx("p",{className:"font-14 color-light ms-2",children:d})]}),e.jsx("p",{className:"color-black ms-5",style:{wordBreak:"break-all"},children:i})]}),Qe=()=>{var j;const{id:t}=je(),d=U(),{loading:i,data:s,fetchApi:g}=E("task",{type:"mount",slug:`/${t}`}),{postData:_}=E("update_task",{type:"submit"});l.useEffect(()=>{i||(async()=>{var f,w;try{await g()==="Record not found"&&(V.error("This content is no longer available."),d("/assign-task"))}catch(a){console.log("error",a),((w=(f=a==null?void 0:a.response)==null?void 0:f.data)==null?void 0:w.message)==="Record not found"&&(V.error("This content is no longer available."),d("/assign-task"))}})()},[t,i,d,g]);const o=l.useMemo(()=>{var h,f;return[{iconSrc:"/admin/assets/img/status-icon.png",label:"Status",value:window.helper.capitalizeFirstLetter(s==null?void 0:s.status)},{iconSrc:"/admin/assets/img/assignee-icon.png",label:"Assignees",value:((h=s==null?void 0:s.assignees)==null?void 0:h.map(w=>w._id.name).join(", "))||"-"},{iconSrc:"/admin/assets/img/date-icon.png",label:"Start Date",value:window.helper.formatDate(s==null?void 0:s.start_at)},{iconSrc:"/admin/assets/img/date-icon.png",label:"End Date",value:window.helper.formatDate(s==null?void 0:s.end_at)},{iconSrc:"/admin/assets/img/category-img.png",label:"Description",value:(s==null?void 0:s.description)||"-"},{iconSrc:"/admin/assets/img/location-icon.png",label:"Location",value:((f=s==null?void 0:s.project)==null?void 0:f.address)||"-"}]},[s]),u=l.useMemo(()=>{var w;const h={pending:{title:"Start",className:"btn-primary"},in_process:{title:"Complete",className:"btn-warning"},completed:{title:"Complete",className:"btn-success"}},f=(w=s==null?void 0:s.status)==null?void 0:w.replace("-","_");return f==="verified"?null:h[f]||h.pending},[s==null?void 0:s.status]);if(i)return e.jsx(be,{});if(!s)return null;const r=(j=s==null?void 0:s.directories[0])==null?void 0:j._id;return e.jsxs(ve,{children:[e.jsx(Ne,{title:e.jsx(Se,{})}),s&&e.jsxs("div",{className:"row mb-5",children:[e.jsx("div",{className:"col-12 col-md-6 col-lg-7 col-xl-8 mt-4",children:e.jsx(ze,{data:s,fetchApi:g})}),e.jsx("div",{className:"col-12 col-md-6 col-lg-5 col-xl-4 mt-4",children:e.jsx(qe,{attributes_img:r==null?void 0:r.file,id:s==null?void 0:s._id,btn_title:u.title,btn_className:u.className,postData:_,status:s==null?void 0:s.status,fetchApi:g,pdfTitle:r==null?void 0:r.title,directories:s==null?void 0:s.directories,children:o.map((h,f)=>e.jsx(Ge,{...h},f))})})]})]})},ls=l.memo(Qe);export{ls as default};
