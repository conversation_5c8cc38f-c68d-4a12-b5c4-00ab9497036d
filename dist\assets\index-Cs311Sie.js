import{r,B as N,R as Dt,w as at,d as J,ag as Wt,v as De,J as V,H as Y,f as de,W as ae,A as Ht,x as nt,ah as Gt,z as Re,V as Xt,X as Kt,Z as Ft,a1 as g,$ as bt,a6 as Ut,ad as ht,a4 as Vt,a9 as Yt,ai as qt,j as C}from"./index-BNo7PP7q.js";import{E as Qt,j as Zt,k as Jt,l as ea,m as ta,I as aa,P as na}from"./index-C2aV_UZj.js";import{j as ke,O as ra,n as rt,P as ia,t as oa,Q as it,w as la,a as ca}from"./rules-B0Vq_sHo.js";import"./index-BNdc7YlR.js";import"./notification-7LrlFXRF.js";const Ie=r.createContext(null);var sa=function(t){var a=t.activeTabOffset,n=t.horizontal,i=t.rtl,l=t.indicator,c=l===void 0?{}:l,o=c.size,s=c.align,d=s===void 0?"center":s,v=r.useState(),f=N(v,2),x=f[0],R=f[1],B=r.useRef(),P=Dt.useCallback(function(m){return typeof o=="function"?o(m):typeof o=="number"?o:m},[o]);function z(){at.cancel(B.current)}return r.useEffect(function(){var m={};if(a)if(n){m.width=P(a.width);var u=i?"right":"left";d==="start"&&(m[u]=a[u]),d==="center"&&(m[u]=a[u]+a.width/2,m.transform=i?"translateX(50%)":"translateX(-50%)"),d==="end"&&(m[u]=a[u]+a.width,m.transform="translateX(-100%)")}else m.height=P(a.height),d==="start"&&(m.top=a.top),d==="center"&&(m.top=a.top+a.height/2,m.transform="translateY(-50%)"),d==="end"&&(m.top=a.top+a.height,m.transform="translateY(-100%)");return z(),B.current=at(function(){R(m)}),z},[a,n,i,d,P]),{style:x}},ot={width:0,height:0,left:0,top:0};function da(e,t,a){return r.useMemo(function(){for(var n,i=new Map,l=t.get((n=e[0])===null||n===void 0?void 0:n.key)||ot,c=l.left+l.width,o=0;o<e.length;o+=1){var s=e[o].key,d=t.get(s);if(!d){var v;d=t.get((v=e[o-1])===null||v===void 0?void 0:v.key)||ot}var f=i.get(s)||J({},d);f.right=c-f.left-f.width,i.set(s,f)}return i},[e.map(function(n){return n.key}).join("_"),t,a])}function lt(e,t){var a=r.useRef(e),n=r.useState({}),i=N(n,2),l=i[1];function c(o){var s=typeof o=="function"?o(a.current):o;s!==a.current&&t(s,a.current),a.current=s,l({})}return[a.current,c]}var ua=.1,ct=.01,Te=20,st=Math.pow(.995,Te);function va(e,t){var a=r.useState(),n=N(a,2),i=n[0],l=n[1],c=r.useState(0),o=N(c,2),s=o[0],d=o[1],v=r.useState(0),f=N(v,2),x=f[0],R=f[1],B=r.useState(),P=N(B,2),z=P[0],m=P[1],u=r.useRef();function T(p){var I=p.touches[0],$=I.screenX,_=I.screenY;l({x:$,y:_}),window.clearInterval(u.current)}function M(p){if(i){p.preventDefault();var I=p.touches[0],$=I.screenX,_=I.screenY;l({x:$,y:_});var b=$-i.x,w=_-i.y;t(b,w);var q=Date.now();d(q),R(q-s),m({x:b,y:w})}}function O(){if(i&&(l(null),m(null),z)){var p=z.x/x,I=z.y/x,$=Math.abs(p),_=Math.abs(I);if(Math.max($,_)<ua)return;var b=p,w=I;u.current=window.setInterval(function(){if(Math.abs(b)<ct&&Math.abs(w)<ct){window.clearInterval(u.current);return}b*=st,w*=st,t(b*Te,w*Te)},Te)}}var W=r.useRef();function k(p){var I=p.deltaX,$=p.deltaY,_=0,b=Math.abs(I),w=Math.abs($);b===w?_=W.current==="x"?I:$:b>w?(_=I,W.current="x"):(_=$,W.current="y"),t(-_,-_)&&p.preventDefault()}var y=r.useRef(null);y.current={onTouchStart:T,onTouchMove:M,onTouchEnd:O,onWheel:k},r.useEffect(function(){function p(b){y.current.onTouchStart(b)}function I(b){y.current.onTouchMove(b)}function $(b){y.current.onTouchEnd(b)}function _(b){y.current.onWheel(b)}return document.addEventListener("touchmove",I,{passive:!1}),document.addEventListener("touchend",$,{passive:!0}),e.current.addEventListener("touchstart",p,{passive:!0}),e.current.addEventListener("wheel",_,{passive:!1}),function(){document.removeEventListener("touchmove",I),document.removeEventListener("touchend",$)}},[])}function gt(e){var t=r.useState(0),a=N(t,2),n=a[0],i=a[1],l=r.useRef(0),c=r.useRef();return c.current=e,Wt(function(){var o;(o=c.current)===null||o===void 0||o.call(c)},[n]),function(){l.current===n&&(l.current+=1,i(l.current))}}function fa(e){var t=r.useRef([]),a=r.useState({}),n=N(a,2),i=n[1],l=r.useRef(typeof e=="function"?e():e),c=gt(function(){var s=l.current;t.current.forEach(function(d){s=d(s)}),t.current=[],l.current=s,i({})});function o(s){t.current.push(s),c()}return[l.current,o]}var dt={width:0,height:0,left:0,top:0,right:0};function ma(e,t,a,n,i,l,c){var o=c.tabs,s=c.tabPosition,d=c.rtl,v,f,x;return["top","bottom"].includes(s)?(v="width",f=d?"right":"left",x=Math.abs(a)):(v="height",f="top",x=-a),r.useMemo(function(){if(!o.length)return[0,0];for(var R=o.length,B=R,P=0;P<R;P+=1){var z=e.get(o[P].key)||dt;if(z[f]+z[v]>x+t){B=P-1;break}}for(var m=0,u=R-1;u>=0;u-=1){var T=e.get(o[u].key)||dt;if(T[f]<x){m=u+1;break}}return m>=B?[0,0]:[m,B]},[e,t,n,i,l,x,s,o.map(function(R){return R.key}).join("_"),d])}function ut(e){var t;return e instanceof Map?(t={},e.forEach(function(a,n){t[n]=a})):t=e,JSON.stringify(t)}var ba="TABS_DQ";function pt(e){return String(e).replace(/"/g,ba)}function $t(e,t,a,n){return!(!a||n||e===!1||e===void 0&&(t===!1||t===null))}var St=r.forwardRef(function(e,t){var a=e.prefixCls,n=e.editable,i=e.locale,l=e.style;return!n||n.showAdd===!1?null:r.createElement("button",{ref:t,type:"button",className:"".concat(a,"-nav-add"),style:l,"aria-label":(i==null?void 0:i.addAriaLabel)||"Add tab",onClick:function(o){n.onEdit("add",{event:o})}},n.addIcon||"+")}),vt=r.forwardRef(function(e,t){var a=e.position,n=e.prefixCls,i=e.extra;if(!i)return null;var l,c={};return De(i)==="object"&&!r.isValidElement(i)?c=i:c.right=i,a==="right"&&(l=c.right),a==="left"&&(l=c.left),l?r.createElement("div",{className:"".concat(n,"-extra-content"),ref:t},l):null}),ha=r.forwardRef(function(e,t){var a=e.prefixCls,n=e.id,i=e.tabs,l=e.locale,c=e.mobile,o=e.more,s=o===void 0?{}:o,d=e.style,v=e.className,f=e.editable,x=e.tabBarGutter,R=e.rtl,B=e.removeAriaLabel,P=e.onTabClick,z=e.getPopupContainer,m=e.popupClassName,u=r.useState(!1),T=N(u,2),M=T[0],O=T[1],W=r.useState(null),k=N(W,2),y=k[0],p=k[1],I=s.icon,$=I===void 0?"More":I,_="".concat(n,"-more-popup"),b="".concat(a,"-dropdown"),w=y!==null?"".concat(_,"-").concat(y):null,q=l==null?void 0:l.dropdownAriaLabel;function ie(E,A){E.preventDefault(),E.stopPropagation(),f.onEdit("remove",{key:A,event:E})}var h=r.createElement(Qt,{onClick:function(A){var G=A.key,K=A.domEvent;P(G,K),O(!1)},prefixCls:"".concat(b,"-menu"),id:_,tabIndex:-1,role:"listbox","aria-activedescendant":w,selectedKeys:[y],"aria-label":q!==void 0?q:"expanded dropdown"},i.map(function(E){var A=E.closable,G=E.disabled,K=E.closeIcon,U=E.key,ee=E.label,Z=$t(A,K,f,G);return r.createElement(Zt,{key:U,id:"".concat(_,"-").concat(U),role:"option","aria-controls":n&&"".concat(n,"-panel-").concat(U),disabled:G},r.createElement("span",null,ee),Z&&r.createElement("button",{type:"button","aria-label":B||"remove",tabIndex:0,className:"".concat(b,"-menu-item-remove"),onClick:function(le){le.stopPropagation(),ie(le,U)}},K||f.removeIcon||"×"))}));function X(E){for(var A=i.filter(function(Z){return!Z.disabled}),G=A.findIndex(function(Z){return Z.key===y})||0,K=A.length,U=0;U<K;U+=1){G=(G+E+K)%K;var ee=A[G];if(!ee.disabled){p(ee.key);return}}}function ne(E){var A=E.which;if(!M){[ae.DOWN,ae.SPACE,ae.ENTER].includes(A)&&(O(!0),E.preventDefault());return}switch(A){case ae.UP:X(-1),E.preventDefault();break;case ae.DOWN:X(1),E.preventDefault();break;case ae.ESC:O(!1);break;case ae.SPACE:case ae.ENTER:y!==null&&P(y,E);break}}r.useEffect(function(){var E=document.getElementById(w);E&&E.scrollIntoView&&E.scrollIntoView(!1)},[y]),r.useEffect(function(){M||p(null)},[M]);var H=V({},R?"marginRight":"marginLeft",x);i.length||(H.visibility="hidden",H.order=1);var F=Y(V({},"".concat(b,"-rtl"),R)),oe=c?null:r.createElement(Jt,de({prefixCls:b,overlay:h,visible:i.length?M:!1,onVisibleChange:O,overlayClassName:Y(F,m),mouseEnterDelay:.1,mouseLeaveDelay:.1,getPopupContainer:z},s),r.createElement("button",{type:"button",className:"".concat(a,"-nav-more"),style:H,tabIndex:-1,"aria-hidden":"true","aria-haspopup":"listbox","aria-controls":_,id:"".concat(n,"-more"),"aria-expanded":M,onKeyDown:ne},$));return r.createElement("div",{className:Y("".concat(a,"-nav-operations"),v),style:d,ref:t},oe,r.createElement(St,{prefixCls:a,locale:l,editable:f}))});const ga=r.memo(ha,function(e,t){return t.tabMoving});var pa=function(t){var a=t.prefixCls,n=t.id,i=t.active,l=t.tab,c=l.key,o=l.label,s=l.disabled,d=l.closeIcon,v=l.icon,f=t.closable,x=t.renderWrapper,R=t.removeAriaLabel,B=t.editable,P=t.onClick,z=t.onFocus,m=t.style,u="".concat(a,"-tab"),T=$t(f,d,B,s);function M(y){s||P(y)}function O(y){y.preventDefault(),y.stopPropagation(),B.onEdit("remove",{key:c,event:y})}var W=r.useMemo(function(){return v&&typeof o=="string"?r.createElement("span",null,o):o},[o,v]),k=r.createElement("div",{key:c,"data-node-key":pt(c),className:Y(u,V(V(V({},"".concat(u,"-with-remove"),T),"".concat(u,"-active"),i),"".concat(u,"-disabled"),s)),style:m,onClick:M},r.createElement("div",{role:"tab","aria-selected":i,id:n&&"".concat(n,"-tab-").concat(c),className:"".concat(u,"-btn"),"aria-controls":n&&"".concat(n,"-panel-").concat(c),"aria-disabled":s,tabIndex:s?null:0,onClick:function(p){p.stopPropagation(),M(p)},onKeyDown:function(p){[ae.SPACE,ae.ENTER].includes(p.which)&&(p.preventDefault(),M(p))},onFocus:z},v&&r.createElement("span",{className:"".concat(u,"-icon")},v),o&&W),T&&r.createElement("button",{type:"button","aria-label":R||"remove",tabIndex:0,className:"".concat(u,"-remove"),onClick:function(p){p.stopPropagation(),O(p)}},d||B.removeIcon||"×"));return x?x(k):k},$a=function(t,a){var n=t.offsetWidth,i=t.offsetHeight,l=t.offsetTop,c=t.offsetLeft,o=t.getBoundingClientRect(),s=o.width,d=o.height,v=o.x,f=o.y;return Math.abs(s-n)<1?[s,d,v-a.x,f-a.y]:[n,i,c,l]},he=function(t){var a=t.current||{},n=a.offsetWidth,i=n===void 0?0:n,l=a.offsetHeight,c=l===void 0?0:l;if(t.current){var o=t.current.getBoundingClientRect(),s=o.width,d=o.height;if(Math.abs(s-i)<1)return[s,d]}return[i,c]},Pe=function(t,a){return t[a?0:1]},ft=r.forwardRef(function(e,t){var a=e.className,n=e.style,i=e.id,l=e.animated,c=e.activeKey,o=e.rtl,s=e.extra,d=e.editable,v=e.locale,f=e.tabPosition,x=e.tabBarGutter,R=e.children,B=e.onTabClick,P=e.onTabScroll,z=e.indicator,m=r.useContext(Ie),u=m.prefixCls,T=m.tabs,M=r.useRef(null),O=r.useRef(null),W=r.useRef(null),k=r.useRef(null),y=r.useRef(null),p=r.useRef(null),I=r.useRef(null),$=f==="top"||f==="bottom",_=lt(0,function(j,S){$&&P&&P({direction:j>S?"left":"right"})}),b=N(_,2),w=b[0],q=b[1],ie=lt(0,function(j,S){!$&&P&&P({direction:j>S?"top":"bottom"})}),h=N(ie,2),X=h[0],ne=h[1],H=r.useState([0,0]),F=N(H,2),oe=F[0],E=F[1],A=r.useState([0,0]),G=N(A,2),K=G[0],U=G[1],ee=r.useState([0,0]),Z=N(ee,2),me=Z[0],le=Z[1],ue=r.useState([0,0]),be=N(ue,2),L=be[0],re=be[1],pe=fa(new Map),We=N(pe,2),Ct=We[0],wt=We[1],xe=da(T,Ct,K[0]),Le=Pe(oe,$),$e=Pe(K,$),Ne=Pe(me,$),He=Pe(L,$),Ge=Le<$e+Ne,te=Ge?Le-He:Le-Ne,Et="".concat(u,"-nav-operations-hidden"),ce=0,ve=0;$&&o?(ce=0,ve=Math.max(0,$e-te)):(ce=Math.min(0,te-$e),ve=0);function ze(j){return j<ce?ce:j>ve?ve:j}var Me=r.useRef(null),Pt=r.useState(),Xe=N(Pt,2),ye=Xe[0],Ke=Xe[1];function Be(){Ke(Date.now())}function je(){Me.current&&clearTimeout(Me.current)}va(k,function(j,S){function D(Q,fe){Q(function(se){var we=ze(se+fe);return we})}return Ge?($?D(q,j):D(ne,S),je(),Be(),!0):!1}),r.useEffect(function(){return je(),ye&&(Me.current=setTimeout(function(){Ke(0)},100)),je},[ye]);var Tt=ma(xe,te,$?w:X,$e,Ne,He,J(J({},e),{},{tabs:T})),Fe=N(Tt,2),Rt=Fe[0],It=Fe[1],Ue=Ht(function(){var j=arguments.length>0&&arguments[0]!==void 0?arguments[0]:c,S=xe.get(j)||{width:0,height:0,left:0,right:0,top:0};if($){var D=w;o?S.right<w?D=S.right:S.right+S.width>w+te&&(D=S.right+S.width-te):S.left<-w?D=-S.left:S.left+S.width>-w+te&&(D=-(S.left+S.width-te)),ne(0),q(ze(D))}else{var Q=X;S.top<-X?Q=-S.top:S.top+S.height>-X+te&&(Q=-(S.top+S.height-te)),q(0),ne(ze(Q))}}),_e={};f==="top"||f==="bottom"?_e[o?"marginRight":"marginLeft"]=x:_e.marginTop=x;var Ve=T.map(function(j,S){var D=j.key;return r.createElement(pa,{id:i,prefixCls:u,key:D,tab:j,style:S===0?void 0:_e,closable:j.closable,editable:d,active:D===c,renderWrapper:R,removeAriaLabel:v==null?void 0:v.removeAriaLabel,onClick:function(fe){B(D,fe)},onFocus:function(){Ue(D),Be(),k.current&&(o||(k.current.scrollLeft=0),k.current.scrollTop=0)}})}),Ye=function(){return wt(function(){var S,D=new Map,Q=(S=y.current)===null||S===void 0?void 0:S.getBoundingClientRect();return T.forEach(function(fe){var se,we=fe.key,tt=(se=y.current)===null||se===void 0?void 0:se.querySelector('[data-node-key="'.concat(pt(we),'"]'));if(tt){var Bt=$a(tt,Q),Ee=N(Bt,4),jt=Ee[0],At=Ee[1],Ot=Ee[2],kt=Ee[3];D.set(we,{width:jt,height:At,left:Ot,top:kt})}}),D})};r.useEffect(function(){Ye()},[T.map(function(j){return j.key}).join("_")]);var Ce=gt(function(){var j=he(M),S=he(O),D=he(W);E([j[0]-S[0]-D[0],j[1]-S[1]-D[1]]);var Q=he(I);le(Q);var fe=he(p);re(fe);var se=he(y);U([se[0]-Q[0],se[1]-Q[1]]),Ye()}),Lt=T.slice(0,Rt),Nt=T.slice(It+1),qe=[].concat(nt(Lt),nt(Nt)),Qe=xe.get(c),zt=sa({activeTabOffset:Qe,horizontal:$,indicator:z,rtl:o}),Mt=zt.style;r.useEffect(function(){Ue()},[c,ce,ve,ut(Qe),ut(xe),$]),r.useEffect(function(){Ce()},[o]);var Ze=!!qe.length,Se="".concat(u,"-nav-wrap"),Ae,Oe,Je,et;return $?o?(Oe=w>0,Ae=w!==ve):(Ae=w<0,Oe=w!==ce):(Je=X<0,et=X!==ce),r.createElement(ke,{onResize:Ce},r.createElement("div",{ref:Gt(t,M),role:"tablist",className:Y("".concat(u,"-nav"),a),style:n,onKeyDown:function(){Be()}},r.createElement(vt,{ref:O,position:"left",extra:s,prefixCls:u}),r.createElement(ke,{onResize:Ce},r.createElement("div",{className:Y(Se,V(V(V(V({},"".concat(Se,"-ping-left"),Ae),"".concat(Se,"-ping-right"),Oe),"".concat(Se,"-ping-top"),Je),"".concat(Se,"-ping-bottom"),et)),ref:k},r.createElement(ke,{onResize:Ce},r.createElement("div",{ref:y,className:"".concat(u,"-nav-list"),style:{transform:"translate(".concat(w,"px, ").concat(X,"px)"),transition:ye?"none":void 0}},Ve,r.createElement(St,{ref:I,prefixCls:u,locale:v,editable:d,style:J(J({},Ve.length===0?void 0:_e),{},{visibility:Ze?"hidden":null})}),r.createElement("div",{className:Y("".concat(u,"-ink-bar"),V({},"".concat(u,"-ink-bar-animated"),l.inkBar)),style:Mt}))))),r.createElement(ga,de({},e,{removeAriaLabel:v==null?void 0:v.removeAriaLabel,ref:p,prefixCls:u,tabs:qe,className:!Ze&&Et,tabMoving:!!ye})),r.createElement(vt,{ref:W,position:"right",extra:s,prefixCls:u})))}),xt=r.forwardRef(function(e,t){var a=e.prefixCls,n=e.className,i=e.style,l=e.id,c=e.active,o=e.tabKey,s=e.children;return r.createElement("div",{id:l&&"".concat(l,"-panel-").concat(o),role:"tabpanel",tabIndex:c?0:-1,"aria-labelledby":l&&"".concat(l,"-tab-").concat(o),"aria-hidden":!c,style:i,className:Y(a,c&&"".concat(a,"-active"),n),ref:t},s)}),Sa=["renderTabBar"],xa=["label","key"],ya=function(t){var a=t.renderTabBar,n=Re(t,Sa),i=r.useContext(Ie),l=i.tabs;if(a){var c=J(J({},n),{},{panes:l.map(function(o){var s=o.label,d=o.key,v=Re(o,xa);return r.createElement(xt,de({tab:s,key:d,tabKey:d},v))})});return a(c,ft)}return r.createElement(ft,n)},_a=["key","forceRender","style","className","destroyInactiveTabPane"],Ca=function(t){var a=t.id,n=t.activeKey,i=t.animated,l=t.tabPosition,c=t.destroyInactiveTabPane,o=r.useContext(Ie),s=o.prefixCls,d=o.tabs,v=i.tabPane,f="".concat(s,"-tabpane");return r.createElement("div",{className:Y("".concat(s,"-content-holder"))},r.createElement("div",{className:Y("".concat(s,"-content"),"".concat(s,"-content-").concat(l),V({},"".concat(s,"-content-animated"),v))},d.map(function(x){var R=x.key,B=x.forceRender,P=x.style,z=x.className,m=x.destroyInactiveTabPane,u=Re(x,_a),T=R===n;return r.createElement(Xt,de({key:R,visible:T,forceRender:B,removeOnLeave:!!(c||m),leavedClassName:"".concat(f,"-hidden")},i.tabPaneMotion),function(M,O){var W=M.style,k=M.className;return r.createElement(xt,de({},u,{prefixCls:f,id:a,tabKey:R,animated:v,active:T,style:J(J({},P),W),className:Y(z,k),ref:O}))})})))};function wa(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{inkBar:!0,tabPane:!1},t;return e===!1?t={inkBar:!1,tabPane:!1}:e===!0?t={inkBar:!0,tabPane:!1}:t=J({inkBar:!0},De(e)==="object"?e:{}),t.tabPaneMotion&&t.tabPane===void 0&&(t.tabPane=!0),!t.tabPaneMotion&&t.tabPane&&(t.tabPane=!1),t}var Ea=["id","prefixCls","className","items","direction","activeKey","defaultActiveKey","editable","animated","tabPosition","tabBarGutter","tabBarStyle","tabBarExtraContent","locale","more","destroyInactiveTabPane","renderTabBar","onChange","onTabClick","onTabScroll","getPopupContainer","popupClassName","indicator"],mt=0,Pa=r.forwardRef(function(e,t){var a=e.id,n=e.prefixCls,i=n===void 0?"rc-tabs":n,l=e.className,c=e.items,o=e.direction,s=e.activeKey,d=e.defaultActiveKey,v=e.editable,f=e.animated,x=e.tabPosition,R=x===void 0?"top":x,B=e.tabBarGutter,P=e.tabBarStyle,z=e.tabBarExtraContent,m=e.locale,u=e.more,T=e.destroyInactiveTabPane,M=e.renderTabBar,O=e.onChange,W=e.onTabClick,k=e.onTabScroll,y=e.getPopupContainer,p=e.popupClassName,I=e.indicator,$=Re(e,Ea),_=r.useMemo(function(){return(c||[]).filter(function(L){return L&&De(L)==="object"&&"key"in L})},[c]),b=o==="rtl",w=wa(f),q=r.useState(!1),ie=N(q,2),h=ie[0],X=ie[1];r.useEffect(function(){X(ra())},[]);var ne=rt(function(){var L;return(L=_[0])===null||L===void 0?void 0:L.key},{value:s,defaultValue:d}),H=N(ne,2),F=H[0],oe=H[1],E=r.useState(function(){return _.findIndex(function(L){return L.key===F})}),A=N(E,2),G=A[0],K=A[1];r.useEffect(function(){var L=_.findIndex(function(pe){return pe.key===F});if(L===-1){var re;L=Math.max(0,Math.min(G,_.length-1)),oe((re=_[L])===null||re===void 0?void 0:re.key)}K(L)},[_.map(function(L){return L.key}).join("_"),F,G]);var U=rt(null,{value:a}),ee=N(U,2),Z=ee[0],me=ee[1];r.useEffect(function(){a||(me("rc-tabs-".concat(mt)),mt+=1)},[]);function le(L,re){W==null||W(L,re);var pe=L!==F;oe(L),pe&&(O==null||O(L))}var ue={id:Z,activeKey:F,animated:w,tabPosition:R,rtl:b,mobile:h},be=J(J({},ue),{},{editable:v,locale:m,more:u,tabBarGutter:B,onTabClick:le,onTabScroll:k,extra:z,style:P,panes:null,getPopupContainer:y,popupClassName:p,indicator:I});return r.createElement(Ie.Provider,{value:{tabs:_,prefixCls:i}},r.createElement("div",de({ref:t,id:a,className:Y(i,"".concat(i,"-").concat(R),V(V(V({},"".concat(i,"-mobile"),h),"".concat(i,"-editable"),v),"".concat(i,"-rtl"),b),l)},$),r.createElement(ya,de({},be,{renderTabBar:M})),r.createElement(Ca,de({destroyInactiveTabPane:T},ue,{animated:w}))))});const Ta={motionAppear:!1,motionEnter:!0,motionLeave:!0};function Ra(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{inkBar:!0,tabPane:!1},a;return t===!1?a={inkBar:!1,tabPane:!1}:t===!0?a={inkBar:!0,tabPane:!0}:a=Object.assign({inkBar:!0},typeof t=="object"?t:{}),a.tabPane&&(a.tabPaneMotion=Object.assign(Object.assign({},Ta),{motionName:ia(e,"switch")})),a}var Ia=function(e,t){var a={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(a[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,n=Object.getOwnPropertySymbols(e);i<n.length;i++)t.indexOf(n[i])<0&&Object.prototype.propertyIsEnumerable.call(e,n[i])&&(a[n[i]]=e[n[i]]);return a};function La(e){return e.filter(t=>t)}function Na(e,t){if(e)return e;const a=oa(t).map(n=>{if(r.isValidElement(n)){const{key:i,props:l}=n,c=l||{},{tab:o}=c,s=Ia(c,["tab"]);return Object.assign(Object.assign({key:String(i)},s),{label:o})}return null});return La(a)}const za=e=>{const{componentCls:t,motionDurationSlow:a}=e;return[{[t]:{[`${t}-switch`]:{"&-appear, &-enter":{transition:"none","&-start":{opacity:0},"&-active":{opacity:1,transition:`opacity ${a}`}},"&-leave":{position:"absolute",transition:"none",inset:0,"&-start":{opacity:1},"&-active":{opacity:0,transition:`opacity ${a}`}}}}},[it(e,"slide-up"),it(e,"slide-down")]]},Ma=e=>{const{componentCls:t,tabsCardPadding:a,cardBg:n,cardGutter:i,colorBorderSecondary:l,itemSelectedColor:c}=e;return{[`${t}-card`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{margin:0,padding:a,background:n,border:`${g(e.lineWidth)} ${e.lineType} ${l}`,transition:`all ${e.motionDurationSlow} ${e.motionEaseInOut}`},[`${t}-tab-active`]:{color:c,background:e.colorBgContainer},[`${t}-ink-bar`]:{visibility:"hidden"}},[`&${t}-top, &${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab + ${t}-tab`]:{marginLeft:{_skip_check_:!0,value:g(i)}}}},[`&${t}-top`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:`${g(e.borderRadiusLG)} ${g(e.borderRadiusLG)} 0 0`},[`${t}-tab-active`]:{borderBottomColor:e.colorBgContainer}}},[`&${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:`0 0 ${g(e.borderRadiusLG)} ${g(e.borderRadiusLG)}`},[`${t}-tab-active`]:{borderTopColor:e.colorBgContainer}}},[`&${t}-left, &${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab + ${t}-tab`]:{marginTop:g(i)}}},[`&${t}-left`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`${g(e.borderRadiusLG)} 0 0 ${g(e.borderRadiusLG)}`}},[`${t}-tab-active`]:{borderRightColor:{_skip_check_:!0,value:e.colorBgContainer}}}},[`&${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`0 ${g(e.borderRadiusLG)} ${g(e.borderRadiusLG)} 0`}},[`${t}-tab-active`]:{borderLeftColor:{_skip_check_:!0,value:e.colorBgContainer}}}}}}},Ba=e=>{const{componentCls:t,itemHoverColor:a,dropdownEdgeChildVerticalPadding:n}=e;return{[`${t}-dropdown`]:Object.assign(Object.assign({},bt(e)),{position:"absolute",top:-9999,left:{_skip_check_:!0,value:-9999},zIndex:e.zIndexPopup,display:"block","&-hidden":{display:"none"},[`${t}-dropdown-menu`]:{maxHeight:e.tabsDropdownHeight,margin:0,padding:`${g(n)} 0`,overflowX:"hidden",overflowY:"auto",textAlign:{_skip_check_:!0,value:"left"},listStyleType:"none",backgroundColor:e.colorBgContainer,backgroundClip:"padding-box",borderRadius:e.borderRadiusLG,outline:"none",boxShadow:e.boxShadowSecondary,"&-item":Object.assign(Object.assign({},Ut),{display:"flex",alignItems:"center",minWidth:e.tabsDropdownWidth,margin:0,padding:`${g(e.paddingXXS)} ${g(e.paddingSM)}`,color:e.colorText,fontWeight:"normal",fontSize:e.fontSize,lineHeight:e.lineHeight,cursor:"pointer",transition:`all ${e.motionDurationSlow}`,"> span":{flex:1,whiteSpace:"nowrap"},"&-remove":{flex:"none",marginLeft:{_skip_check_:!0,value:e.marginSM},color:e.colorTextDescription,fontSize:e.fontSizeSM,background:"transparent",border:0,cursor:"pointer","&:hover":{color:a}},"&:hover":{background:e.controlItemBgHover},"&-disabled":{"&, &:hover":{color:e.colorTextDisabled,background:"transparent",cursor:"not-allowed"}}})}})}},ja=e=>{const{componentCls:t,margin:a,colorBorderSecondary:n,horizontalMargin:i,verticalItemPadding:l,verticalItemMargin:c,calc:o}=e;return{[`${t}-top, ${t}-bottom`]:{flexDirection:"column",[`> ${t}-nav, > div > ${t}-nav`]:{margin:i,"&::before":{position:"absolute",right:{_skip_check_:!0,value:0},left:{_skip_check_:!0,value:0},borderBottom:`${g(e.lineWidth)} ${e.lineType} ${n}`,content:"''"},[`${t}-ink-bar`]:{height:e.lineWidthBold,"&-animated":{transition:`width ${e.motionDurationSlow}, left ${e.motionDurationSlow},
            right ${e.motionDurationSlow}`}},[`${t}-nav-wrap`]:{"&::before, &::after":{top:0,bottom:0,width:e.controlHeight},"&::before":{left:{_skip_check_:!0,value:0},boxShadow:e.boxShadowTabsOverflowLeft},"&::after":{right:{_skip_check_:!0,value:0},boxShadow:e.boxShadowTabsOverflowRight},[`&${t}-nav-wrap-ping-left::before`]:{opacity:1},[`&${t}-nav-wrap-ping-right::after`]:{opacity:1}}}},[`${t}-top`]:{[`> ${t}-nav,
        > div > ${t}-nav`]:{"&::before":{bottom:0},[`${t}-ink-bar`]:{bottom:0}}},[`${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{order:1,marginTop:a,marginBottom:0,"&::before":{top:0},[`${t}-ink-bar`]:{top:0}},[`> ${t}-content-holder, > div > ${t}-content-holder`]:{order:0}},[`${t}-left, ${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{flexDirection:"column",minWidth:o(e.controlHeight).mul(1.25).equal(),[`${t}-tab`]:{padding:l,textAlign:"center"},[`${t}-tab + ${t}-tab`]:{margin:c},[`${t}-nav-wrap`]:{flexDirection:"column","&::before, &::after":{right:{_skip_check_:!0,value:0},left:{_skip_check_:!0,value:0},height:e.controlHeight},"&::before":{top:0,boxShadow:e.boxShadowTabsOverflowTop},"&::after":{bottom:0,boxShadow:e.boxShadowTabsOverflowBottom},[`&${t}-nav-wrap-ping-top::before`]:{opacity:1},[`&${t}-nav-wrap-ping-bottom::after`]:{opacity:1}},[`${t}-ink-bar`]:{width:e.lineWidthBold,"&-animated":{transition:`height ${e.motionDurationSlow}, top ${e.motionDurationSlow}`}},[`${t}-nav-list, ${t}-nav-operations`]:{flex:"1 0 auto",flexDirection:"column"}}},[`${t}-left`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-ink-bar`]:{right:{_skip_check_:!0,value:0}}},[`> ${t}-content-holder, > div > ${t}-content-holder`]:{marginLeft:{_skip_check_:!0,value:g(o(e.lineWidth).mul(-1).equal())},borderLeft:{_skip_check_:!0,value:`${g(e.lineWidth)} ${e.lineType} ${e.colorBorder}`},[`> ${t}-content > ${t}-tabpane`]:{paddingLeft:{_skip_check_:!0,value:e.paddingLG}}}},[`${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{order:1,[`${t}-ink-bar`]:{left:{_skip_check_:!0,value:0}}},[`> ${t}-content-holder, > div > ${t}-content-holder`]:{order:0,marginRight:{_skip_check_:!0,value:o(e.lineWidth).mul(-1).equal()},borderRight:{_skip_check_:!0,value:`${g(e.lineWidth)} ${e.lineType} ${e.colorBorder}`},[`> ${t}-content > ${t}-tabpane`]:{paddingRight:{_skip_check_:!0,value:e.paddingLG}}}}}},Aa=e=>{const{componentCls:t,cardPaddingSM:a,cardPaddingLG:n,horizontalItemPaddingSM:i,horizontalItemPaddingLG:l}=e;return{[t]:{"&-small":{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:i,fontSize:e.titleFontSizeSM}}},"&-large":{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:l,fontSize:e.titleFontSizeLG}}}},[`${t}-card`]:{[`&${t}-small`]:{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:a}},[`&${t}-bottom`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:`0 0 ${g(e.borderRadius)} ${g(e.borderRadius)}`}},[`&${t}-top`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:`${g(e.borderRadius)} ${g(e.borderRadius)} 0 0`}},[`&${t}-right`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`0 ${g(e.borderRadius)} ${g(e.borderRadius)} 0`}}},[`&${t}-left`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`${g(e.borderRadius)} 0 0 ${g(e.borderRadius)}`}}}},[`&${t}-large`]:{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:n}}}}}},Oa=e=>{const{componentCls:t,itemActiveColor:a,itemHoverColor:n,iconCls:i,tabsHorizontalItemMargin:l,horizontalItemPadding:c,itemSelectedColor:o,itemColor:s}=e,d=`${t}-tab`;return{[d]:{position:"relative",WebkitTouchCallout:"none",WebkitTapHighlightColor:"transparent",display:"inline-flex",alignItems:"center",padding:c,fontSize:e.titleFontSize,background:"transparent",border:0,outline:"none",cursor:"pointer",color:s,"&-btn, &-remove":Object.assign({"&:focus:not(:focus-visible), &:active":{color:a}},ht(e)),"&-btn":{outline:"none",transition:`all ${e.motionDurationSlow}`,[`${d}-icon:not(:last-child)`]:{marginInlineEnd:e.marginSM}},"&-remove":{flex:"none",marginRight:{_skip_check_:!0,value:e.calc(e.marginXXS).mul(-1).equal()},marginLeft:{_skip_check_:!0,value:e.marginXS},color:e.colorTextDescription,fontSize:e.fontSizeSM,background:"transparent",border:"none",outline:"none",cursor:"pointer",transition:`all ${e.motionDurationSlow}`,"&:hover":{color:e.colorTextHeading}},"&:hover":{color:n},[`&${d}-active ${d}-btn`]:{color:o,textShadow:e.tabsActiveTextShadow},[`&${d}-disabled`]:{color:e.colorTextDisabled,cursor:"not-allowed"},[`&${d}-disabled ${d}-btn, &${d}-disabled ${t}-remove`]:{"&:focus, &:active":{color:e.colorTextDisabled}},[`& ${d}-remove ${i}`]:{margin:0},[`${i}:not(:last-child)`]:{marginRight:{_skip_check_:!0,value:e.marginSM}}},[`${d} + ${d}`]:{margin:{_skip_check_:!0,value:l}}}},ka=e=>{const{componentCls:t,tabsHorizontalItemMarginRTL:a,iconCls:n,cardGutter:i,calc:l}=e;return{[`${t}-rtl`]:{direction:"rtl",[`${t}-nav`]:{[`${t}-tab`]:{margin:{_skip_check_:!0,value:a},[`${t}-tab:last-of-type`]:{marginLeft:{_skip_check_:!0,value:0}},[n]:{marginRight:{_skip_check_:!0,value:0},marginLeft:{_skip_check_:!0,value:g(e.marginSM)}},[`${t}-tab-remove`]:{marginRight:{_skip_check_:!0,value:g(e.marginXS)},marginLeft:{_skip_check_:!0,value:g(l(e.marginXXS).mul(-1).equal())},[n]:{margin:0}}}},[`&${t}-left`]:{[`> ${t}-nav`]:{order:1},[`> ${t}-content-holder`]:{order:0}},[`&${t}-right`]:{[`> ${t}-nav`]:{order:0},[`> ${t}-content-holder`]:{order:1}},[`&${t}-card${t}-top, &${t}-card${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab + ${t}-tab`]:{marginRight:{_skip_check_:!0,value:i},marginLeft:{_skip_check_:!0,value:0}}}}},[`${t}-dropdown-rtl`]:{direction:"rtl"},[`${t}-menu-item`]:{[`${t}-dropdown-rtl`]:{textAlign:{_skip_check_:!0,value:"right"}}}}},Da=e=>{const{componentCls:t,tabsCardPadding:a,cardHeight:n,cardGutter:i,itemHoverColor:l,itemActiveColor:c,colorBorderSecondary:o}=e;return{[t]:Object.assign(Object.assign(Object.assign(Object.assign({},bt(e)),{display:"flex",[`> ${t}-nav, > div > ${t}-nav`]:{position:"relative",display:"flex",flex:"none",alignItems:"center",[`${t}-nav-wrap`]:{position:"relative",display:"flex",flex:"auto",alignSelf:"stretch",overflow:"hidden",whiteSpace:"nowrap",transform:"translate(0)","&::before, &::after":{position:"absolute",zIndex:1,opacity:0,transition:`opacity ${e.motionDurationSlow}`,content:"''",pointerEvents:"none"}},[`${t}-nav-list`]:{position:"relative",display:"flex",transition:`opacity ${e.motionDurationSlow}`},[`${t}-nav-operations`]:{display:"flex",alignSelf:"stretch"},[`${t}-nav-operations-hidden`]:{position:"absolute",visibility:"hidden",pointerEvents:"none"},[`${t}-nav-more`]:{position:"relative",padding:a,background:"transparent",border:0,color:e.colorText,"&::after":{position:"absolute",right:{_skip_check_:!0,value:0},bottom:0,left:{_skip_check_:!0,value:0},height:e.calc(e.controlHeightLG).div(8).equal(),transform:"translateY(100%)",content:"''"}},[`${t}-nav-add`]:Object.assign({minWidth:n,minHeight:n,marginLeft:{_skip_check_:!0,value:i},padding:`0 ${g(e.paddingXS)}`,background:"transparent",border:`${g(e.lineWidth)} ${e.lineType} ${o}`,borderRadius:`${g(e.borderRadiusLG)} ${g(e.borderRadiusLG)} 0 0`,outline:"none",cursor:"pointer",color:e.colorText,transition:`all ${e.motionDurationSlow} ${e.motionEaseInOut}`,"&:hover":{color:l},"&:active, &:focus:not(:focus-visible)":{color:c}},ht(e))},[`${t}-extra-content`]:{flex:"none"},[`${t}-ink-bar`]:{position:"absolute",background:e.inkBarColor,pointerEvents:"none"}}),Oa(e)),{[`${t}-content`]:{position:"relative",width:"100%"},[`${t}-content-holder`]:{flex:"auto",minWidth:0,minHeight:0},[`${t}-tabpane`]:{outline:"none","&-hidden":{display:"none"}}}),[`${t}-centered`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-nav-wrap`]:{[`&:not([class*='${t}-nav-wrap-ping'])`]:{justifyContent:"center"}}}}}},Wa=e=>{const t=e.controlHeightLG;return{zIndexPopup:e.zIndexPopupBase+50,cardBg:e.colorFillAlter,cardHeight:t,cardPadding:`${(t-Math.round(e.fontSize*e.lineHeight))/2-e.lineWidth}px ${e.padding}px`,cardPaddingSM:`${e.paddingXXS*1.5}px ${e.padding}px`,cardPaddingLG:`${e.paddingXS}px ${e.padding}px ${e.paddingXXS*1.5}px`,titleFontSize:e.fontSize,titleFontSizeLG:e.fontSizeLG,titleFontSizeSM:e.fontSize,inkBarColor:e.colorPrimary,horizontalMargin:`0 0 ${e.margin}px 0`,horizontalItemGutter:32,horizontalItemMargin:"",horizontalItemMarginRTL:"",horizontalItemPadding:`${e.paddingSM}px 0`,horizontalItemPaddingSM:`${e.paddingXS}px 0`,horizontalItemPaddingLG:`${e.padding}px 0`,verticalItemPadding:`${e.paddingXS}px ${e.paddingLG}px`,verticalItemMargin:`${e.margin}px 0 0 0`,itemColor:e.colorText,itemSelectedColor:e.colorPrimary,itemHoverColor:e.colorPrimaryHover,itemActiveColor:e.colorPrimaryActive,cardGutter:e.marginXXS/2}},Ha=Kt("Tabs",e=>{const t=Ft(e,{tabsCardPadding:e.cardPadding,dropdownEdgeChildVerticalPadding:e.paddingXXS,tabsActiveTextShadow:"0 0 0.25px currentcolor",tabsDropdownHeight:200,tabsDropdownWidth:120,tabsHorizontalItemMargin:`0 0 0 ${g(e.horizontalItemGutter)}`,tabsHorizontalItemMarginRTL:`0 0 0 ${g(e.horizontalItemGutter)}`});return[Aa(t),ka(t),ja(t),Ba(t),Ma(t),Da(t),za(t)]},Wa),Ga=()=>null;var Xa=function(e,t){var a={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(a[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,n=Object.getOwnPropertySymbols(e);i<n.length;i++)t.indexOf(n[i])<0&&Object.prototype.propertyIsEnumerable.call(e,n[i])&&(a[n[i]]=e[n[i]]);return a};const yt=e=>{var t,a,n,i,l,c,o,s,d,v,f;const{type:x,className:R,rootClassName:B,size:P,onEdit:z,hideAdd:m,centered:u,addIcon:T,removeIcon:M,moreIcon:O,more:W,popupClassName:k,children:y,items:p,animated:I,style:$,indicatorSize:_,indicator:b}=e,w=Xa(e,["type","className","rootClassName","size","onEdit","hideAdd","centered","addIcon","removeIcon","moreIcon","more","popupClassName","children","items","animated","style","indicatorSize","indicator"]),{prefixCls:q}=w,{direction:ie,tabs:h,getPrefixCls:X,getPopupContainer:ne}=r.useContext(Vt),H=X("tabs",q),F=Yt(H),[oe,E,A]=Ha(H,F);let G;x==="editable-card"&&(G={onEdit:(ue,be)=>{let{key:L,event:re}=be;z==null||z(ue==="add"?re:L,ue)},removeIcon:(t=M??(h==null?void 0:h.removeIcon))!==null&&t!==void 0?t:r.createElement(qt,null),addIcon:(T??(h==null?void 0:h.addIcon))||r.createElement(ea,null),showAdd:m!==!0});const K=X(),U=la(P),ee=Na(p,y),Z=Ra(H,I),me=Object.assign(Object.assign({},h==null?void 0:h.style),$),le={align:(a=b==null?void 0:b.align)!==null&&a!==void 0?a:(n=h==null?void 0:h.indicator)===null||n===void 0?void 0:n.align,size:(o=(l=(i=b==null?void 0:b.size)!==null&&i!==void 0?i:_)!==null&&l!==void 0?l:(c=h==null?void 0:h.indicator)===null||c===void 0?void 0:c.size)!==null&&o!==void 0?o:h==null?void 0:h.indicatorSize};return oe(r.createElement(Pa,Object.assign({direction:ie,getPopupContainer:ne},w,{items:ee,className:Y({[`${H}-${U}`]:U,[`${H}-card`]:["card","editable-card"].includes(x),[`${H}-editable-card`]:x==="editable-card",[`${H}-centered`]:u},h==null?void 0:h.className,R,B,E,A,F),popupClassName:Y(k,E,A,F),style:me,editable:G,more:Object.assign({icon:(f=(v=(d=(s=h==null?void 0:h.more)===null||s===void 0?void 0:s.icon)!==null&&d!==void 0?d:h==null?void 0:h.moreIcon)!==null&&v!==void 0?v:O)!==null&&f!==void 0?f:r.createElement(ta,null),transitionName:`${K}-slide-up`},W),prefixCls:H,animated:Z,indicator:le})))};yt.TabPane=Ga;const Ka=({items:e,onChange:t,className:a})=>C.jsx(yt,{defaultActiveKey:"1",items:e,onChange:t,className:a}),_t=({title:e,price:t,time:a,detail:n,annually:i,text_1:l,btntitle:c,text_2:o,text_3:s,text_4:d,className:v,btn_className:f})=>C.jsxs("div",{className:v,children:[C.jsx("p",{className:"font-36",children:e}),C.jsxs("div",{className:"d-flex justify-content-center",children:[C.jsx("span",{className:"font-36",children:"$"}),C.jsx("h2",{className:"font-90",children:t})]}),C.jsxs("p",{children:["per user / ",a]}),C.jsx("p",{className:"font-italic",children:i}),C.jsx("p",{className:"mt-4",children:n}),C.jsx(ca,{title:c,className:f}),C.jsxs("ul",{className:"mt-3",children:[C.jsx("li",{children:l}),C.jsx("li",{children:o}),C.jsx("li",{children:s}),C.jsx("li",{children:d})]})]}),ge=[{id:"1",className:"suscription-card suscription-card-active ",title:"Basic",price:"0",time:"month",detail:"For small teams wanting to try out BluePrintFix’s core features.",text_1:"Lorem ipsum dolor sit amet",text_2:"Lorem ipsum dolor sit amet",text_3:"Lorem ipsum dolor sit amet",text_4:"Lorem ipsum dolor sit amet",btntitle:"CURRENT PLAN",btn_className:"mt-3 suscription-btn suscription-active-btn"},{id:"2",className:"suscription-card  ",title:"Pro",price:"39",annually:"billed annually",time:"month",detail:"For growing teams looking to scale with BluePrintFix.",text_1:"Lorem ipsum dolor sit amet",text_2:"Lorem ipsum dolor sit amet",text_3:"Lorem ipsum dolor sit amet",text_4:"Lorem ipsum dolor sit amet",btntitle:"BUY PRO",btn_className:"mt-3 suscription-btn"},{id:"3",className:"suscription-card  ",title:"Business",price:"59",annually:"billed annually",time:"month",detail:"For advanced teams unifying their processes on BluePrintFix.",text_1:"Lorem ipsum dolor sit amet",text_2:"Lorem ipsum dolor sit amet",text_3:"Lorem ipsum dolor sit amet",text_4:"Lorem ipsum dolor sit amet",btntitle:"BUY BUSINESS",btn_className:"mt-3 suscription-business-btn"},{id:"4",className:"suscription-card  ",title:"Business Plus",price:"79",annually:"billed annually",time:"month",detail:"For teams connecting BluePrintFix to their core technology.",text_1:"Lorem ipsum dolor sit amet",text_2:"Lorem ipsum dolor sit amet",text_3:"Lorem ipsum dolor sit amet",text_4:"Lorem ipsum dolor sit amet",btntitle:"BUY BUSINESS PLUS",btn_className:"mt-3 suscription-btn"}],Fa=()=>C.jsx("div",{className:"row",children:ge==null?void 0:ge.map((e,t)=>C.jsx("div",{className:"col-12 col-md-6 col-lg-4 col-xl-3",children:C.jsx(_t,{...e})},t))}),Ua=()=>C.jsx("div",{className:"row",children:ge==null?void 0:ge.map((e,t)=>C.jsx("div",{className:"col-12 col-md-6 col-lg-4 col-xl-3",children:C.jsx(_t,{...e})},t))}),Ja=()=>{const e=a=>{},t=[{key:"1",label:"Annual",children:C.jsx(Fa,{})},{key:"2",label:"Monthly",children:C.jsx(Ua,{})}];return C.jsxs(aa,{children:[C.jsx(na,{title:"Subscription"}),C.jsx("div",{className:"subscription-box mt-4",children:C.jsx("div",{className:"row",children:C.jsxs("div",{className:"col-12",children:[C.jsx("h2",{className:"text-center mb-3 font-22",children:"Select Plan"}),C.jsx("p",{className:"text-center mb-4",children:"Transparent pricing for teams and projects of any size"}),C.jsx(Ka,{onChange:e,items:t,className:"custom-tabs"})]})})})]})};export{Ja as default};
