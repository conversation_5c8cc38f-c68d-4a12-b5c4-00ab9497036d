import{r as l,u as c,j as t,L as m}from"./index-BNo7PP7q.js";import{A as u}from"./auth-TFDPTwFC.js";import{u as d,F as p,B as x,c as f,a as h}from"./rules-B0Vq_sHo.js";const g=()=>{const o=c(),{loading:j,postData:n}=d("forgot_password",{type:"submit"}),i=s=>{const a=new FormData;for(const e in s)a.append(e,s[e]);n(a,r)},r=s=>{s.statusCode==200&&o("/")};return t.jsx(u,{title:"Forgot Password",detail:"Please enter your email address associated with your account",children:t.jsxs(p,{name:"login",layout:"vertical",onFinish:i,initialValues:{remember:!0},autoComplete:"off",children:[t.jsx(x,{name:"email",placeholder:"Email",label:"Email",rules:f.email}),t.jsx("div",{children:t.jsx(h,{title:"Send",className:"mx-auto mt-4 signin-btn mt-5",htmlType:"submit"})}),t.jsx("div",{children:t.jsxs("p",{className:"signup-text",children:["Don’t have an account?",t.jsx(m,{to:"/",className:"color-blue font-600 font-16 ms-1",children:"Sign in"})]})})]})})},w=l.memo(g);export{w as default};
