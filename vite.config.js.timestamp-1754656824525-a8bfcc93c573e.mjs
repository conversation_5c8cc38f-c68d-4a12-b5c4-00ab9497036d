// vite.config.js
import { defineConfig } from "file:///C:/xampp-8-0/htdocs/constructify_web_panel/node_modules/vite/dist/node/index.js";
import react from "file:///C:/xampp-8-0/htdocs/constructify_web_panel/node_modules/@vitejs/plugin-react-swc/index.mjs";
var vite_config_default = defineConfig({
  plugins: [react()],
  publicDir: "public",
  optimizeDeps: {
    include: ["react-phone-number-input"]
  }
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,ewogICJ2ZXJzaW9uIjogMywKICAic291cmNlcyI6IFsidml0ZS5jb25maWcuanMiXSwKICAic291cmNlc0NvbnRlbnQiOiBbImNvbnN0IF9fdml0ZV9pbmplY3RlZF9vcmlnaW5hbF9kaXJuYW1lID0gXCJDOlxcXFx4YW1wcC04LTBcXFxcaHRkb2NzXFxcXGNvbnN0cnVjdGlmeV93ZWJfcGFuZWxcIjtjb25zdCBfX3ZpdGVfaW5qZWN0ZWRfb3JpZ2luYWxfZmlsZW5hbWUgPSBcIkM6XFxcXHhhbXBwLTgtMFxcXFxodGRvY3NcXFxcY29uc3RydWN0aWZ5X3dlYl9wYW5lbFxcXFx2aXRlLmNvbmZpZy5qc1wiO2NvbnN0IF9fdml0ZV9pbmplY3RlZF9vcmlnaW5hbF9pbXBvcnRfbWV0YV91cmwgPSBcImZpbGU6Ly8vQzoveGFtcHAtOC0wL2h0ZG9jcy9jb25zdHJ1Y3RpZnlfd2ViX3BhbmVsL3ZpdGUuY29uZmlnLmpzXCI7aW1wb3J0IHsgZGVmaW5lQ29uZmlnIH0gZnJvbSBcInZpdGVcIjtcbmltcG9ydCByZWFjdCBmcm9tIFwiQHZpdGVqcy9wbHVnaW4tcmVhY3Qtc3djXCI7XG5cbi8vIGh0dHBzOi8vdml0ZWpzLmRldi9jb25maWcvXG5leHBvcnQgZGVmYXVsdCBkZWZpbmVDb25maWcoe1xuICBwbHVnaW5zOiBbcmVhY3QoKV0sXG4gIHB1YmxpY0RpcjogXCJwdWJsaWNcIixcbiAgb3B0aW1pemVEZXBzOiB7XG4gIGluY2x1ZGU6IFsncmVhY3QtcGhvbmUtbnVtYmVyLWlucHV0J11cbn1cbn0pO1xuIl0sCiAgIm1hcHBpbmdzIjogIjtBQUF3VCxTQUFTLG9CQUFvQjtBQUNyVixPQUFPLFdBQVc7QUFHbEIsSUFBTyxzQkFBUSxhQUFhO0FBQUEsRUFDMUIsU0FBUyxDQUFDLE1BQU0sQ0FBQztBQUFBLEVBQ2pCLFdBQVc7QUFBQSxFQUNYLGNBQWM7QUFBQSxJQUNkLFNBQVMsQ0FBQywwQkFBMEI7QUFBQSxFQUN0QztBQUNBLENBQUM7IiwKICAibmFtZXMiOiBbXQp9Cg==
