import{r as p,j as e}from"./index-BNo7PP7q.js";import{F as i,B as c,a as d,d as m}from"./rules-B0Vq_sHo.js";import{R as a}from"./index-Dc53ayCB.js";import{b as y}from"./index-C2aV_UZj.js";const F=({projectdetail:l,onApplyFilter:r,isFilterRemove:h})=>{const[n,o]=p.useState(""),[s]=i.useForm(),u=t=>{o(t.target.value)},x=()=>{const t=s.getFieldsValue(),f={filter_by:n||"",start_at:t.start_at?m(t.start_at).format("YYYY-MM-DD"):"",end_at:t.end_at?m(t.end_at).format("YYYY-MM-DD"):"",page:1};r(f)},j=()=>{s.resetFields(),o(""),r({filter_by:"",start_at:"",end_at:"",page:1}),h()};return e.jsxs(i,{form:s,name:"filter",layout:"vertical",onFinish:()=>setIsFilterModalOpen(!1),initialValues:{remember:!0},autoComplete:"off",children:[l!=="true"?e.jsx("p",{className:"font-18 color-black mb-2",children:"Sort By Priority"}):"",e.jsx(a.Group,{onChange:u,value:n,children:e.jsxs(y,{direction:"vertical",children:[e.jsx(a,{value:"recently_opened",children:"Most recently opened"}),e.jsx(a,{value:"alphabetically",children:"Alphabetically"}),e.jsx(a,{value:"asc",children:"Newest to Oldest"})]})}),l!=="true"?e.jsxs("div",{className:"row",children:[e.jsx("div",{className:"col-12 col-md-6",children:e.jsx(c,{name:"start_at",type:"datepiker",placeholder:"",label:"Start Date"})}),e.jsx("div",{className:"col-12 col-md-6",children:e.jsx(c,{name:"end_at",type:"datepiker",placeholder:"",label:"End Date"})})]}):"",e.jsxs("div",{className:"text-end mt-5",children:[e.jsx(d,{title:"Reset",className:"reset-btn",onClick:j}),e.jsx(d,{title:"Apply",className:"add-new-btn",onClick:x})]})]})},N=p.memo(F);export{N as A};
