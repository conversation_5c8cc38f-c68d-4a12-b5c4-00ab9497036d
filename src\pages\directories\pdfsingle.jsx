import React, { useEffect, useRef, useState } from "react";
import { useParams, useLocation } from "react-router-dom";
import InnerLayout from "../../components/shared/layout/innerlayout";
import PageTitle from "../../components/shared/pagetitle";
import BackButton from "../../components/shared/button/backbutton";
import { useFetch } from "../../hooks";
import { Spin, Empty } from "antd";
import CustomTable from "../../components/shared/table/customtable";
import { ColumnsPdfList } from "../../components/partial/configdata/tabledata";
import ComPDFKitViewer from "@compdfkit_pdf_sdk/webviewer";
const PdfViewer = ({ file, onLoading }) => {
  const containerRef = useRef(null);
  const docViewerRef = useRef(null); // store the instance for later use

  useEffect(() => {
    let docViewer = null;
    ComPDFKitViewer.init(
      {
        path: "/",
        pdfUrl: file,
        license: window.constants.compdf_license_key,
      },
      containerRef.current
    ).then((instance) => {
      docViewer = instance.docViewer;
      docViewerRef.current = docViewer;

      docViewer.addEvent("documentloaded", () => {
        console.log("ComPDFKit Web Demo loaded");
      });
    });
  }, [file]);
  const savePdfToServer = async () => {
    if (!docViewerRef.current) return;

    try {
      // Download the edited PDF
      const docStream = await docViewerRef.current.download();
      const docBlob = new Blob([docStream], { type: "application/pdf" });

      // Prepare FormData
      const fileName = "document.pdf";
      const data = new FormData();
      data.append("file", docBlob, fileName);

      // Send to server
      const res = await fetch("https://example.com/api/test", {
        method: "POST",
        body: data,
      });

      if (!res.ok) throw new Error("Upload failed");
      console.log("✅ PDF uploaded successfully");
      alert("PDF saved to server!");
    } catch (error) {
      console.error("❌ Error uploading PDF:", error);
    }
  };

  return (
    <div style={{ height: "100vh", display: "flex", flexDirection: "column" }}>
      {/* Save Button */}
      <div
        style={{ padding: "5px", background: "#f5f5f5", textAlign: "right" }}
      >
        <button
          onClick={savePdfToServer}
          style={{
            padding: "6px 12px",
            border: "none",
            background: "#1890ff",
            color: "#fff",
            borderRadius: "4px",
            cursor: "pointer",
          }}
        >
          Save to Server
        </button>
      </div>

      {/* PDF Container */}
      <div
        ref={containerRef}
        style={{
          flex: 1,
          overflow: "auto",
          padding: "10px",
        }}
      />
    </div>
  );
};

const PdfSingle = () => {
  const location = useLocation();
  const { record } = location.state || {};
  const { project_id, parent_id } = useParams();
  const { loading, data, fetchApi, pagination, setQueryParams } = useFetch(
    "directories",
    {
      slug: `/?project_id=${project_id}&parent_id=${parent_id}`,
      enablePagination: true,
      defaultQueryParams: { page: 1, limit: 10 },
    }
  );

  const [tabs, setTabs] = useState([]);
  const [activeTab, setActiveTab] = useState(null);
  const [pdfLoading, setPdfLoading] = useState(false);

  useEffect(() => {
    fetchApi();
  }, []);

  useEffect(() => {
    if (!window.lodash.isEmpty(record)) {
      const defaultTab = {
        id: record._id,
        title: record.title,
        file: record.file,
      };
      setTabs([defaultTab]);
      setActiveTab(defaultTab.id); // Set the active tab to the default tab
    }
  }, [record]);

  const addTab = (record) => {
    const existingTab = tabs.find((tab) => tab.id === record._id);
    if (!existingTab) {
      setTabs([
        ...tabs,
        { id: record._id, title: record.title, file: record.file },
      ]);
    }
    setActiveTab(record._id);
  };

  const closeTab = (tabId) => {
    const newTabs = tabs.filter((tab) => tab.id !== tabId);
    setTabs(newTabs);
    if (activeTab === tabId && newTabs.length > 0) {
      setActiveTab(newTabs[0].id);
    } else if (newTabs.length === 0) {
      setActiveTab(null);
    }
  };

  const handlePdfLoading = (status) => {
    setPdfLoading(status);
  };
  const handlePageChange = (page, pageSize) => {
    setQueryParams({ page, limit: pageSize });
  };
  return (
    <InnerLayout>
      <PageTitle title={<BackButton />} />
      <Spin spinning={loading} tip="Loading...">
        <div className="row" style={{ height: "100%" }}>
          <div
            className="col-12 col-md-3"
            style={{
              overflowY: "auto",
              padding: "10px",
            }}
          >
            <CustomTable
              columns={ColumnsPdfList({ onViewPdf: addTab })}
              data={data}
              loading={loading}
              rowKey={"_id"}
              scroll={200}
              pagination={{
                current: pagination?.currentPage,
                total: pagination?.count,
                pageSize: pagination?.perPage,
              }}
              showPagination={true}
              onChange={handlePageChange}
            />
          </div>

          <div
            className="col-12 col-md-9"
            style={{
              overflowY: "auto",
              padding: "10px",
            }}
          >
            {tabs.length > 0 && (
              <div
                className="tabs-container"
                style={{
                  display: "flex",
                  alignItems: "center",
                  backgroundColor: "#fff",
                  padding: "10px 0",
                  marginBottom: "10px",
                  width: "100%",
                  overflow: "auto",
                }}
              >
                {tabs.map((tab) => (
                  <div
                    key={tab.id}
                    className={`tab ${activeTab === tab.id ? "active" : ""}`}
                    style={{
                      display: "flex",
                      alignItems: "center",
                      padding: "10px 20px",
                      margin: "0 5px",
                      borderRadius: "10px 10px 0 0",
                      boxShadow:
                        activeTab === tab.id
                          ? "0 4px 8px rgba(0, 0, 0, 0.2)"
                          : "none",
                      backgroundColor:
                        activeTab === tab.id ? "#e0e0e0" : "#fff",
                      cursor: "pointer",
                      position: "relative",
                    }}
                    onClick={() => setActiveTab(tab.id)}
                  >
                    {tab.title}
                    <span
                      style={{
                        marginLeft: "10px",
                        color: "red",
                        cursor: "pointer",
                        position: "absolute",
                        top: "5px",
                        right: "5px",
                      }}
                      onClick={(e) => {
                        e.stopPropagation();
                        closeTab(tab.id);
                      }}
                    >
                      x
                    </span>
                  </div>
                ))}
              </div>
            )}
            <div
              className="tab-content"
              style={{
                backgroundColor: "#fff",
                padding: "10px",
                position: "relative",
                width: "100%",
                overflow: "auto",
              }}
            >
              {pdfLoading && (
                <Spin
                  spinning={pdfLoading}
                  tip="Loading PDF..."
                  style={{
                    position: "absolute",
                    top: "50%",
                    left: "50%",
                    transform: "translate(-50%, -50%)",
                  }}
                />
              )}
              {tabs.length > 0 ? (
                tabs.map(
                  (tab) =>
                    activeTab === tab.id && (
                      <PdfViewer
                        key={tab.id}
                        file={tab.file}
                        onLoading={handlePdfLoading} // Pass onLoading here
                      />
                    )
                )
              ) : (
                <Empty
                  description="No PDF is selected"
                  style={{ marginTop: "20px" }}
                />
              )}
            </div>
          </div>
        </div>
      </Spin>
    </InnerLayout>
  );
};

export default PdfSingle;
