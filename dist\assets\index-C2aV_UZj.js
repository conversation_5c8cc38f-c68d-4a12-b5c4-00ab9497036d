import{r as a,x as ve,aj as Zi,R as Z,ah as uo,H as M,d as J,v as Ke,M as vn,f as te,B as ce,V as bt,ak as Go,W as Ee,ai as _t,t as Xr,X as We,Z as Fe,Y as Xe,a1 as L,a4 as ue,I as ke,A as mo,al as qr,am as Qi,$ as tt,ad as on,a9 as ut,an as At,ao as Ur,ae as Ji,a7 as Lt,ap as Kr,aq as el,ar as Gr,as as Yr,at as tl,a5 as fo,au as nl,av as ol,aw as rl,ax as al,aa as rn,C as il,ab as go,a3 as Je,w as vt,E as Zr,z as Oe,J as ee,F as ll,D as po,y as vo,N as ho,O as bo,P as Co,T as yo,i as Kn,ay as sl,a0 as cl,a6 as hn,a8 as He,az as dl,U as ul,k as Yo,a<PERSON> as Gn,ac as Qr,a2 as Yn,aB as Zo,aC as ml,aD as fl,aE as bn,aF as gl,aG as pl,aH as vl,aI as Jr,aJ as hl,aK as bl,aL as Qo,Q as Ae,aM as Cl,aN as Jo,aO as er,aP as yl,c as $l,j as k,L as tr,m as nr,u as Sl}from"./index-BNo7PP7q.js";import{g as ht,U as ea,V as wl,X as ta,o as qe,N as Cn,Y as xl,Z as Ht,_ as na,P as at,$ as Il,w as oa,a0 as Ol,j as El,a1 as ra,a2 as Pl,a3 as aa,a4 as ia,a5 as Nl,n as Ve,T as yn,t as Ft,a6 as la,a7 as sa,a8 as ca,l as Rl,a9 as Bt,r as da,Q as an,s as ua,aa as jl,ab as Ml,ac as Tl,ad as kl,ae as or,af as zl,ag as Bl,ah as Ll,ai as Dl,aj as _l,ak as ma,al as Al,am as Hl,W as Fl,u as dt,p as Wl,F as xt,a as $o,B as gt,c as Zt,e as Vl}from"./rules-B0Vq_sHo.js";import{P as Xl}from"./index-BNdc7YlR.js";import{r as ql,m as rr,o as Ul}from"./notification-7LrlFXRF.js";function Kl(){const[e,t]=a.useState([]),n=a.useCallback(o=>(t(r=>[].concat(ve(r),[o])),()=>{t(r=>r.filter(i=>i!==o))}),[]);return[e,n]}function zn(e){return!!(e!=null&&e.then)}const fa=e=>{const{type:t,children:n,prefixCls:o,buttonProps:r,close:i,autoFocus:l,emitEvent:s,isSilent:c,quitOnNullishReturnValue:d,actionFn:u}=e,m=a.useRef(!1),f=a.useRef(null),[p,g]=Zi(!1),v=function(){i==null||i.apply(void 0,arguments)};a.useEffect(()=>{let b=null;return l&&(b=setTimeout(()=>{var y;(y=f.current)===null||y===void 0||y.focus()})),()=>{b&&clearTimeout(b)}},[]);const $=b=>{zn(b)&&(g(!0),b.then(function(){g(!1,!0),v.apply(void 0,arguments),m.current=!1},y=>{if(g(!1,!0),m.current=!1,!(c!=null&&c()))return Promise.reject(y)}))},h=b=>{if(m.current)return;if(m.current=!0,!u){v();return}let y;if(s){if(y=u(b),d&&!zn(y)){m.current=!1,v(b);return}}else if(u.length)y=u(i),m.current=!1;else if(y=u(),!zn(y)){v();return}$(y)};return a.createElement(ht,Object.assign({},ea(t),{onClick:h,loading:p,prefixCls:o},r,{ref:f}),n)},Wt=Z.createContext({}),{Provider:ga}=Wt,ar=()=>{const{autoFocusButton:e,cancelButtonProps:t,cancelTextLocale:n,isSilent:o,mergedOkCancel:r,rootPrefixCls:i,close:l,onCancel:s,onConfirm:c}=a.useContext(Wt);return r?Z.createElement(fa,{isSilent:o,actionFn:s,close:function(){l==null||l.apply(void 0,arguments),c==null||c(!1)},autoFocus:e==="cancel",buttonProps:t,prefixCls:`${i}-btn`},n):null},ir=()=>{const{autoFocusButton:e,close:t,isSilent:n,okButtonProps:o,rootPrefixCls:r,okTextLocale:i,okType:l,onConfirm:s,onOk:c}=a.useContext(Wt);return Z.createElement(fa,{isSilent:n,type:l||"primary",actionFn:c,close:function(){t==null||t.apply(void 0,arguments),s==null||s(!0)},autoFocus:e==="ok",buttonProps:o,prefixCls:`${r}-btn`},i)};var pa=a.createContext({});function lr(e,t,n){var o=t;return!o&&n&&(o="".concat(e,"-").concat(n)),o}function sr(e,t){var n=e["page".concat(t?"Y":"X","Offset")],o="scroll".concat(t?"Top":"Left");if(typeof n!="number"){var r=e.document;n=r.documentElement[o],typeof n!="number"&&(n=r.body[o])}return n}function Gl(e){var t=e.getBoundingClientRect(),n={left:t.left,top:t.top},o=e.ownerDocument,r=o.defaultView||o.parentWindow;return n.left+=sr(r),n.top+=sr(r,!0),n}const Yl=a.memo(function(e){var t=e.children;return t},function(e,t){var n=t.shouldUpdate;return!n});var cr={width:0,height:0,overflow:"hidden",outline:"none"},Zl={outline:"none"},va=Z.forwardRef(function(e,t){var n=e.prefixCls,o=e.className,r=e.style,i=e.title,l=e.ariaId,s=e.footer,c=e.closable,d=e.closeIcon,u=e.onClose,m=e.children,f=e.bodyStyle,p=e.bodyProps,g=e.modalRender,v=e.onMouseDown,$=e.onMouseUp,h=e.holderRef,b=e.visible,y=e.forceRender,S=e.width,C=e.height,w=e.classNames,x=e.styles,O=Z.useContext(pa),I=O.panel,P=uo(h,I),j=a.useRef(),N=a.useRef(),E=a.useRef();Z.useImperativeHandle(t,function(){return{focus:function(){var _;(_=E.current)===null||_===void 0||_.focus({preventScroll:!0})},changeActive:function(_){var B=document,X=B.activeElement;_&&X===N.current?j.current.focus({preventScroll:!0}):!_&&X===j.current&&N.current.focus({preventScroll:!0})}}});var R={};S!==void 0&&(R.width=S),C!==void 0&&(R.height=C);var z=s?Z.createElement("div",{className:M("".concat(n,"-footer"),w==null?void 0:w.footer),style:J({},x==null?void 0:x.footer)},s):null,D=i?Z.createElement("div",{className:M("".concat(n,"-header"),w==null?void 0:w.header),style:J({},x==null?void 0:x.header)},Z.createElement("div",{className:"".concat(n,"-title"),id:l},i)):null,T=a.useMemo(function(){return Ke(c)==="object"&&c!==null?c:c?{closeIcon:d??Z.createElement("span",{className:"".concat(n,"-close-x")})}:{}},[c,d,n]),K=vn(T,!0),U=Ke(c)==="object"&&c.disabled,V=c?Z.createElement("button",te({type:"button",onClick:u,"aria-label":"Close"},K,{className:"".concat(n,"-close"),disabled:U}),T.closeIcon):null,F=Z.createElement("div",{className:M("".concat(n,"-content"),w==null?void 0:w.content),style:x==null?void 0:x.content},V,D,Z.createElement("div",te({className:M("".concat(n,"-body"),w==null?void 0:w.body),style:J(J({},f),x==null?void 0:x.body)},p),m),z);return Z.createElement("div",{key:"dialog-element",role:"dialog","aria-labelledby":i?l:null,"aria-modal":"true",ref:P,style:J(J({},r),R),className:M(n,o),onMouseDown:v,onMouseUp:$},Z.createElement("div",{tabIndex:0,ref:j,style:cr,"aria-hidden":"true"}),Z.createElement("div",{ref:E,tabIndex:-1,style:Zl},Z.createElement(Yl,{shouldUpdate:b||y},g?g(F):F)),Z.createElement("div",{tabIndex:0,ref:N,style:cr,"aria-hidden":"true"}))}),ha=a.forwardRef(function(e,t){var n=e.prefixCls,o=e.title,r=e.style,i=e.className,l=e.visible,s=e.forceRender,c=e.destroyOnClose,d=e.motionName,u=e.ariaId,m=e.onVisibleChanged,f=e.mousePosition,p=a.useRef(),g=a.useState(),v=ce(g,2),$=v[0],h=v[1],b={};$&&(b.transformOrigin=$);function y(){var S=Gl(p.current);h(f&&(f.x||f.y)?"".concat(f.x-S.left,"px ").concat(f.y-S.top,"px"):"")}return a.createElement(bt,{visible:l,onVisibleChanged:m,onAppearPrepare:y,onEnterPrepare:y,forceRender:s,motionName:d,removeOnLeave:c,ref:p},function(S,C){var w=S.className,x=S.style;return a.createElement(va,te({},e,{ref:t,title:o,ariaId:u,prefixCls:n,holderRef:C,style:J(J(J({},x),r),b),className:M(i,w)}))})});ha.displayName="Content";var Ql=function(t){var n=t.prefixCls,o=t.style,r=t.visible,i=t.maskProps,l=t.motionName,s=t.className;return a.createElement(bt,{key:"mask",visible:r,motionName:l,leavedClassName:"".concat(n,"-mask-hidden")},function(c,d){var u=c.className,m=c.style;return a.createElement("div",te({ref:d,style:J(J({},m),o),className:M("".concat(n,"-mask"),u,s)},i))})},Jl=function(t){var n=t.prefixCls,o=n===void 0?"rc-dialog":n,r=t.zIndex,i=t.visible,l=i===void 0?!1:i,s=t.keyboard,c=s===void 0?!0:s,d=t.focusTriggerAfterClose,u=d===void 0?!0:d,m=t.wrapStyle,f=t.wrapClassName,p=t.wrapProps,g=t.onClose,v=t.afterOpenChange,$=t.afterClose,h=t.transitionName,b=t.animation,y=t.closable,S=y===void 0?!0:y,C=t.mask,w=C===void 0?!0:C,x=t.maskTransitionName,O=t.maskAnimation,I=t.maskClosable,P=I===void 0?!0:I,j=t.maskStyle,N=t.maskProps,E=t.rootClassName,R=t.classNames,z=t.styles,D=a.useRef(),T=a.useRef(),K=a.useRef(),U=a.useState(l),V=ce(U,2),F=V[0],A=V[1],_=wl();function B(){Go(T.current,document.activeElement)||(D.current=document.activeElement)}function X(){if(!Go(T.current,document.activeElement)){var re;(re=K.current)===null||re===void 0||re.focus()}}function Q(re){if(re)X();else{if(A(!1),w&&D.current&&u){try{D.current.focus({preventScroll:!0})}catch{}D.current=null}F&&($==null||$())}v==null||v(re)}function q(re){g==null||g(re)}var H=a.useRef(!1),W=a.useRef(),G=function(){clearTimeout(W.current),H.current=!0},le=function(){W.current=setTimeout(function(){H.current=!1})},ae=null;P&&(ae=function(xe){H.current?H.current=!1:T.current===xe.target&&q(xe)});function Y(re){if(c&&re.keyCode===Ee.ESC){re.stopPropagation(),q(re);return}l&&re.keyCode===Ee.TAB&&K.current.changeActive(!re.shiftKey)}a.useEffect(function(){l&&(A(!0),B())},[l]),a.useEffect(function(){return function(){clearTimeout(W.current)}},[]);var ie=J(J(J({zIndex:r},m),z==null?void 0:z.wrapper),{},{display:F?null:"none"});return a.createElement("div",te({className:M("".concat(o,"-root"),E)},vn(t,{data:!0})),a.createElement(Ql,{prefixCls:o,visible:w&&l,motionName:lr(o,x,O),style:J(J({zIndex:r},j),z==null?void 0:z.mask),maskProps:N,className:R==null?void 0:R.mask}),a.createElement("div",te({tabIndex:-1,onKeyDown:Y,className:M("".concat(o,"-wrap"),f,R==null?void 0:R.wrapper),ref:T,onClick:ae,style:ie},p),a.createElement(ha,te({},t,{onMouseDown:G,onMouseUp:le,ref:K,closable:S,ariaId:_,prefixCls:o,visible:l&&F,onClose:q,onVisibleChanged:Q,motionName:lr(o,h,b)}))))},So=function(t){var n=t.visible,o=t.getContainer,r=t.forceRender,i=t.destroyOnClose,l=i===void 0?!1:i,s=t.afterClose,c=t.panelRef,d=a.useState(n),u=ce(d,2),m=u[0],f=u[1],p=a.useMemo(function(){return{panel:c}},[c]);return a.useEffect(function(){n&&f(!0)},[n]),!r&&l&&!m?null:a.createElement(pa.Provider,{value:p},a.createElement(ta,{open:n||r||m,autoDestroy:!1,getContainer:o,autoLock:n||m},a.createElement(Jl,te({},t,{destroyOnClose:l,afterClose:function(){s==null||s(),f(!1)}}))))};So.displayName="Dialog";function dr(e){if(e)return{closable:e.closable,closeIcon:e.closeIcon}}function ur(e){const{closable:t,closeIcon:n}=e||{};return Z.useMemo(()=>{if(!t&&(t===!1||n===!1||n===null))return!1;if(t===void 0&&n===void 0)return null;let o={closeIcon:typeof n!="boolean"&&n!==null?n:void 0};return t&&typeof t=="object"&&(o=Object.assign(Object.assign({},o),t)),o},[t,n])}function mr(){const e={};for(var t=arguments.length,n=new Array(t),o=0;o<t;o++)n[o]=arguments[o];return n.forEach(r=>{r&&Object.keys(r).forEach(i=>{r[i]!==void 0&&(e[i]=r[i])})}),e}const es={};function ts(e,t){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:es;const o=ur(e),r=ur(t),i=Z.useMemo(()=>Object.assign({closeIcon:Z.createElement(_t,null)},n),[n]),l=Z.useMemo(()=>o===!1?!1:o?mr(i,r,o):r===!1?!1:r?mr(i,r):i.closable?i:!1,[o,r,i]);return Z.useMemo(()=>{if(l===!1)return[!1,null];const{closeIconRender:s}=i,{closeIcon:c}=l;let d=c;if(d!=null){s&&(d=s(c));const u=vn(l,!0);Object.keys(u).length&&(d=Z.isValidElement(d)?Z.cloneElement(d,u):Z.createElement("span",Object.assign({},u),d))}return[!0,d]},[l,i])}const ns=()=>Xr()&&window.document.documentElement,$n=e=>{const{prefixCls:t,className:n,style:o,size:r,shape:i}=e,l=M({[`${t}-lg`]:r==="large",[`${t}-sm`]:r==="small"}),s=M({[`${t}-circle`]:i==="circle",[`${t}-square`]:i==="square",[`${t}-round`]:i==="round"}),c=a.useMemo(()=>typeof r=="number"?{width:r,height:r,lineHeight:`${r}px`}:{},[r]);return a.createElement("span",{className:M(t,l,s,n),style:Object.assign(Object.assign({},c),o)})},os=new Xe("ant-skeleton-loading",{"0%":{backgroundPosition:"100% 50%"},"100%":{backgroundPosition:"0 50%"}}),Sn=e=>({height:e,lineHeight:L(e)}),It=e=>Object.assign({width:e},Sn(e)),rs=e=>({background:e.skeletonLoadingBackground,backgroundSize:"400% 100%",animationName:os,animationDuration:e.skeletonLoadingMotionDuration,animationTimingFunction:"ease",animationIterationCount:"infinite"}),Bn=(e,t)=>Object.assign({width:t(e).mul(5).equal(),minWidth:t(e).mul(5).equal()},Sn(e)),as=e=>{const{skeletonAvatarCls:t,gradientFromColor:n,controlHeight:o,controlHeightLG:r,controlHeightSM:i}=e;return{[t]:Object.assign({display:"inline-block",verticalAlign:"top",background:n},It(o)),[`${t}${t}-circle`]:{borderRadius:"50%"},[`${t}${t}-lg`]:Object.assign({},It(r)),[`${t}${t}-sm`]:Object.assign({},It(i))}},is=e=>{const{controlHeight:t,borderRadiusSM:n,skeletonInputCls:o,controlHeightLG:r,controlHeightSM:i,gradientFromColor:l,calc:s}=e;return{[o]:Object.assign({display:"inline-block",verticalAlign:"top",background:l,borderRadius:n},Bn(t,s)),[`${o}-lg`]:Object.assign({},Bn(r,s)),[`${o}-sm`]:Object.assign({},Bn(i,s))}},fr=e=>Object.assign({width:e},Sn(e)),ls=e=>{const{skeletonImageCls:t,imageSizeBase:n,gradientFromColor:o,borderRadiusSM:r,calc:i}=e;return{[t]:Object.assign(Object.assign({display:"flex",alignItems:"center",justifyContent:"center",verticalAlign:"top",background:o,borderRadius:r},fr(i(n).mul(2).equal())),{[`${t}-path`]:{fill:"#bfbfbf"},[`${t}-svg`]:Object.assign(Object.assign({},fr(n)),{maxWidth:i(n).mul(4).equal(),maxHeight:i(n).mul(4).equal()}),[`${t}-svg${t}-svg-circle`]:{borderRadius:"50%"}}),[`${t}${t}-circle`]:{borderRadius:"50%"}}},Ln=(e,t,n)=>{const{skeletonButtonCls:o}=e;return{[`${n}${o}-circle`]:{width:t,minWidth:t,borderRadius:"50%"},[`${n}${o}-round`]:{borderRadius:t}}},Dn=(e,t)=>Object.assign({width:t(e).mul(2).equal(),minWidth:t(e).mul(2).equal()},Sn(e)),ss=e=>{const{borderRadiusSM:t,skeletonButtonCls:n,controlHeight:o,controlHeightLG:r,controlHeightSM:i,gradientFromColor:l,calc:s}=e;return Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({[n]:Object.assign({display:"inline-block",verticalAlign:"top",background:l,borderRadius:t,width:s(o).mul(2).equal(),minWidth:s(o).mul(2).equal()},Dn(o,s))},Ln(e,o,n)),{[`${n}-lg`]:Object.assign({},Dn(r,s))}),Ln(e,r,`${n}-lg`)),{[`${n}-sm`]:Object.assign({},Dn(i,s))}),Ln(e,i,`${n}-sm`))},cs=e=>{const{componentCls:t,skeletonAvatarCls:n,skeletonTitleCls:o,skeletonParagraphCls:r,skeletonButtonCls:i,skeletonInputCls:l,skeletonImageCls:s,controlHeight:c,controlHeightLG:d,controlHeightSM:u,gradientFromColor:m,padding:f,marginSM:p,borderRadius:g,titleHeight:v,blockRadius:$,paragraphLiHeight:h,controlHeightXS:b,paragraphMarginTop:y}=e;return{[t]:{display:"table",width:"100%",[`${t}-header`]:{display:"table-cell",paddingInlineEnd:f,verticalAlign:"top",[n]:Object.assign({display:"inline-block",verticalAlign:"top",background:m},It(c)),[`${n}-circle`]:{borderRadius:"50%"},[`${n}-lg`]:Object.assign({},It(d)),[`${n}-sm`]:Object.assign({},It(u))},[`${t}-content`]:{display:"table-cell",width:"100%",verticalAlign:"top",[o]:{width:"100%",height:v,background:m,borderRadius:$,[`+ ${r}`]:{marginBlockStart:u}},[r]:{padding:0,"> li":{width:"100%",height:h,listStyle:"none",background:m,borderRadius:$,"+ li":{marginBlockStart:b}}},[`${r}> li:last-child:not(:first-child):not(:nth-child(2))`]:{width:"61%"}},[`&-round ${t}-content`]:{[`${o}, ${r} > li`]:{borderRadius:g}}},[`${t}-with-avatar ${t}-content`]:{[o]:{marginBlockStart:p,[`+ ${r}`]:{marginBlockStart:y}}},[`${t}${t}-element`]:Object.assign(Object.assign(Object.assign(Object.assign({display:"inline-block",width:"auto"},ss(e)),as(e)),is(e)),ls(e)),[`${t}${t}-block`]:{width:"100%",[i]:{width:"100%"},[l]:{width:"100%"}},[`${t}${t}-active`]:{[`
        ${o},
        ${r} > li,
        ${n},
        ${i},
        ${l},
        ${s}
      `]:Object.assign({},rs(e))}}},ds=e=>{const{colorFillContent:t,colorFill:n}=e,o=t,r=n;return{color:o,colorGradientEnd:r,gradientFromColor:o,gradientToColor:r,titleHeight:e.controlHeight/2,blockRadius:e.borderRadiusSM,paragraphMarginTop:e.marginLG+e.marginXXS,paragraphLiHeight:e.controlHeight/2}},Ot=We("Skeleton",e=>{const{componentCls:t,calc:n}=e,o=Fe(e,{skeletonAvatarCls:`${t}-avatar`,skeletonTitleCls:`${t}-title`,skeletonParagraphCls:`${t}-paragraph`,skeletonButtonCls:`${t}-button`,skeletonInputCls:`${t}-input`,skeletonImageCls:`${t}-image`,imageSizeBase:n(e.controlHeight).mul(1.5).equal(),borderRadius:100,skeletonLoadingBackground:`linear-gradient(90deg, ${e.gradientFromColor} 25%, ${e.gradientToColor} 37%, ${e.gradientFromColor} 63%)`,skeletonLoadingMotionDuration:"1.4s"});return[cs(o)]},ds,{deprecatedTokens:[["color","gradientFromColor"],["colorGradientEnd","gradientToColor"]]}),us=e=>{const{prefixCls:t,className:n,rootClassName:o,active:r,shape:i="circle",size:l="default"}=e,{getPrefixCls:s}=a.useContext(ue),c=s("skeleton",t),[d,u,m]=Ot(c),f=qe(e,["prefixCls","className"]),p=M(c,`${c}-element`,{[`${c}-active`]:r},n,o,u,m);return d(a.createElement("div",{className:p},a.createElement($n,Object.assign({prefixCls:`${c}-avatar`,shape:i,size:l},f))))},ms=e=>{const{prefixCls:t,className:n,rootClassName:o,active:r,block:i=!1,size:l="default"}=e,{getPrefixCls:s}=a.useContext(ue),c=s("skeleton",t),[d,u,m]=Ot(c),f=qe(e,["prefixCls"]),p=M(c,`${c}-element`,{[`${c}-active`]:r,[`${c}-block`]:i},n,o,u,m);return d(a.createElement("div",{className:p},a.createElement($n,Object.assign({prefixCls:`${c}-button`,size:l},f))))},fs="M365.714286 329.142857q0 45.714286-32.036571 77.677714t-77.677714 32.036571-77.677714-32.036571-32.036571-77.677714 32.036571-77.677714 77.677714-32.036571 77.677714 32.036571 32.036571 77.677714zM950.857143 548.571429l0 256-804.571429 0 0-109.714286 182.857143-182.857143 91.428571 91.428571 292.571429-292.571429zM1005.714286 146.285714l-914.285714 0q-7.460571 0-12.873143 5.412571t-5.412571 12.873143l0 694.857143q0 7.460571 5.412571 12.873143t12.873143 5.412571l914.285714 0q7.460571 0 12.873143-5.412571t5.412571-12.873143l0-694.857143q0-7.460571-5.412571-12.873143t-12.873143-5.412571zM1097.142857 164.571429l0 694.857143q0 37.741714-26.843429 64.585143t-64.585143 26.843429l-914.285714 0q-37.741714 0-64.585143-26.843429t-26.843429-64.585143l0-694.857143q0-37.741714 26.843429-64.585143t64.585143-26.843429l914.285714 0q37.741714 0 64.585143 26.843429t26.843429 64.585143z",gs=e=>{const{prefixCls:t,className:n,rootClassName:o,style:r,active:i}=e,{getPrefixCls:l}=a.useContext(ue),s=l("skeleton",t),[c,d,u]=Ot(s),m=M(s,`${s}-element`,{[`${s}-active`]:i},n,o,d,u);return c(a.createElement("div",{className:m},a.createElement("div",{className:M(`${s}-image`,n),style:r},a.createElement("svg",{viewBox:"0 0 1098 1024",xmlns:"http://www.w3.org/2000/svg",className:`${s}-image-svg`},a.createElement("title",null,"Image placeholder"),a.createElement("path",{d:fs,className:`${s}-image-path`})))))},ps=e=>{const{prefixCls:t,className:n,rootClassName:o,active:r,block:i,size:l="default"}=e,{getPrefixCls:s}=a.useContext(ue),c=s("skeleton",t),[d,u,m]=Ot(c),f=qe(e,["prefixCls"]),p=M(c,`${c}-element`,{[`${c}-active`]:r,[`${c}-block`]:i},n,o,u,m);return d(a.createElement("div",{className:p},a.createElement($n,Object.assign({prefixCls:`${c}-input`,size:l},f))))};var vs={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M888 792H200V168c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v688c0 4.4 3.6 8 8 8h752c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM288 604a64 64 0 10128 0 64 64 0 10-128 0zm118-224a48 48 0 1096 0 48 48 0 10-96 0zm158 228a96 96 0 10192 0 96 96 0 10-192 0zm148-314a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"dot-chart",theme:"outlined"},hs=function(t,n){return a.createElement(ke,te({},t,{ref:n,icon:vs}))},bs=a.forwardRef(hs);const Cs=e=>{const{prefixCls:t,className:n,rootClassName:o,style:r,active:i,children:l}=e,{getPrefixCls:s}=a.useContext(ue),c=s("skeleton",t),[d,u,m]=Ot(c),f=M(c,`${c}-element`,{[`${c}-active`]:i},u,n,o,m),p=l??a.createElement(bs,null);return d(a.createElement("div",{className:f},a.createElement("div",{className:M(`${c}-image`,n),style:r},p)))},ys=(e,t)=>{const{width:n,rows:o=2}=t;if(Array.isArray(n))return n[e];if(o-1===e)return n},$s=e=>{const{prefixCls:t,className:n,style:o,rows:r}=e,i=ve(Array(r)).map((l,s)=>a.createElement("li",{key:s,style:{width:ys(s,e)}}));return a.createElement("ul",{className:M(t,n),style:o},i)},Ss=e=>{let{prefixCls:t,className:n,width:o,style:r}=e;return a.createElement("h3",{className:M(t,n),style:Object.assign({width:o},r)})};function _n(e){return e&&typeof e=="object"?e:{}}function ws(e,t){return e&&!t?{size:"large",shape:"square"}:{size:"large",shape:"circle"}}function xs(e,t){return!e&&t?{width:"38%"}:e&&t?{width:"50%"}:{}}function Is(e,t){const n={};return(!e||!t)&&(n.width="61%"),!e&&t?n.rows=3:n.rows=2,n}const it=e=>{const{prefixCls:t,loading:n,className:o,rootClassName:r,style:i,children:l,avatar:s=!1,title:c=!0,paragraph:d=!0,active:u,round:m}=e,{getPrefixCls:f,direction:p,skeleton:g}=a.useContext(ue),v=f("skeleton",t),[$,h,b]=Ot(v);if(n||!("loading"in e)){const y=!!s,S=!!c,C=!!d;let w;if(y){const I=Object.assign(Object.assign({prefixCls:`${v}-avatar`},ws(S,C)),_n(s));w=a.createElement("div",{className:`${v}-header`},a.createElement($n,Object.assign({},I)))}let x;if(S||C){let I;if(S){const j=Object.assign(Object.assign({prefixCls:`${v}-title`},xs(y,C)),_n(c));I=a.createElement(Ss,Object.assign({},j))}let P;if(C){const j=Object.assign(Object.assign({prefixCls:`${v}-paragraph`},Is(y,S)),_n(d));P=a.createElement($s,Object.assign({},j))}x=a.createElement("div",{className:`${v}-content`},I,P)}const O=M(v,{[`${v}-with-avatar`]:y,[`${v}-active`]:u,[`${v}-rtl`]:p==="rtl",[`${v}-round`]:m},g==null?void 0:g.className,o,r,h,b);return $(a.createElement("div",{className:O,style:Object.assign(Object.assign({},g==null?void 0:g.style),i)},w,x))}return l??null};it.Button=ms;it.Avatar=us;it.Input=ps;it.Image=gs;it.Node=Cs;function gr(){}const Os=a.createContext({add:gr,remove:gr});function Es(e){const t=a.useContext(Os),n=a.useRef();return mo(r=>{if(r){const i=e?r.querySelector(e):r;t.add(i),n.current=i}else t.remove(n.current)})}const pr=()=>{const{cancelButtonProps:e,cancelTextLocale:t,onCancel:n}=a.useContext(Wt);return Z.createElement(ht,Object.assign({onClick:n},e),t)},vr=()=>{const{confirmLoading:e,okButtonProps:t,okType:n,okTextLocale:o,onOk:r}=a.useContext(Wt);return Z.createElement(ht,Object.assign({},ea(n),{loading:e,onClick:r},t),o)};function ba(e,t){return Z.createElement("span",{className:`${e}-close-x`},t||Z.createElement(_t,{className:`${e}-close-icon`}))}const Ca=e=>{const{okText:t,okType:n="primary",cancelText:o,confirmLoading:r,onOk:i,onCancel:l,okButtonProps:s,cancelButtonProps:c,footer:d}=e,[u]=Cn("Modal",qr()),m=t||(u==null?void 0:u.okText),f=o||(u==null?void 0:u.cancelText),p={confirmLoading:r,okButtonProps:s,cancelButtonProps:c,okTextLocale:m,cancelTextLocale:f,okType:n,onOk:i,onCancel:l},g=Z.useMemo(()=>p,ve(Object.values(p)));let v;return typeof d=="function"||typeof d>"u"?(v=Z.createElement(Z.Fragment,null,Z.createElement(pr,null),Z.createElement(vr,null)),typeof d=="function"&&(v=d(v,{OkBtn:vr,CancelBtn:pr})),v=Z.createElement(ga,{value:g},v)):v=d,Z.createElement(Qi,{disabled:!1},v)},Ps=new Xe("antFadeIn",{"0%":{opacity:0},"100%":{opacity:1}}),Ns=new Xe("antFadeOut",{"0%":{opacity:1},"100%":{opacity:0}}),wo=function(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;const{antCls:n}=e,o=`${n}-fade`,r=t?"&":"";return[xl(o,Ps,Ns,e.motionDurationMid,t),{[`
        ${r}${o}-enter,
        ${r}${o}-appear
      `]:{opacity:0,animationTimingFunction:"linear"},[`${r}${o}-leave`]:{animationTimingFunction:"linear"}}]};function hr(e){return{position:e,inset:0}}const ya=e=>{const{componentCls:t,antCls:n}=e;return[{[`${t}-root`]:{[`${t}${n}-zoom-enter, ${t}${n}-zoom-appear`]:{transform:"none",opacity:0,animationDuration:e.motionDurationSlow,userSelect:"none"},[`${t}${n}-zoom-leave ${t}-content`]:{pointerEvents:"none"},[`${t}-mask`]:Object.assign(Object.assign({},hr("fixed")),{zIndex:e.zIndexPopupBase,height:"100%",backgroundColor:e.colorBgMask,pointerEvents:"none",[`${t}-hidden`]:{display:"none"}}),[`${t}-wrap`]:Object.assign(Object.assign({},hr("fixed")),{zIndex:e.zIndexPopupBase,overflow:"auto",outline:0,WebkitOverflowScrolling:"touch"})}},{[`${t}-root`]:wo(e)}]},Rs=e=>{const{componentCls:t}=e;return[{[`${t}-root`]:{[`${t}-wrap-rtl`]:{direction:"rtl"},[`${t}-centered`]:{textAlign:"center","&::before":{display:"inline-block",width:0,height:"100%",verticalAlign:"middle",content:'""'},[t]:{top:0,display:"inline-block",paddingBottom:0,textAlign:"start",verticalAlign:"middle"}},[`@media (max-width: ${e.screenSMMax}px)`]:{[t]:{maxWidth:"calc(100vw - 16px)",margin:`${L(e.marginXS)} auto`},[`${t}-centered`]:{[t]:{flex:1}}}}},{[t]:Object.assign(Object.assign({},tt(e)),{pointerEvents:"none",position:"relative",top:100,width:"auto",maxWidth:`calc(100vw - ${L(e.calc(e.margin).mul(2).equal())})`,margin:"0 auto",paddingBottom:e.paddingLG,[`${t}-title`]:{margin:0,color:e.titleColor,fontWeight:e.fontWeightStrong,fontSize:e.titleFontSize,lineHeight:e.titleLineHeight,wordWrap:"break-word"},[`${t}-content`]:{position:"relative",backgroundColor:e.contentBg,backgroundClip:"padding-box",border:0,borderRadius:e.borderRadiusLG,boxShadow:e.boxShadow,pointerEvents:"auto",padding:e.contentPadding},[`${t}-close`]:Object.assign({position:"absolute",top:e.calc(e.modalHeaderHeight).sub(e.modalCloseBtnSize).div(2).equal(),insetInlineEnd:e.calc(e.modalHeaderHeight).sub(e.modalCloseBtnSize).div(2).equal(),zIndex:e.calc(e.zIndexPopupBase).add(10).equal(),padding:0,color:e.modalCloseIconColor,fontWeight:e.fontWeightStrong,lineHeight:1,textDecoration:"none",background:"transparent",borderRadius:e.borderRadiusSM,width:e.modalCloseBtnSize,height:e.modalCloseBtnSize,border:0,outline:0,cursor:"pointer",transition:`color ${e.motionDurationMid}, background-color ${e.motionDurationMid}`,"&-x":{display:"flex",fontSize:e.fontSizeLG,fontStyle:"normal",lineHeight:L(e.modalCloseBtnSize),justifyContent:"center",textTransform:"none",textRendering:"auto"},"&:hover":{color:e.modalCloseIconHoverColor,backgroundColor:e.colorBgTextHover,textDecoration:"none"},"&:active":{backgroundColor:e.colorBgTextActive}},on(e)),[`${t}-header`]:{color:e.colorText,background:e.headerBg,borderRadius:`${L(e.borderRadiusLG)} ${L(e.borderRadiusLG)} 0 0`,marginBottom:e.headerMarginBottom,padding:e.headerPadding,borderBottom:e.headerBorderBottom},[`${t}-body`]:{fontSize:e.fontSize,lineHeight:e.lineHeight,wordWrap:"break-word",padding:e.bodyPadding,[`${t}-body-skeleton`]:{width:"100%",height:"100%",display:"flex",justifyContent:"center",alignItems:"center",margin:`${L(e.margin)} auto`}},[`${t}-footer`]:{textAlign:"end",background:e.footerBg,marginTop:e.footerMarginTop,padding:e.footerPadding,borderTop:e.footerBorderTop,borderRadius:e.footerBorderRadius,[`> ${e.antCls}-btn + ${e.antCls}-btn`]:{marginInlineStart:e.marginXS}},[`${t}-open`]:{overflow:"hidden"}})},{[`${t}-pure-panel`]:{top:"auto",padding:0,display:"flex",flexDirection:"column",[`${t}-content,
          ${t}-body,
          ${t}-confirm-body-wrapper`]:{display:"flex",flexDirection:"column",flex:"auto"},[`${t}-confirm-body`]:{marginBottom:"auto"}}}]},js=e=>{const{componentCls:t}=e;return{[`${t}-root`]:{[`${t}-wrap-rtl`]:{direction:"rtl",[`${t}-confirm-body`]:{direction:"rtl"}}}}},$a=e=>{const t=e.padding,n=e.fontSizeHeading5,o=e.lineHeightHeading5;return Fe(e,{modalHeaderHeight:e.calc(e.calc(o).mul(n).equal()).add(e.calc(t).mul(2).equal()).equal(),modalFooterBorderColorSplit:e.colorSplit,modalFooterBorderStyle:e.lineType,modalFooterBorderWidth:e.lineWidth,modalCloseIconColor:e.colorIcon,modalCloseIconHoverColor:e.colorIconHover,modalCloseBtnSize:e.controlHeight,modalConfirmIconSize:e.fontHeight,modalTitleHeight:e.calc(e.titleFontSize).mul(e.titleLineHeight).equal()})},Sa=e=>({footerBg:"transparent",headerBg:e.colorBgElevated,titleLineHeight:e.lineHeightHeading5,titleFontSize:e.fontSizeHeading5,contentBg:e.colorBgElevated,titleColor:e.colorTextHeading,contentPadding:e.wireframe?0:`${L(e.paddingMD)} ${L(e.paddingContentHorizontalLG)}`,headerPadding:e.wireframe?`${L(e.padding)} ${L(e.paddingLG)}`:0,headerBorderBottom:e.wireframe?`${L(e.lineWidth)} ${e.lineType} ${e.colorSplit}`:"none",headerMarginBottom:e.wireframe?0:e.marginXS,bodyPadding:e.wireframe?e.paddingLG:0,footerPadding:e.wireframe?`${L(e.paddingXS)} ${L(e.padding)}`:0,footerBorderTop:e.wireframe?`${L(e.lineWidth)} ${e.lineType} ${e.colorSplit}`:"none",footerBorderRadius:e.wireframe?`0 0 ${L(e.borderRadiusLG)} ${L(e.borderRadiusLG)}`:0,footerMarginTop:e.wireframe?0:e.marginSM,confirmBodyPadding:e.wireframe?`${L(e.padding*2)} ${L(e.padding*2)} ${L(e.paddingLG)}`:0,confirmIconMarginInlineEnd:e.wireframe?e.margin:e.marginSM,confirmBtnsMarginTop:e.wireframe?e.marginLG:e.marginSM}),wa=We("Modal",e=>{const t=$a(e);return[Rs(t),js(t),ya(t),Ht(t,"zoom")]},Sa,{unitless:{titleLineHeight:!0}});var Ms=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n};let Zn;const Ts=e=>{Zn={x:e.pageX,y:e.pageY},setTimeout(()=>{Zn=null},100)};ns()&&document.documentElement.addEventListener("click",Ts,!0);const xa=e=>{var t;const{getPopupContainer:n,getPrefixCls:o,direction:r,modal:i}=a.useContext(ue),l=A=>{const{onCancel:_}=e;_==null||_(A)},s=A=>{const{onOk:_}=e;_==null||_(A)},{prefixCls:c,className:d,rootClassName:u,open:m,wrapClassName:f,centered:p,getContainer:g,focusTriggerAfterClose:v=!0,style:$,visible:h,width:b=520,footer:y,classNames:S,styles:C,children:w,loading:x}=e,O=Ms(e,["prefixCls","className","rootClassName","open","wrapClassName","centered","getContainer","focusTriggerAfterClose","style","visible","width","footer","classNames","styles","children","loading"]),I=o("modal",c),P=o(),j=ut(I),[N,E,R]=wa(I,j),z=M(f,{[`${I}-centered`]:!!p,[`${I}-wrap-rtl`]:r==="rtl"}),D=y!==null&&!x?a.createElement(Ca,Object.assign({},e,{onOk:s,onCancel:l})):null,[T,K]=ts(dr(e),dr(i),{closable:!0,closeIcon:a.createElement(_t,{className:`${I}-close-icon`}),closeIconRender:A=>ba(I,A)}),U=Es(`.${I}-content`),[V,F]=At("Modal",O.zIndex);return N(a.createElement(na,{form:!0,space:!0},a.createElement(Ur.Provider,{value:F},a.createElement(So,Object.assign({width:b},O,{zIndex:V,getContainer:g===void 0?n:g,prefixCls:I,rootClassName:M(E,u,R,j),footer:D,visible:m??h,mousePosition:(t=O.mousePosition)!==null&&t!==void 0?t:Zn,onClose:l,closable:T,closeIcon:K,focusTriggerAfterClose:v,transitionName:at(P,"zoom",e.transitionName),maskTransitionName:at(P,"fade",e.maskTransitionName),className:M(E,d,i==null?void 0:i.className),style:Object.assign(Object.assign({},i==null?void 0:i.style),$),classNames:Object.assign(Object.assign(Object.assign({},i==null?void 0:i.classNames),S),{wrapper:M(z,S==null?void 0:S.wrapper)}),styles:Object.assign(Object.assign({},i==null?void 0:i.styles),C),panelRef:U}),x?a.createElement(it,{active:!0,title:!1,paragraph:{rows:4},className:`${I}-body-skeleton`}):w))))},ks=e=>{const{componentCls:t,titleFontSize:n,titleLineHeight:o,modalConfirmIconSize:r,fontSize:i,lineHeight:l,modalTitleHeight:s,fontHeight:c,confirmBodyPadding:d}=e,u=`${t}-confirm`;return{[u]:{"&-rtl":{direction:"rtl"},[`${e.antCls}-modal-header`]:{display:"none"},[`${u}-body-wrapper`]:Object.assign({},Lt()),[`&${t} ${t}-body`]:{padding:d},[`${u}-body`]:{display:"flex",flexWrap:"nowrap",alignItems:"start",[`> ${e.iconCls}`]:{flex:"none",fontSize:r,marginInlineEnd:e.confirmIconMarginInlineEnd,marginTop:e.calc(e.calc(c).sub(r).equal()).div(2).equal()},[`&-has-title > ${e.iconCls}`]:{marginTop:e.calc(e.calc(s).sub(r).equal()).div(2).equal()}},[`${u}-paragraph`]:{display:"flex",flexDirection:"column",flex:"auto",rowGap:e.marginXS},[`${e.iconCls} + ${u}-paragraph`]:{maxWidth:`calc(100% - ${L(e.calc(e.modalConfirmIconSize).add(e.marginSM).equal())})`},[`${u}-title`]:{color:e.colorTextHeading,fontWeight:e.fontWeightStrong,fontSize:n,lineHeight:o},[`${u}-content`]:{color:e.colorText,fontSize:i,lineHeight:l},[`${u}-btns`]:{textAlign:"end",marginTop:e.confirmBtnsMarginTop,[`${e.antCls}-btn + ${e.antCls}-btn`]:{marginBottom:0,marginInlineStart:e.marginXS}}},[`${u}-error ${u}-body > ${e.iconCls}`]:{color:e.colorError},[`${u}-warning ${u}-body > ${e.iconCls},
        ${u}-confirm ${u}-body > ${e.iconCls}`]:{color:e.colorWarning},[`${u}-info ${u}-body > ${e.iconCls}`]:{color:e.colorInfo},[`${u}-success ${u}-body > ${e.iconCls}`]:{color:e.colorSuccess}}},zs=Ji(["Modal","confirm"],e=>{const t=$a(e);return[ks(t)]},Sa,{order:-1e3});var Bs=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n};function Ia(e){const{prefixCls:t,icon:n,okText:o,cancelText:r,confirmPrefixCls:i,type:l,okCancel:s,footer:c,locale:d}=e,u=Bs(e,["prefixCls","icon","okText","cancelText","confirmPrefixCls","type","okCancel","footer","locale"]);let m=n;if(!n&&n!==null)switch(l){case"info":m=a.createElement(tl,null);break;case"success":m=a.createElement(Yr,null);break;case"error":m=a.createElement(Gr,null);break;default:m=a.createElement(el,null)}const f=s??l==="confirm",p=e.autoFocusButton===null?!1:e.autoFocusButton||"ok",[g]=Cn("Modal"),v=d||g,$=o||(f?v==null?void 0:v.okText:v==null?void 0:v.justOkText),h=r||(v==null?void 0:v.cancelText),b=Object.assign({autoFocusButton:p,cancelTextLocale:h,okTextLocale:$,mergedOkCancel:f},u),y=a.useMemo(()=>b,ve(Object.values(b))),S=a.createElement(a.Fragment,null,a.createElement(ar,null),a.createElement(ir,null)),C=e.title!==void 0&&e.title!==null,w=`${i}-body`;return a.createElement("div",{className:`${i}-body-wrapper`},a.createElement("div",{className:M(w,{[`${w}-has-title`]:C})},m,a.createElement("div",{className:`${i}-paragraph`},C&&a.createElement("span",{className:`${i}-title`},e.title),a.createElement("div",{className:`${i}-content`},e.content))),c===void 0||typeof c=="function"?a.createElement(ga,{value:y},a.createElement("div",{className:`${i}-btns`},typeof c=="function"?c(S,{OkBtn:ir,CancelBtn:ar}):S)):c,a.createElement(zs,{prefixCls:t}))}const Ls=e=>{const{close:t,zIndex:n,afterClose:o,open:r,keyboard:i,centered:l,getContainer:s,maskStyle:c,direction:d,prefixCls:u,wrapClassName:m,rootPrefixCls:f,bodyStyle:p,closable:g=!1,closeIcon:v,modalRender:$,focusTriggerAfterClose:h,onConfirm:b,styles:y}=e,S=`${u}-confirm`,C=e.width||416,w=e.style||{},x=e.mask===void 0?!0:e.mask,O=e.maskClosable===void 0?!1:e.maskClosable,I=M(S,`${S}-${e.type}`,{[`${S}-rtl`]:d==="rtl"},e.className),[,P]=fo(),j=a.useMemo(()=>n!==void 0?n:P.zIndexPopupBase+nl,[n,P]);return a.createElement(xa,{prefixCls:u,className:I,wrapClassName:M({[`${S}-centered`]:!!e.centered},m),onCancel:()=>{t==null||t({triggerCancel:!0}),b==null||b(!1)},open:r,title:"",footer:null,transitionName:at(f||"","zoom",e.transitionName),maskTransitionName:at(f||"","fade",e.maskTransitionName),mask:x,maskClosable:O,style:w,styles:Object.assign({body:p,mask:c},y),width:C,zIndex:j,afterClose:o,keyboard:i,centered:l,getContainer:s,closable:g,closeIcon:v,modalRender:$,focusTriggerAfterClose:h},a.createElement(Ia,Object.assign({},e,{confirmPrefixCls:S})))},Oa=e=>{const{rootPrefixCls:t,iconPrefixCls:n,direction:o,theme:r}=e;return a.createElement(Kr,{prefixCls:t,iconPrefixCls:n,direction:o,theme:r},a.createElement(Ls,Object.assign({},e)))},pt=[];let Ea="";function Pa(){return Ea}const Ds=e=>{var t,n;const{prefixCls:o,getContainer:r,direction:i}=e,l=qr(),s=a.useContext(ue),c=Pa()||s.getPrefixCls(),d=o||`${c}-modal`;let u=r;return u===!1&&(u=void 0),Z.createElement(Oa,Object.assign({},e,{rootPrefixCls:c,prefixCls:d,iconPrefixCls:s.iconPrefixCls,theme:s.theme,direction:i??s.direction,locale:(n=(t=s.locale)===null||t===void 0?void 0:t.Modal)!==null&&n!==void 0?n:l,getContainer:u}))};function Vt(e){const t=al(),n=document.createDocumentFragment();let o=Object.assign(Object.assign({},e),{close:s,open:!0}),r;function i(){for(var d,u=arguments.length,m=new Array(u),f=0;f<u;f++)m[f]=arguments[f];if(m.some(v=>v==null?void 0:v.triggerCancel)){var g;(d=e.onCancel)===null||d===void 0||(g=d).call.apply(g,[e,()=>{}].concat(ve(m.slice(1))))}for(let v=0;v<pt.length;v++)if(pt[v]===s){pt.splice(v,1);break}ol(n)}function l(d){clearTimeout(r),r=setTimeout(()=>{const u=t.getPrefixCls(void 0,Pa()),m=t.getIconPrefixCls(),f=t.getTheme(),p=Z.createElement(Ds,Object.assign({},d));rl(Z.createElement(Kr,{prefixCls:u,iconPrefixCls:m,theme:f},t.holderRender?t.holderRender(p):p),n)})}function s(){for(var d=arguments.length,u=new Array(d),m=0;m<d;m++)u[m]=arguments[m];o=Object.assign(Object.assign({},o),{open:!1,afterClose:()=>{typeof e.afterClose=="function"&&e.afterClose(),i.apply(this,u)}}),o.visible&&delete o.visible,l(o)}function c(d){typeof d=="function"?o=d(o):o=Object.assign(Object.assign({},o),d),l(o)}return l(o),pt.push(s),{destroy:s,update:c}}function Na(e){return Object.assign(Object.assign({},e),{type:"warning"})}function Ra(e){return Object.assign(Object.assign({},e),{type:"info"})}function ja(e){return Object.assign(Object.assign({},e),{type:"success"})}function Ma(e){return Object.assign(Object.assign({},e),{type:"error"})}function Ta(e){return Object.assign(Object.assign({},e),{type:"confirm"})}function _s(e){let{rootPrefixCls:t}=e;Ea=t}var As=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n};const Hs=(e,t)=>{var n,{afterClose:o,config:r}=e,i=As(e,["afterClose","config"]);const[l,s]=a.useState(!0),[c,d]=a.useState(r),{direction:u,getPrefixCls:m}=a.useContext(ue),f=m("modal"),p=m(),g=()=>{var b;o(),(b=c.afterClose)===null||b===void 0||b.call(c)},v=function(){var b;s(!1);for(var y=arguments.length,S=new Array(y),C=0;C<y;C++)S[C]=arguments[C];if(S.some(O=>O==null?void 0:O.triggerCancel)){var x;(b=c.onCancel)===null||b===void 0||(x=b).call.apply(x,[c,()=>{}].concat(ve(S.slice(1))))}};a.useImperativeHandle(t,()=>({destroy:v,update:b=>{d(y=>Object.assign(Object.assign({},y),b))}}));const $=(n=c.okCancel)!==null&&n!==void 0?n:c.type==="confirm",[h]=Cn("Modal",rn.Modal);return a.createElement(Oa,Object.assign({prefixCls:f,rootPrefixCls:p},c,{close:v,open:l,afterClose:g,okText:c.okText||($?h==null?void 0:h.okText:h==null?void 0:h.justOkText),direction:c.direction||u,cancelText:c.cancelText||(h==null?void 0:h.cancelText)},i))},Fs=a.forwardRef(Hs);let br=0;const Ws=a.memo(a.forwardRef((e,t)=>{const[n,o]=Kl();return a.useImperativeHandle(t,()=>({patchElement:o}),[]),a.createElement(a.Fragment,null,n)}));function Vs(){const e=a.useRef(null),[t,n]=a.useState([]);a.useEffect(()=>{t.length&&(ve(t).forEach(l=>{l()}),n([]))},[t]);const o=a.useCallback(i=>function(s){var c;br+=1;const d=a.createRef();let u;const m=new Promise($=>{u=$});let f=!1,p;const g=a.createElement(Fs,{key:`modal-${br}`,config:i(s),ref:d,afterClose:()=>{p==null||p()},isSilent:()=>f,onConfirm:$=>{u($)}});return p=(c=e.current)===null||c===void 0?void 0:c.patchElement(g),p&&pt.push(p),{destroy:()=>{function $(){var h;(h=d.current)===null||h===void 0||h.destroy()}d.current?$():n(h=>[].concat(ve(h),[$]))},update:$=>{function h(){var b;(b=d.current)===null||b===void 0||b.update($)}d.current?h():n(b=>[].concat(ve(b),[h]))},then:$=>(f=!0,m.then($))}},[]);return[a.useMemo(()=>({info:o(Ra),success:o(ja),error:o(Ma),warning:o(Na),confirm:o(Ta)}),[]),a.createElement(Ws,{key:"modal-holder",ref:e})]}function ka(){const[,e]=a.useReducer(t=>t+1,0);return e}function Xs(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;const t=a.useRef({}),n=ka(),o=Il();return il(()=>{const r=o.subscribe(i=>{t.current=i,e&&n()});return()=>o.unsubscribe(r)},[]),t.current}const Qn=a.createContext({}),qs=e=>{const{antCls:t,componentCls:n,iconCls:o,avatarBg:r,avatarColor:i,containerSize:l,containerSizeLG:s,containerSizeSM:c,textFontSize:d,textFontSizeLG:u,textFontSizeSM:m,borderRadius:f,borderRadiusLG:p,borderRadiusSM:g,lineWidth:v,lineType:$}=e,h=(b,y,S)=>({width:b,height:b,borderRadius:"50%",[`&${n}-square`]:{borderRadius:S},[`&${n}-icon`]:{fontSize:y,[`> ${o}`]:{margin:0}}});return{[n]:Object.assign(Object.assign(Object.assign(Object.assign({},tt(e)),{position:"relative",display:"inline-flex",justifyContent:"center",alignItems:"center",overflow:"hidden",color:i,whiteSpace:"nowrap",textAlign:"center",verticalAlign:"middle",background:r,border:`${L(v)} ${$} transparent`,"&-image":{background:"transparent"},[`${t}-image-img`]:{display:"block"}}),h(l,d,f)),{"&-lg":Object.assign({},h(s,u,p)),"&-sm":Object.assign({},h(c,m,g)),"> img":{display:"block",width:"100%",height:"100%",objectFit:"cover"}})}},Us=e=>{const{componentCls:t,groupBorderColor:n,groupOverlapping:o,groupSpace:r}=e;return{[`${t}-group`]:{display:"inline-flex",[t]:{borderColor:n},"> *:not(:first-child)":{marginInlineStart:o}},[`${t}-group-popover`]:{[`${t} + ${t}`]:{marginInlineStart:r}}}},Ks=e=>{const{controlHeight:t,controlHeightLG:n,controlHeightSM:o,fontSize:r,fontSizeLG:i,fontSizeXL:l,fontSizeHeading3:s,marginXS:c,marginXXS:d,colorBorderBg:u}=e;return{containerSize:t,containerSizeLG:n,containerSizeSM:o,textFontSize:Math.round((i+l)/2),textFontSizeLG:s,textFontSizeSM:r,groupSpace:d,groupOverlapping:-c,groupBorderColor:u}},za=We("Avatar",e=>{const{colorTextLightSolid:t,colorTextPlaceholder:n}=e,o=Fe(e,{avatarBg:n,avatarColor:t});return[qs(o),Us(o)]},Ks);var Gs=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n};const Ys=(e,t)=>{const[n,o]=a.useState(1),[r,i]=a.useState(!1),[l,s]=a.useState(!0),c=a.useRef(null),d=a.useRef(null),u=go(t,c),{getPrefixCls:m,avatar:f}=a.useContext(ue),p=a.useContext(Qn),g=()=>{if(!d.current||!c.current)return;const H=d.current.offsetWidth,W=c.current.offsetWidth;if(H!==0&&W!==0){const{gap:G=4}=e;G*2<W&&o(W-G*2<H?(W-G*2)/H:1)}};a.useEffect(()=>{i(!0)},[]),a.useEffect(()=>{s(!0),o(1)},[e.src]),a.useEffect(g,[e.gap]);const v=()=>{const{onError:H}=e;(H==null?void 0:H())!==!1&&s(!1)},{prefixCls:$,shape:h,size:b,src:y,srcSet:S,icon:C,className:w,rootClassName:x,alt:O,draggable:I,children:P,crossOrigin:j}=e,N=Gs(e,["prefixCls","shape","size","src","srcSet","icon","className","rootClassName","alt","draggable","children","crossOrigin"]),E=oa(H=>{var W,G;return(G=(W=b??(p==null?void 0:p.size))!==null&&W!==void 0?W:H)!==null&&G!==void 0?G:"default"}),R=Object.keys(typeof E=="object"?E||{}:{}).some(H=>["xs","sm","md","lg","xl","xxl"].includes(H)),z=Xs(R),D=a.useMemo(()=>{if(typeof E!="object")return{};const H=Ol.find(G=>z[G]),W=E[H];return W?{width:W,height:W,fontSize:W&&(C||P)?W/2:18}:{}},[z,E]),T=m("avatar",$),K=ut(T),[U,V,F]=za(T,K),A=M({[`${T}-lg`]:E==="large",[`${T}-sm`]:E==="small"}),_=a.isValidElement(y),B=h||(p==null?void 0:p.shape)||"circle",X=M(T,A,f==null?void 0:f.className,`${T}-${B}`,{[`${T}-image`]:_||y&&l,[`${T}-icon`]:!!C},F,K,w,x,V),Q=typeof E=="number"?{width:E,height:E,fontSize:C?E/2:18}:{};let q;if(typeof y=="string"&&l)q=a.createElement("img",{src:y,draggable:I,srcSet:S,onError:v,alt:O,crossOrigin:j});else if(_)q=y;else if(C)q=C;else if(r||n!==1){const H=`scale(${n})`,W={msTransform:H,WebkitTransform:H,transform:H};q=a.createElement(El,{onResize:g},a.createElement("span",{className:`${T}-string`,ref:d,style:Object.assign({},W)},P))}else q=a.createElement("span",{className:`${T}-string`,style:{opacity:0},ref:d},P);return delete N.onError,delete N.gap,U(a.createElement("span",Object.assign({},N,{style:Object.assign(Object.assign(Object.assign(Object.assign({},Q),D),f==null?void 0:f.style),N.style),className:X,ref:u}),q))},Ba=a.forwardRef(Ys),ln=e=>e?typeof e=="function"?e():e:null,Zs=e=>{const{componentCls:t,popoverColor:n,titleMinWidth:o,fontWeightStrong:r,innerPadding:i,boxShadowSecondary:l,colorTextHeading:s,borderRadiusLG:c,zIndexPopup:d,titleMarginBottom:u,colorBgElevated:m,popoverBg:f,titleBorderBottom:p,innerContentPadding:g,titlePadding:v}=e;return[{[t]:Object.assign(Object.assign({},tt(e)),{position:"absolute",top:0,left:{_skip_check_:!0,value:0},zIndex:d,fontWeight:"normal",whiteSpace:"normal",textAlign:"start",cursor:"auto",userSelect:"text","--valid-offset-x":"var(--arrow-offset-horizontal, var(--arrow-x))",transformOrigin:["var(--valid-offset-x, 50%)","var(--arrow-y, 50%)"].join(" "),"--antd-arrow-background-color":m,width:"max-content",maxWidth:"100vw","&-rtl":{direction:"rtl"},"&-hidden":{display:"none"},[`${t}-content`]:{position:"relative"},[`${t}-inner`]:{backgroundColor:f,backgroundClip:"padding-box",borderRadius:c,boxShadow:l,padding:i},[`${t}-title`]:{minWidth:o,marginBottom:u,color:s,fontWeight:r,borderBottom:p,padding:v},[`${t}-inner-content`]:{color:n,padding:g}})},ra(e,"var(--antd-arrow-background-color)"),{[`${t}-pure`]:{position:"relative",maxWidth:"none",margin:e.sizePopupArrow,display:"inline-block",[`${t}-content`]:{display:"inline-block"}}}]},Qs=e=>{const{componentCls:t}=e;return{[t]:Pl.map(n=>{const o=e[`${n}6`];return{[`&${t}-${n}`]:{"--antd-arrow-background-color":o,[`${t}-inner`]:{backgroundColor:o},[`${t}-arrow`]:{background:"transparent"}}}})}},Js=e=>{const{lineWidth:t,controlHeight:n,fontHeight:o,padding:r,wireframe:i,zIndexPopupBase:l,borderRadiusLG:s,marginXS:c,lineType:d,colorSplit:u,paddingSM:m}=e,f=n-o,p=f/2,g=f/2-t,v=r;return Object.assign(Object.assign(Object.assign({titleMinWidth:177,zIndexPopup:l+30},aa(e)),ia({contentRadius:s,limitVerticalRadius:!0})),{innerPadding:i?0:12,titleMarginBottom:i?0:c,titlePadding:i?`${p}px ${v}px ${g}px`:0,titleBorderBottom:i?`${t}px ${d} ${u}`:"none",innerContentPadding:i?`${m}px ${v}px`:0})},La=We("Popover",e=>{const{colorBgElevated:t,colorText:n}=e,o=Fe(e,{popoverBg:t,popoverColor:n});return[Zs(o),Qs(o),Ht(o,"zoom-big")]},Js,{resetStyle:!1,deprecatedTokens:[["width","titleMinWidth"],["minWidth","titleMinWidth"]]});var ec=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n};const Da=e=>{let{title:t,content:n,prefixCls:o}=e;return!t&&!n?null:a.createElement(a.Fragment,null,t&&a.createElement("div",{className:`${o}-title`},t),n&&a.createElement("div",{className:`${o}-inner-content`},n))},tc=e=>{const{hashId:t,prefixCls:n,className:o,style:r,placement:i="top",title:l,content:s,children:c}=e,d=ln(l),u=ln(s),m=M(t,n,`${n}-pure`,`${n}-placement-${i}`,o);return a.createElement("div",{className:m,style:r},a.createElement("div",{className:`${n}-arrow`}),a.createElement(Nl,Object.assign({},e,{className:t,prefixCls:n}),c||a.createElement(Da,{prefixCls:n,title:d,content:u})))},nc=e=>{const{prefixCls:t,className:n}=e,o=ec(e,["prefixCls","className"]),{getPrefixCls:r}=a.useContext(ue),i=r("popover",t),[l,s,c]=La(i);return l(a.createElement(tc,Object.assign({},o,{prefixCls:i,hashId:s,className:M(n,c)})))};var oc=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n};const rc=a.forwardRef((e,t)=>{var n,o;const{prefixCls:r,title:i,content:l,overlayClassName:s,placement:c="top",trigger:d="hover",children:u,mouseEnterDelay:m=.1,mouseLeaveDelay:f=.1,onOpenChange:p,overlayStyle:g={}}=e,v=oc(e,["prefixCls","title","content","overlayClassName","placement","trigger","children","mouseEnterDelay","mouseLeaveDelay","onOpenChange","overlayStyle"]),{getPrefixCls:$}=a.useContext(ue),h=$("popover",r),[b,y,S]=La(h),C=$(),w=M(s,y,S),[x,O]=Ve(!1,{value:(n=e.open)!==null&&n!==void 0?n:e.visible,defaultValue:(o=e.defaultOpen)!==null&&o!==void 0?o:e.defaultVisible}),I=(R,z)=>{O(R,!0),p==null||p(R,z)},P=R=>{R.keyCode===Ee.ESC&&I(!1,R)},j=R=>{I(R)},N=ln(i),E=ln(l);return b(a.createElement(yn,Object.assign({placement:c,trigger:d,mouseEnterDelay:m,mouseLeaveDelay:f,overlayStyle:g},v,{prefixCls:h,overlayClassName:w,ref:t,open:x,onOpenChange:j,overlay:N||E?a.createElement(Da,{prefixCls:h,title:N,content:E}):null,transitionName:at(C,"zoom-big",v.transitionName),"data-popover-inject":!0}),Je(u,{onKeyDown:R=>{var z,D;a.isValidElement(u)&&((D=u==null?void 0:(z=u.props).onKeyDown)===null||D===void 0||D.call(z,R)),P(R)}})))}),_a=rc;_a._InternalPanelDoNotUseOrYouWillBeFired=nc;const Cr=e=>{const{size:t,shape:n}=a.useContext(Qn),o=a.useMemo(()=>({size:e.size||t,shape:e.shape||n}),[e.size,e.shape,t,n]);return a.createElement(Qn.Provider,{value:o},e.children)},ac=e=>{var t,n,o;const{getPrefixCls:r,direction:i}=a.useContext(ue),{prefixCls:l,className:s,rootClassName:c,style:d,maxCount:u,maxStyle:m,size:f,shape:p,maxPopoverPlacement:g,maxPopoverTrigger:v,children:$,max:h}=e,b=r("avatar",l),y=`${b}-group`,S=ut(b),[C,w,x]=za(b,S),O=M(y,{[`${y}-rtl`]:i==="rtl"},x,S,s,c,w),I=Ft($).map((N,E)=>Je(N,{key:`avatar-key-${E}`})),P=(h==null?void 0:h.count)||u,j=I.length;if(P&&P<j){const N=I.slice(0,P),E=I.slice(P,j),R=(h==null?void 0:h.style)||m,z=((t=h==null?void 0:h.popover)===null||t===void 0?void 0:t.trigger)||v||"hover",D=((n=h==null?void 0:h.popover)===null||n===void 0?void 0:n.placement)||g||"top",T=Object.assign(Object.assign({content:E},h==null?void 0:h.popover),{overlayClassName:M(`${y}-popover`,(o=h==null?void 0:h.popover)===null||o===void 0?void 0:o.overlayClassName),placement:D,trigger:z});return N.push(a.createElement(_a,Object.assign({key:"avatar-popover-key",destroyTooltipOnHide:!0},T),a.createElement(Ba,{style:R},`+${j-P}`))),C(a.createElement(Cr,{shape:p,size:f},a.createElement("div",{className:O,style:d},N)))}return C(a.createElement(Cr,{shape:p,size:f},a.createElement("div",{className:O,style:d},I)))},Aa=Ba;Aa.Group=ac;const ic=new Xe("antStatusProcessing",{"0%":{transform:"scale(0.8)",opacity:.5},"100%":{transform:"scale(2.4)",opacity:0}}),lc=new Xe("antZoomBadgeIn",{"0%":{transform:"scale(0) translate(50%, -50%)",opacity:0},"100%":{transform:"scale(1) translate(50%, -50%)"}}),sc=new Xe("antZoomBadgeOut",{"0%":{transform:"scale(1) translate(50%, -50%)"},"100%":{transform:"scale(0) translate(50%, -50%)",opacity:0}}),cc=new Xe("antNoWrapperZoomBadgeIn",{"0%":{transform:"scale(0)",opacity:0},"100%":{transform:"scale(1)"}}),dc=new Xe("antNoWrapperZoomBadgeOut",{"0%":{transform:"scale(1)"},"100%":{transform:"scale(0)",opacity:0}}),uc=new Xe("antBadgeLoadingCircle",{"0%":{transformOrigin:"50%"},"100%":{transform:"translate(50%, -50%) rotate(360deg)",transformOrigin:"50%"}}),mc=e=>{const{componentCls:t,iconCls:n,antCls:o,badgeShadowSize:r,motionDurationSlow:i,textFontSize:l,textFontSizeSM:s,statusSize:c,dotSize:d,textFontWeight:u,indicatorHeight:m,indicatorHeightSM:f,marginXS:p,calc:g}=e,v=`${o}-scroll-number`,$=la(e,(h,b)=>{let{darkColor:y}=b;return{[`&${t} ${t}-color-${h}`]:{background:y,[`&:not(${t}-count)`]:{color:y},"a:hover &":{background:y}}}});return{[t]:Object.assign(Object.assign(Object.assign(Object.assign({},tt(e)),{position:"relative",display:"inline-block",width:"fit-content",lineHeight:1,[`${t}-count`]:{display:"inline-flex",justifyContent:"center",zIndex:e.indicatorZIndex,minWidth:m,height:m,color:e.badgeTextColor,fontWeight:u,fontSize:l,lineHeight:L(m),whiteSpace:"nowrap",textAlign:"center",background:e.badgeColor,borderRadius:g(m).div(2).equal(),boxShadow:`0 0 0 ${L(r)} ${e.badgeShadowColor}`,transition:`background ${e.motionDurationMid}`,a:{color:e.badgeTextColor},"a:hover":{color:e.badgeTextColor},"a:hover &":{background:e.badgeColorHover}},[`${t}-count-sm`]:{minWidth:f,height:f,fontSize:s,lineHeight:L(f),borderRadius:g(f).div(2).equal()},[`${t}-multiple-words`]:{padding:`0 ${L(e.paddingXS)}`,bdi:{unicodeBidi:"plaintext"}},[`${t}-dot`]:{zIndex:e.indicatorZIndex,width:d,minWidth:d,height:d,background:e.badgeColor,borderRadius:"100%",boxShadow:`0 0 0 ${L(r)} ${e.badgeShadowColor}`},[`${t}-dot${v}`]:{transition:`background ${i}`},[`${t}-count, ${t}-dot, ${v}-custom-component`]:{position:"absolute",top:0,insetInlineEnd:0,transform:"translate(50%, -50%)",transformOrigin:"100% 0%",[`&${n}-spin`]:{animationName:uc,animationDuration:"1s",animationIterationCount:"infinite",animationTimingFunction:"linear"}},[`&${t}-status`]:{lineHeight:"inherit",verticalAlign:"baseline",[`${t}-status-dot`]:{position:"relative",top:-1,display:"inline-block",width:c,height:c,verticalAlign:"middle",borderRadius:"50%"},[`${t}-status-success`]:{backgroundColor:e.colorSuccess},[`${t}-status-processing`]:{overflow:"visible",color:e.colorInfo,backgroundColor:e.colorInfo,borderColor:"currentcolor","&::after":{position:"absolute",top:0,insetInlineStart:0,width:"100%",height:"100%",borderWidth:r,borderStyle:"solid",borderColor:"inherit",borderRadius:"50%",animationName:ic,animationDuration:e.badgeProcessingDuration,animationIterationCount:"infinite",animationTimingFunction:"ease-in-out",content:'""'}},[`${t}-status-default`]:{backgroundColor:e.colorTextPlaceholder},[`${t}-status-error`]:{backgroundColor:e.colorError},[`${t}-status-warning`]:{backgroundColor:e.colorWarning},[`${t}-status-text`]:{marginInlineStart:p,color:e.colorText,fontSize:e.fontSize}}}),$),{[`${t}-zoom-appear, ${t}-zoom-enter`]:{animationName:lc,animationDuration:e.motionDurationSlow,animationTimingFunction:e.motionEaseOutBack,animationFillMode:"both"},[`${t}-zoom-leave`]:{animationName:sc,animationDuration:e.motionDurationSlow,animationTimingFunction:e.motionEaseOutBack,animationFillMode:"both"},[`&${t}-not-a-wrapper`]:{[`${t}-zoom-appear, ${t}-zoom-enter`]:{animationName:cc,animationDuration:e.motionDurationSlow,animationTimingFunction:e.motionEaseOutBack},[`${t}-zoom-leave`]:{animationName:dc,animationDuration:e.motionDurationSlow,animationTimingFunction:e.motionEaseOutBack},[`&:not(${t}-status)`]:{verticalAlign:"middle"},[`${v}-custom-component, ${t}-count`]:{transform:"none"},[`${v}-custom-component, ${v}`]:{position:"relative",top:"auto",display:"block",transformOrigin:"50% 50%"}},[v]:{overflow:"hidden",[`${v}-only`]:{position:"relative",display:"inline-block",height:m,transition:`all ${e.motionDurationSlow} ${e.motionEaseOutBack}`,WebkitTransformStyle:"preserve-3d",WebkitBackfaceVisibility:"hidden",[`> p${v}-only-unit`]:{height:m,margin:0,WebkitTransformStyle:"preserve-3d",WebkitBackfaceVisibility:"hidden"}},[`${v}-symbol`]:{verticalAlign:"top"}},"&-rtl":{direction:"rtl",[`${t}-count, ${t}-dot, ${v}-custom-component`]:{transform:"translate(-50%, -50%)"}}})}},Ha=e=>{const{fontHeight:t,lineWidth:n,marginXS:o,colorBorderBg:r}=e,i=t,l=n,s=e.colorBgContainer,c=e.colorError,d=e.colorErrorHover;return Fe(e,{badgeFontHeight:i,badgeShadowSize:l,badgeTextColor:s,badgeColor:c,badgeColorHover:d,badgeShadowColor:r,badgeProcessingDuration:"1.2s",badgeRibbonOffset:o,badgeRibbonCornerTransform:"scaleY(0.75)",badgeRibbonCornerFilter:"brightness(75%)"})},Fa=e=>{const{fontSize:t,lineHeight:n,fontSizeSM:o,lineWidth:r}=e;return{indicatorZIndex:"auto",indicatorHeight:Math.round(t*n)-2*r,indicatorHeightSM:t,dotSize:o/2,textFontSize:o,textFontSizeSM:o,textFontWeight:"normal",statusSize:o/2}},fc=We("Badge",e=>{const t=Ha(e);return mc(t)},Fa),gc=e=>{const{antCls:t,badgeFontHeight:n,marginXS:o,badgeRibbonOffset:r,calc:i}=e,l=`${t}-ribbon`,s=`${t}-ribbon-wrapper`,c=la(e,(d,u)=>{let{darkColor:m}=u;return{[`&${l}-color-${d}`]:{background:m,color:m}}});return{[s]:{position:"relative"},[l]:Object.assign(Object.assign(Object.assign(Object.assign({},tt(e)),{position:"absolute",top:o,padding:`0 ${L(e.paddingXS)}`,color:e.colorPrimary,lineHeight:L(n),whiteSpace:"nowrap",backgroundColor:e.colorPrimary,borderRadius:e.borderRadiusSM,[`${l}-text`]:{color:e.colorTextLightSolid},[`${l}-corner`]:{position:"absolute",top:"100%",width:r,height:r,color:"currentcolor",border:`${L(i(r).div(2).equal())} solid`,transform:e.badgeRibbonCornerTransform,transformOrigin:"top",filter:e.badgeRibbonCornerFilter}}),c),{[`&${l}-placement-end`]:{insetInlineEnd:i(r).mul(-1).equal(),borderEndEndRadius:0,[`${l}-corner`]:{insetInlineEnd:0,borderInlineEndColor:"transparent",borderBlockEndColor:"transparent"}},[`&${l}-placement-start`]:{insetInlineStart:i(r).mul(-1).equal(),borderEndStartRadius:0,[`${l}-corner`]:{insetInlineStart:0,borderBlockEndColor:"transparent",borderInlineStartColor:"transparent"}},"&-rtl":{direction:"rtl"}})}},pc=We(["Badge","Ribbon"],e=>{const t=Ha(e);return gc(t)},Fa),vc=e=>{const{className:t,prefixCls:n,style:o,color:r,children:i,text:l,placement:s="end",rootClassName:c}=e,{getPrefixCls:d,direction:u}=a.useContext(ue),m=d("ribbon",n),f=`${m}-wrapper`,[p,g,v]=pc(m,f),$=sa(r,!1),h=M(m,`${m}-placement-${s}`,{[`${m}-rtl`]:u==="rtl",[`${m}-color-${r}`]:$},t),b={},y={};return r&&!$&&(b.background=r,y.color=r),p(a.createElement("div",{className:M(f,c,g,v)},i,a.createElement("div",{className:M(h,g),style:Object.assign(Object.assign({},b),o)},a.createElement("span",{className:`${m}-text`},l),a.createElement("div",{className:`${m}-corner`,style:y}))))},yr=e=>{const{prefixCls:t,value:n,current:o,offset:r=0}=e;let i;return r&&(i={position:"absolute",top:`${r}00%`,left:0}),a.createElement("span",{style:i,className:M(`${t}-only-unit`,{current:o})},n)};function hc(e,t,n){let o=e,r=0;for(;(o+10)%10!==t;)o+=n,r+=n;return r}const bc=e=>{const{prefixCls:t,count:n,value:o}=e,r=Number(o),i=Math.abs(n),[l,s]=a.useState(r),[c,d]=a.useState(i),u=()=>{s(r),d(i)};a.useEffect(()=>{const p=setTimeout(u,1e3);return()=>clearTimeout(p)},[r]);let m,f;if(l===r||Number.isNaN(r)||Number.isNaN(l))m=[a.createElement(yr,Object.assign({},e,{key:r,current:!0}))],f={transition:"none"};else{m=[];const p=r+10,g=[];for(let h=r;h<=p;h+=1)g.push(h);const v=g.findIndex(h=>h%10===l);m=g.map((h,b)=>{const y=h%10;return a.createElement(yr,Object.assign({},e,{key:h,value:y,offset:b-v,current:b===v}))});const $=c<i?1:-1;f={transform:`translateY(${-hc(l,r,$)}00%)`}}return a.createElement("span",{className:`${t}-only`,style:f,onTransitionEnd:u},m)};var Cc=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n};const yc=a.forwardRef((e,t)=>{const{prefixCls:n,count:o,className:r,motionClassName:i,style:l,title:s,show:c,component:d="sup",children:u}=e,m=Cc(e,["prefixCls","count","className","motionClassName","style","title","show","component","children"]),{getPrefixCls:f}=a.useContext(ue),p=f("scroll-number",n),g=Object.assign(Object.assign({},m),{"data-show":c,style:l,className:M(p,r,i),title:s});let v=o;if(o&&Number(o)%1===0){const $=String(o).split("");v=a.createElement("bdi",null,$.map((h,b)=>a.createElement(bc,{prefixCls:p,count:Number(o),value:h,key:$.length-b})))}return l!=null&&l.borderColor&&(g.style=Object.assign(Object.assign({},l),{boxShadow:`0 0 0 1px ${l.borderColor} inset`})),u?Je(u,$=>({className:M(`${p}-custom-component`,$==null?void 0:$.className,i)})):a.createElement(d,Object.assign({},g,{ref:t}),v)});var $c=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n};const Sc=a.forwardRef((e,t)=>{var n,o,r,i,l;const{prefixCls:s,scrollNumberPrefixCls:c,children:d,status:u,text:m,color:f,count:p=null,overflowCount:g=99,dot:v=!1,size:$="default",title:h,offset:b,style:y,className:S,rootClassName:C,classNames:w,styles:x,showZero:O=!1}=e,I=$c(e,["prefixCls","scrollNumberPrefixCls","children","status","text","color","count","overflowCount","dot","size","title","offset","style","className","rootClassName","classNames","styles","showZero"]),{getPrefixCls:P,direction:j,badge:N}=a.useContext(ue),E=P("badge",s),[R,z,D]=fc(E),T=p>g?`${g}+`:p,K=T==="0"||T===0,U=p===null||K&&!O,V=(u!=null||f!=null)&&U,F=v&&!K,A=F?"":T,_=a.useMemo(()=>(A==null||A===""||K&&!O)&&!F,[A,K,O,F]),B=a.useRef(p);_||(B.current=p);const X=B.current,Q=a.useRef(A);_||(Q.current=A);const q=Q.current,H=a.useRef(F);_||(H.current=F);const W=a.useMemo(()=>{if(!b)return Object.assign(Object.assign({},N==null?void 0:N.style),y);const he={marginTop:b[1]};return j==="rtl"?he.left=parseInt(b[0],10):he.right=-parseInt(b[0],10),Object.assign(Object.assign(Object.assign({},he),N==null?void 0:N.style),y)},[j,b,y,N==null?void 0:N.style]),G=h??(typeof X=="string"||typeof X=="number"?X:void 0),le=_||!m?null:a.createElement("span",{className:`${E}-status-text`},m),ae=!X||typeof X!="object"?void 0:Je(X,he=>({style:Object.assign(Object.assign({},W),he.style)})),Y=sa(f,!1),ie=M(w==null?void 0:w.indicator,(n=N==null?void 0:N.classNames)===null||n===void 0?void 0:n.indicator,{[`${E}-status-dot`]:V,[`${E}-status-${u}`]:!!u,[`${E}-color-${f}`]:Y}),re={};f&&!Y&&(re.color=f,re.background=f);const xe=M(E,{[`${E}-status`]:V,[`${E}-not-a-wrapper`]:!d,[`${E}-rtl`]:j==="rtl"},S,C,N==null?void 0:N.className,(o=N==null?void 0:N.classNames)===null||o===void 0?void 0:o.root,w==null?void 0:w.root,z,D);if(!d&&V){const he=W.color;return R(a.createElement("span",Object.assign({},I,{className:xe,style:Object.assign(Object.assign(Object.assign({},x==null?void 0:x.root),(r=N==null?void 0:N.styles)===null||r===void 0?void 0:r.root),W)}),a.createElement("span",{className:ie,style:Object.assign(Object.assign(Object.assign({},x==null?void 0:x.indicator),(i=N==null?void 0:N.styles)===null||i===void 0?void 0:i.indicator),re)}),m&&a.createElement("span",{style:{color:he},className:`${E}-status-text`},m)))}return R(a.createElement("span",Object.assign({ref:t},I,{className:xe,style:Object.assign(Object.assign({},(l=N==null?void 0:N.styles)===null||l===void 0?void 0:l.root),x==null?void 0:x.root)}),d,a.createElement(bt,{visible:!_,motionName:`${E}-zoom`,motionAppear:!1,motionDeadline:1e3},he=>{let{className:$e}=he;var Ce,Se;const Pe=P("scroll-number",c),Re=H.current,Ne=M(w==null?void 0:w.indicator,(Ce=N==null?void 0:N.classNames)===null||Ce===void 0?void 0:Ce.indicator,{[`${E}-dot`]:Re,[`${E}-count`]:!Re,[`${E}-count-sm`]:$==="small",[`${E}-multiple-words`]:!Re&&q&&q.toString().length>1,[`${E}-status-${u}`]:!!u,[`${E}-color-${f}`]:Y});let pe=Object.assign(Object.assign(Object.assign({},x==null?void 0:x.indicator),(Se=N==null?void 0:N.styles)===null||Se===void 0?void 0:Se.indicator),W);return f&&!Y&&(pe=pe||{},pe.background=f),a.createElement(yc,{prefixCls:Pe,show:!_,motionClassName:$e,className:Ne,count:q,title:G,style:pe,key:"scrollNumber"},ae)}),le))}),Jn=Sc;Jn.Ribbon=vc;var wc={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"}}]},name:"right",theme:"outlined"},xc=function(t,n){return a.createElement(ke,te({},t,{ref:n,icon:wc}))},sn=a.forwardRef(xc),Ic=Ee.ESC,Oc=Ee.TAB;function Ec(e){var t=e.visible,n=e.triggerRef,o=e.onVisibleChange,r=e.autoFocus,i=e.overlayRef,l=a.useRef(!1),s=function(){if(t){var m,f;(m=n.current)===null||m===void 0||(f=m.focus)===null||f===void 0||f.call(m),o==null||o(!1)}},c=function(){var m;return(m=i.current)!==null&&m!==void 0&&m.focus?(i.current.focus(),l.current=!0,!0):!1},d=function(m){switch(m.keyCode){case Ic:s();break;case Oc:{var f=!1;l.current||(f=c()),f?m.preventDefault():s();break}}};a.useEffect(function(){return t?(window.addEventListener("keydown",d),r&&vt(c,3),function(){window.removeEventListener("keydown",d),l.current=!1}):function(){l.current=!1}},[t])}var Pc=a.forwardRef(function(e,t){var n=e.overlay,o=e.arrow,r=e.prefixCls,i=a.useMemo(function(){var s;return typeof n=="function"?s=n():s=n,s},[n]),l=go(t,i==null?void 0:i.ref);return Z.createElement(Z.Fragment,null,o&&Z.createElement("div",{className:"".concat(r,"-arrow")}),Z.cloneElement(i,{ref:Zr(i)?l:void 0}))}),Ct={adjustX:1,adjustY:1},yt=[0,0],Nc={topLeft:{points:["bl","tl"],overflow:Ct,offset:[0,-4],targetOffset:yt},top:{points:["bc","tc"],overflow:Ct,offset:[0,-4],targetOffset:yt},topRight:{points:["br","tr"],overflow:Ct,offset:[0,-4],targetOffset:yt},bottomLeft:{points:["tl","bl"],overflow:Ct,offset:[0,4],targetOffset:yt},bottom:{points:["tc","bc"],overflow:Ct,offset:[0,4],targetOffset:yt},bottomRight:{points:["tr","br"],overflow:Ct,offset:[0,4],targetOffset:yt}},Rc=["arrow","prefixCls","transitionName","animation","align","placement","placements","getPopupContainer","showAction","hideAction","overlayClassName","overlayStyle","visible","trigger","autoFocus","overlay","children","onVisibleChange"];function jc(e,t){var n,o=e.arrow,r=o===void 0?!1:o,i=e.prefixCls,l=i===void 0?"rc-dropdown":i,s=e.transitionName,c=e.animation,d=e.align,u=e.placement,m=u===void 0?"bottomLeft":u,f=e.placements,p=f===void 0?Nc:f,g=e.getPopupContainer,v=e.showAction,$=e.hideAction,h=e.overlayClassName,b=e.overlayStyle,y=e.visible,S=e.trigger,C=S===void 0?["hover"]:S,w=e.autoFocus,x=e.overlay,O=e.children,I=e.onVisibleChange,P=Oe(e,Rc),j=Z.useState(),N=ce(j,2),E=N[0],R=N[1],z="visible"in e?y:E,D=Z.useRef(null),T=Z.useRef(null),K=Z.useRef(null);Z.useImperativeHandle(t,function(){return D.current});var U=function(H){R(H),I==null||I(H)};Ec({visible:z,triggerRef:K,onVisibleChange:U,autoFocus:w,overlayRef:T});var V=function(H){var W=e.onOverlayClick;R(!1),W&&W(H)},F=function(){return Z.createElement(Pc,{ref:T,overlay:x,prefixCls:l,arrow:r})},A=function(){return typeof x=="function"?F:F()},_=function(){var H=e.minOverlayWidthMatchTrigger,W=e.alignPoint;return"minOverlayWidthMatchTrigger"in e?H:!W},B=function(){var H=e.openClassName;return H!==void 0?H:"".concat(l,"-open")},X=Z.cloneElement(O,{className:M((n=O.props)===null||n===void 0?void 0:n.className,z&&B()),ref:Zr(O)?go(K,O.ref):void 0}),Q=$;return!Q&&C.indexOf("contextMenu")!==-1&&(Q=["click"]),Z.createElement(ca,te({builtinPlacements:p},P,{prefixCls:l,ref:D,popupClassName:M(h,ee({},"".concat(l,"-show-arrow"),r)),popupStyle:b,action:C,showAction:v,hideAction:Q,popupPlacement:m,popupAlign:d,popupTransitionName:s,popupAnimation:c,popupVisible:z,stretch:_()?"minWidth":"",popup:A(),onPopupVisibleChange:U,onPopupClick:V,getPopupContainer:g}),X)}const Mc=Z.forwardRef(jc);var Wa=a.createContext(null);function Va(e,t){return e===void 0?null:"".concat(e,"-").concat(t)}function Xa(e){var t=a.useContext(Wa);return Va(t,e)}var Tc=["children","locked"],Ge=a.createContext(null);function kc(e,t){var n=J({},e);return Object.keys(t).forEach(function(o){var r=t[o];r!==void 0&&(n[o]=r)}),n}function Dt(e){var t=e.children,n=e.locked,o=Oe(e,Tc),r=a.useContext(Ge),i=ll(function(){return kc(r,o)},[r,o],function(l,s){return!n&&(l[0]!==s[0]||!po(l[1],s[1],!0))});return a.createElement(Ge.Provider,{value:i},t)}var zc=[],qa=a.createContext(null);function wn(){return a.useContext(qa)}var Ua=a.createContext(zc);function Et(e){var t=a.useContext(Ua);return a.useMemo(function(){return e!==void 0?[].concat(ve(t),[e]):t},[t,e])}var Ka=a.createContext(null),xo=a.createContext({});function $r(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(Rl(e)){var n=e.nodeName.toLowerCase(),o=["input","select","textarea","button"].includes(n)||e.isContentEditable||n==="a"&&!!e.getAttribute("href"),r=e.getAttribute("tabindex"),i=Number(r),l=null;return r&&!Number.isNaN(i)?l=i:o&&l===null&&(l=0),o&&e.disabled&&(l=null),l!==null&&(l>=0||t&&l<0)}return!1}function Bc(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,n=ve(e.querySelectorAll("*")).filter(function(o){return $r(o,t)});return $r(e,t)&&n.unshift(e),n}var eo=Ee.LEFT,to=Ee.RIGHT,no=Ee.UP,tn=Ee.DOWN,nn=Ee.ENTER,Ga=Ee.ESC,jt=Ee.HOME,Mt=Ee.END,Sr=[no,tn,eo,to];function Lc(e,t,n,o){var r,i,l,s,c="prev",d="next",u="children",m="parent";if(e==="inline"&&o===nn)return{inlineTrigger:!0};var f=(r={},ee(r,no,c),ee(r,tn,d),r),p=(i={},ee(i,eo,n?d:c),ee(i,to,n?c:d),ee(i,tn,u),ee(i,nn,u),i),g=(l={},ee(l,no,c),ee(l,tn,d),ee(l,nn,u),ee(l,Ga,m),ee(l,eo,n?u:m),ee(l,to,n?m:u),l),v={inline:f,horizontal:p,vertical:g,inlineSub:f,horizontalSub:g,verticalSub:g},$=(s=v["".concat(e).concat(t?"":"Sub")])===null||s===void 0?void 0:s[o];switch($){case c:return{offset:-1,sibling:!0};case d:return{offset:1,sibling:!0};case m:return{offset:-1,sibling:!1};case u:return{offset:1,sibling:!1};default:return null}}function Dc(e){for(var t=e;t;){if(t.getAttribute("data-menu-list"))return t;t=t.parentElement}return null}function _c(e,t){for(var n=e||document.activeElement;n;){if(t.has(n))return n;n=n.parentElement}return null}function Io(e,t){var n=Bc(e,!0);return n.filter(function(o){return t.has(o)})}function wr(e,t,n){var o=arguments.length>3&&arguments[3]!==void 0?arguments[3]:1;if(!e)return null;var r=Io(e,t),i=r.length,l=r.findIndex(function(s){return n===s});return o<0?l===-1?l=i-1:l-=1:o>0&&(l+=1),l=(l+i)%i,r[l]}var oo=function(t,n){var o=new Set,r=new Map,i=new Map;return t.forEach(function(l){var s=document.querySelector("[data-menu-id='".concat(Va(n,l),"']"));s&&(o.add(s),i.set(s,l),r.set(l,s))}),{elements:o,key2element:r,element2key:i}};function Ac(e,t,n,o,r,i,l,s,c,d){var u=a.useRef(),m=a.useRef();m.current=t;var f=function(){vt.cancel(u.current)};return a.useEffect(function(){return function(){f()}},[]),function(p){var g=p.which;if([].concat(Sr,[nn,Ga,jt,Mt]).includes(g)){var v=i(),$=oo(v,o),h=$,b=h.elements,y=h.key2element,S=h.element2key,C=y.get(t),w=_c(C,b),x=S.get(w),O=Lc(e,l(x,!0).length===1,n,g);if(!O&&g!==jt&&g!==Mt)return;(Sr.includes(g)||[jt,Mt].includes(g))&&p.preventDefault();var I=function(T){if(T){var K=T,U=T.querySelector("a");U!=null&&U.getAttribute("href")&&(K=U);var V=S.get(T);s(V),f(),u.current=vt(function(){m.current===V&&K.focus()})}};if([jt,Mt].includes(g)||O.sibling||!w){var P;!w||e==="inline"?P=r.current:P=Dc(w);var j,N=Io(P,b);g===jt?j=N[0]:g===Mt?j=N[N.length-1]:j=wr(P,b,w,O.offset),I(j)}else if(O.inlineTrigger)c(x);else if(O.offset>0)c(x,!0),f(),u.current=vt(function(){$=oo(v,o);var D=w.getAttribute("aria-controls"),T=document.getElementById(D),K=wr(T,$.elements);I(K)},5);else if(O.offset<0){var E=l(x,!0),R=E[E.length-2],z=y.get(R);c(R,!1),I(z)}}d==null||d(p)}}function Hc(e){Promise.resolve().then(e)}var Oo="__RC_UTIL_PATH_SPLIT__",xr=function(t){return t.join(Oo)},Fc=function(t){return t.split(Oo)},ro="rc-menu-more";function Wc(){var e=a.useState({}),t=ce(e,2),n=t[1],o=a.useRef(new Map),r=a.useRef(new Map),i=a.useState([]),l=ce(i,2),s=l[0],c=l[1],d=a.useRef(0),u=a.useRef(!1),m=function(){u.current||n({})},f=a.useCallback(function(y,S){var C=xr(S);r.current.set(C,y),o.current.set(y,C),d.current+=1;var w=d.current;Hc(function(){w===d.current&&m()})},[]),p=a.useCallback(function(y,S){var C=xr(S);r.current.delete(C),o.current.delete(y)},[]),g=a.useCallback(function(y){c(y)},[]),v=a.useCallback(function(y,S){var C=o.current.get(y)||"",w=Fc(C);return S&&s.includes(w[0])&&w.unshift(ro),w},[s]),$=a.useCallback(function(y,S){return y.filter(function(C){return C!==void 0}).some(function(C){var w=v(C,!0);return w.includes(S)})},[v]),h=function(){var S=ve(o.current.keys());return s.length&&S.push(ro),S},b=a.useCallback(function(y){var S="".concat(o.current.get(y)).concat(Oo),C=new Set;return ve(r.current.keys()).forEach(function(w){w.startsWith(S)&&C.add(r.current.get(w))}),C},[]);return a.useEffect(function(){return function(){u.current=!0}},[]),{registerPath:f,unregisterPath:p,refreshOverflowKeys:g,isSubPathKey:$,getKeyPath:v,getKeys:h,getSubPathKeys:b}}function Tt(e){var t=a.useRef(e);t.current=e;var n=a.useCallback(function(){for(var o,r=arguments.length,i=new Array(r),l=0;l<r;l++)i[l]=arguments[l];return(o=t.current)===null||o===void 0?void 0:o.call.apply(o,[t].concat(i))},[]);return e?n:void 0}var Vc=Math.random().toFixed(5).toString().slice(2),Ir=0;function Xc(e){var t=Ve(e,{value:e}),n=ce(t,2),o=n[0],r=n[1];return a.useEffect(function(){Ir+=1;var i="".concat(Vc,"-").concat(Ir);r("rc-menu-uuid-".concat(i))},[]),o}function Ya(e,t,n,o){var r=a.useContext(Ge),i=r.activeKey,l=r.onActive,s=r.onInactive,c={active:i===e};return t||(c.onMouseEnter=function(d){n==null||n({key:e,domEvent:d}),l(e)},c.onMouseLeave=function(d){o==null||o({key:e,domEvent:d}),s(e)}),c}function Za(e){var t=a.useContext(Ge),n=t.mode,o=t.rtl,r=t.inlineIndent;if(n!=="inline")return null;var i=e;return o?{paddingRight:i*r}:{paddingLeft:i*r}}function Qa(e){var t=e.icon,n=e.props,o=e.children,r;return t===null||t===!1?null:(typeof t=="function"?r=a.createElement(t,J({},n)):typeof t!="boolean"&&(r=t),r||o||null)}var qc=["item"];function cn(e){var t=e.item,n=Oe(e,qc);return Object.defineProperty(n,"item",{get:function(){return vo(!1,"`info.item` is deprecated since we will move to function component that not provides React Node instance in future."),t}}),n}var Uc=["title","attribute","elementRef"],Kc=["style","className","eventKey","warnKey","disabled","itemIcon","children","role","onMouseEnter","onMouseLeave","onClick","onKeyDown","onFocus"],Gc=["active"],Yc=function(e){ho(n,e);var t=bo(n);function n(){return Co(this,n),t.apply(this,arguments)}return yo(n,[{key:"render",value:function(){var r=this.props,i=r.title,l=r.attribute,s=r.elementRef,c=Oe(r,Uc),d=qe(c,["eventKey","popupClassName","popupOffset","onTitleClick"]);return vo(!l,"`attribute` of Menu.Item is deprecated. Please pass attribute directly."),a.createElement(Bt.Item,te({},l,{title:typeof i=="string"?i:void 0},d,{ref:s}))}}]),n}(a.Component),Zc=a.forwardRef(function(e,t){var n,o=e.style,r=e.className,i=e.eventKey;e.warnKey;var l=e.disabled,s=e.itemIcon,c=e.children,d=e.role,u=e.onMouseEnter,m=e.onMouseLeave,f=e.onClick,p=e.onKeyDown,g=e.onFocus,v=Oe(e,Kc),$=Xa(i),h=a.useContext(Ge),b=h.prefixCls,y=h.onItemClick,S=h.disabled,C=h.overflowDisabled,w=h.itemIcon,x=h.selectedKeys,O=h.onActive,I=a.useContext(xo),P=I._internalRenderMenuItem,j="".concat(b,"-item"),N=a.useRef(),E=a.useRef(),R=S||l,z=uo(t,E),D=Et(i),T=function(G){return{key:i,keyPath:ve(D).reverse(),item:N.current,domEvent:G}},K=s||w,U=Ya(i,R,u,m),V=U.active,F=Oe(U,Gc),A=x.includes(i),_=Za(D.length),B=function(G){if(!R){var le=T(G);f==null||f(cn(le)),y(le)}},X=function(G){if(p==null||p(G),G.which===Ee.ENTER){var le=T(G);f==null||f(cn(le)),y(le)}},Q=function(G){O(i),g==null||g(G)},q={};e.role==="option"&&(q["aria-selected"]=A);var H=a.createElement(Yc,te({ref:N,elementRef:z,role:d===null?"none":d||"menuitem",tabIndex:l?null:-1,"data-menu-id":C&&$?null:$},v,F,q,{component:"li","aria-disabled":l,style:J(J({},_),o),className:M(j,(n={},ee(n,"".concat(j,"-active"),V),ee(n,"".concat(j,"-selected"),A),ee(n,"".concat(j,"-disabled"),R),n),r),onClick:B,onKeyDown:X,onFocus:Q}),c,a.createElement(Qa,{props:J(J({},e),{},{isSelected:A}),icon:K}));return P&&(H=P(H,e,{selected:A})),H});function Qc(e,t){var n=e.eventKey,o=wn(),r=Et(n);return a.useEffect(function(){if(o)return o.registerPath(n,r),function(){o.unregisterPath(n,r)}},[r]),o?null:a.createElement(Zc,te({},e,{ref:t}))}const xn=a.forwardRef(Qc);var Jc=["className","children"],ed=function(t,n){var o=t.className,r=t.children,i=Oe(t,Jc),l=a.useContext(Ge),s=l.prefixCls,c=l.mode,d=l.rtl;return a.createElement("ul",te({className:M(s,d&&"".concat(s,"-rtl"),"".concat(s,"-sub"),"".concat(s,"-").concat(c==="inline"?"inline":"vertical"),o),role:"menu"},i,{"data-menu-list":!0,ref:n}),r)},Eo=a.forwardRef(ed);Eo.displayName="SubMenuList";function Po(e,t){return Ft(e).map(function(n,o){if(a.isValidElement(n)){var r,i,l=n.key,s=(r=(i=n.props)===null||i===void 0?void 0:i.eventKey)!==null&&r!==void 0?r:l,c=s==null;c&&(s="tmp_key-".concat([].concat(ve(t),[o]).join("-")));var d={key:s,eventKey:s};return a.cloneElement(n,d)}return n})}var Te={adjustX:1,adjustY:1},td={topLeft:{points:["bl","tl"],overflow:Te},topRight:{points:["br","tr"],overflow:Te},bottomLeft:{points:["tl","bl"],overflow:Te},bottomRight:{points:["tr","br"],overflow:Te},leftTop:{points:["tr","tl"],overflow:Te},leftBottom:{points:["br","bl"],overflow:Te},rightTop:{points:["tl","tr"],overflow:Te},rightBottom:{points:["bl","br"],overflow:Te}},nd={topLeft:{points:["bl","tl"],overflow:Te},topRight:{points:["br","tr"],overflow:Te},bottomLeft:{points:["tl","bl"],overflow:Te},bottomRight:{points:["tr","br"],overflow:Te},rightTop:{points:["tr","tl"],overflow:Te},rightBottom:{points:["br","bl"],overflow:Te},leftTop:{points:["tl","tr"],overflow:Te},leftBottom:{points:["bl","br"],overflow:Te}};function Ja(e,t,n){if(t)return t;if(n)return n[e]||n.other}var od={horizontal:"bottomLeft",vertical:"rightTop","vertical-left":"rightTop","vertical-right":"leftTop"};function rd(e){var t=e.prefixCls,n=e.visible,o=e.children,r=e.popup,i=e.popupStyle,l=e.popupClassName,s=e.popupOffset,c=e.disabled,d=e.mode,u=e.onVisibleChange,m=a.useContext(Ge),f=m.getPopupContainer,p=m.rtl,g=m.subMenuOpenDelay,v=m.subMenuCloseDelay,$=m.builtinPlacements,h=m.triggerSubMenuAction,b=m.forceSubMenuRender,y=m.rootClassName,S=m.motion,C=m.defaultMotions,w=a.useState(!1),x=ce(w,2),O=x[0],I=x[1],P=p?J(J({},nd),$):J(J({},td),$),j=od[d],N=Ja(d,S,C),E=a.useRef(N);d!=="inline"&&(E.current=N);var R=J(J({},E.current),{},{leavedClassName:"".concat(t,"-hidden"),removeOnLeave:!1,motionAppear:!0}),z=a.useRef();return a.useEffect(function(){return z.current=vt(function(){I(n)}),function(){vt.cancel(z.current)}},[n]),a.createElement(ca,{prefixCls:t,popupClassName:M("".concat(t,"-popup"),ee({},"".concat(t,"-rtl"),p),l,y),stretch:d==="horizontal"?"minWidth":null,getPopupContainer:f,builtinPlacements:P,popupPlacement:j,popupVisible:O,popup:r,popupStyle:i,popupAlign:s&&{offset:s},action:c?[]:[h],mouseEnterDelay:g,mouseLeaveDelay:v,onPopupVisibleChange:u,forceRender:b,popupMotion:R,fresh:!0},o)}function ad(e){var t=e.id,n=e.open,o=e.keyPath,r=e.children,i="inline",l=a.useContext(Ge),s=l.prefixCls,c=l.forceSubMenuRender,d=l.motion,u=l.defaultMotions,m=l.mode,f=a.useRef(!1);f.current=m===i;var p=a.useState(!f.current),g=ce(p,2),v=g[0],$=g[1],h=f.current?n:!1;a.useEffect(function(){f.current&&$(!1)},[m]);var b=J({},Ja(i,d,u));o.length>1&&(b.motionAppear=!1);var y=b.onVisibleChanged;return b.onVisibleChanged=function(S){return!f.current&&!S&&$(!0),y==null?void 0:y(S)},v?null:a.createElement(Dt,{mode:i,locked:!f.current},a.createElement(bt,te({visible:h},b,{forceRender:c,removeOnLeave:!1,leavedClassName:"".concat(s,"-hidden")}),function(S){var C=S.className,w=S.style;return a.createElement(Eo,{id:t,className:C,style:w},r)}))}var id=["style","className","title","eventKey","warnKey","disabled","internalPopupClose","children","itemIcon","expandIcon","popupClassName","popupOffset","popupStyle","onClick","onMouseEnter","onMouseLeave","onTitleClick","onTitleMouseEnter","onTitleMouseLeave"],ld=["active"],sd=a.forwardRef(function(e,t){var n,o=e.style,r=e.className,i=e.title,l=e.eventKey;e.warnKey;var s=e.disabled,c=e.internalPopupClose,d=e.children,u=e.itemIcon,m=e.expandIcon,f=e.popupClassName,p=e.popupOffset,g=e.popupStyle,v=e.onClick,$=e.onMouseEnter,h=e.onMouseLeave,b=e.onTitleClick,y=e.onTitleMouseEnter,S=e.onTitleMouseLeave,C=Oe(e,id),w=Xa(l),x=a.useContext(Ge),O=x.prefixCls,I=x.mode,P=x.openKeys,j=x.disabled,N=x.overflowDisabled,E=x.activeKey,R=x.selectedKeys,z=x.itemIcon,D=x.expandIcon,T=x.onItemClick,K=x.onOpenChange,U=x.onActive,V=a.useContext(xo),F=V._internalRenderSubMenuItem,A=a.useContext(Ka),_=A.isSubPathKey,B=Et(),X="".concat(O,"-submenu"),Q=j||s,q=a.useRef(),H=a.useRef(),W=u??z,G=m??D,le=P.includes(l),ae=!N&&le,Y=_(R,l),ie=Ya(l,Q,y,S),re=ie.active,xe=Oe(ie,ld),he=a.useState(!1),$e=ce(he,2),Ce=$e[0],Se=$e[1],Pe=function(de){Q||Se(de)},Re=function(de){Pe(!0),$==null||$({key:l,domEvent:de})},Ne=function(de){Pe(!1),h==null||h({key:l,domEvent:de})},pe=a.useMemo(function(){return re||(I!=="inline"?Ce||_([E],l):!1)},[I,re,E,Ce,l,_]),nt=Za(B.length),ot=function(de){Q||(b==null||b({key:l,domEvent:de}),I==="inline"&&K(l,!le))},Ze=Tt(function(ne){v==null||v(cn(ne)),T(ne)}),ze=function(de){I!=="inline"&&K(l,de)},Me=function(){U(l)},je=w&&"".concat(w,"-popup"),Be=a.createElement("div",te({role:"menuitem",style:nt,className:"".concat(X,"-title"),tabIndex:Q?null:-1,ref:q,title:typeof i=="string"?i:null,"data-menu-id":N&&w?null:w,"aria-expanded":ae,"aria-haspopup":!0,"aria-controls":je,"aria-disabled":Q,onClick:ot,onFocus:Me},xe),i,a.createElement(Qa,{icon:I!=="horizontal"?G:void 0,props:J(J({},e),{},{isOpen:ae,isSubMenu:!0})},a.createElement("i",{className:"".concat(X,"-arrow")}))),Le=a.useRef(I);if(I!=="inline"&&B.length>1?Le.current="vertical":Le.current=I,!N){var Qe=Le.current;Be=a.createElement(rd,{mode:Qe,prefixCls:X,visible:!c&&ae&&I!=="inline",popupClassName:f,popupOffset:p,popupStyle:g,popup:a.createElement(Dt,{mode:Qe==="horizontal"?"vertical":Qe},a.createElement(Eo,{id:je,ref:H},d)),disabled:Q,onVisibleChange:ze},Be)}var oe=a.createElement(Bt.Item,te({ref:t,role:"none"},C,{component:"li",style:o,className:M(X,"".concat(X,"-").concat(I),r,(n={},ee(n,"".concat(X,"-open"),ae),ee(n,"".concat(X,"-active"),pe),ee(n,"".concat(X,"-selected"),Y),ee(n,"".concat(X,"-disabled"),Q),n)),onMouseEnter:Re,onMouseLeave:Ne}),Be,!N&&a.createElement(ad,{id:je,open:ae,keyPath:B},d));return F&&(oe=F(oe,e,{selected:Y,active:pe,open:ae,disabled:Q})),a.createElement(Dt,{onItemClick:Ze,mode:I==="horizontal"?"vertical":I,itemIcon:W,expandIcon:G},oe)}),In=a.forwardRef(function(e,t){var n=e.eventKey,o=e.children,r=Et(n),i=Po(o,r),l=wn();a.useEffect(function(){if(l)return l.registerPath(n,r),function(){l.unregisterPath(n,r)}},[r]);var s;return l?s=i:s=a.createElement(sd,te({ref:t},e),i),a.createElement(Ua.Provider,{value:r},s)});function No(e){var t=e.className,n=e.style,o=a.useContext(Ge),r=o.prefixCls,i=wn();return i?null:a.createElement("li",{role:"separator",className:M("".concat(r,"-item-divider"),t),style:n})}var cd=["className","title","eventKey","children"],dd=a.forwardRef(function(e,t){var n=e.className,o=e.title;e.eventKey;var r=e.children,i=Oe(e,cd),l=a.useContext(Ge),s=l.prefixCls,c="".concat(s,"-item-group");return a.createElement("li",te({ref:t,role:"presentation"},i,{onClick:function(u){return u.stopPropagation()},className:M(c,n)}),a.createElement("div",{role:"presentation",className:"".concat(c,"-title"),title:typeof o=="string"?o:void 0},o),a.createElement("ul",{role:"group",className:"".concat(c,"-list")},r))}),Ro=a.forwardRef(function(e,t){var n=e.eventKey,o=e.children,r=Et(n),i=Po(o,r),l=wn();return l?i:a.createElement(dd,te({ref:t},qe(e,["warnKey"])),i)}),ud=["label","children","key","type"];function ao(e,t){var n=t.item,o=t.group,r=t.submenu,i=t.divider;return(e||[]).map(function(l,s){if(l&&Ke(l)==="object"){var c=l,d=c.label,u=c.children,m=c.key,f=c.type,p=Oe(c,ud),g=m??"tmp-".concat(s);return u||f==="group"?f==="group"?a.createElement(o,te({key:g},p,{title:d}),ao(u,t)):a.createElement(r,te({key:g},p,{title:d}),ao(u,t)):f==="divider"?a.createElement(i,te({key:g},p)):a.createElement(n,te({key:g},p),d)}return null}).filter(function(l){return l})}function Or(e,t,n,o){var r=e,i=J({divider:No,item:xn,group:Ro,submenu:In},o);return t&&(r=ao(t,i)),Po(r,n)}var md=["prefixCls","rootClassName","style","className","tabIndex","items","children","direction","id","mode","inlineCollapsed","disabled","disabledOverflow","subMenuOpenDelay","subMenuCloseDelay","forceSubMenuRender","defaultOpenKeys","openKeys","activeKey","defaultActiveFirst","selectable","multiple","defaultSelectedKeys","selectedKeys","onSelect","onDeselect","inlineIndent","motion","defaultMotions","triggerSubMenuAction","builtinPlacements","itemIcon","expandIcon","overflowedIndicator","overflowedIndicatorPopupClassName","getPopupContainer","onClick","onOpenChange","onKeyDown","openAnimation","openTransitionName","_internalRenderMenuItem","_internalRenderSubMenuItem","_internalComponents"],ft=[],fd=a.forwardRef(function(e,t){var n,o,r=e,i=r.prefixCls,l=i===void 0?"rc-menu":i,s=r.rootClassName,c=r.style,d=r.className,u=r.tabIndex,m=u===void 0?0:u,f=r.items,p=r.children,g=r.direction,v=r.id,$=r.mode,h=$===void 0?"vertical":$,b=r.inlineCollapsed,y=r.disabled,S=r.disabledOverflow,C=r.subMenuOpenDelay,w=C===void 0?.1:C,x=r.subMenuCloseDelay,O=x===void 0?.1:x,I=r.forceSubMenuRender,P=r.defaultOpenKeys,j=r.openKeys,N=r.activeKey,E=r.defaultActiveFirst,R=r.selectable,z=R===void 0?!0:R,D=r.multiple,T=D===void 0?!1:D,K=r.defaultSelectedKeys,U=r.selectedKeys,V=r.onSelect,F=r.onDeselect,A=r.inlineIndent,_=A===void 0?24:A,B=r.motion,X=r.defaultMotions,Q=r.triggerSubMenuAction,q=Q===void 0?"hover":Q,H=r.builtinPlacements,W=r.itemIcon,G=r.expandIcon,le=r.overflowedIndicator,ae=le===void 0?"...":le,Y=r.overflowedIndicatorPopupClassName,ie=r.getPopupContainer,re=r.onClick,xe=r.onOpenChange,he=r.onKeyDown;r.openAnimation,r.openTransitionName;var $e=r._internalRenderMenuItem,Ce=r._internalRenderSubMenuItem,Se=r._internalComponents,Pe=Oe(r,md),Re=a.useMemo(function(){return[Or(p,f,ft,Se),Or(p,f,ft,{})]},[p,f,Se]),Ne=ce(Re,2),pe=Ne[0],nt=Ne[1],ot=a.useState(!1),Ze=ce(ot,2),ze=Ze[0],Me=Ze[1],je=a.useRef(),Be=Xc(v),Le=g==="rtl",Qe=Ve(P,{value:j,postState:function(ge){return ge||ft}}),oe=ce(Qe,2),ne=oe[0],de=oe[1],se=function(ge){var be=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;function De(){de(ge),xe==null||xe(ge)}be?Kn.flushSync(De):De()},me=a.useState(ne),Ie=ce(me,2),ye=Ie[0],fe=Ie[1],Ue=a.useRef(!1),ko=a.useMemo(function(){return(h==="inline"||h==="vertical")&&b?["vertical",b]:[h,!1]},[h,b]),zo=ce(ko,2),Ut=zo[0],Rn=zo[1],Bo=Ut==="inline",Ii=a.useState(Ut),Lo=ce(Ii,2),lt=Lo[0],Oi=Lo[1],Ei=a.useState(Rn),Do=ce(Ei,2),Pi=Do[0],Ni=Do[1];a.useEffect(function(){Oi(Ut),Ni(Rn),Ue.current&&(Bo?de(ye):se(ft))},[Ut,Rn]);var Ri=a.useState(0),_o=ce(Ri,2),Kt=_o[0],ji=_o[1],jn=Kt>=pe.length-1||lt!=="horizontal"||S;a.useEffect(function(){Bo&&fe(ne)},[ne]),a.useEffect(function(){return Ue.current=!0,function(){Ue.current=!1}},[]);var mt=Wc(),Ao=mt.registerPath,Ho=mt.unregisterPath,Mi=mt.refreshOverflowKeys,Fo=mt.isSubPathKey,Ti=mt.getKeyPath,Wo=mt.getKeys,ki=mt.getSubPathKeys,zi=a.useMemo(function(){return{registerPath:Ao,unregisterPath:Ho}},[Ao,Ho]),Bi=a.useMemo(function(){return{isSubPathKey:Fo}},[Fo]);a.useEffect(function(){Mi(jn?ft:pe.slice(Kt+1).map(function(we){return we.key}))},[Kt,jn]);var Li=Ve(N||E&&((n=pe[0])===null||n===void 0?void 0:n.key),{value:N}),Vo=ce(Li,2),Nt=Vo[0],Mn=Vo[1],Di=Tt(function(we){Mn(we)}),_i=Tt(function(){Mn(void 0)});a.useImperativeHandle(t,function(){return{list:je.current,focus:function(ge){var be,De=Wo(),_e=oo(De,Be),Yt=_e.elements,Tn=_e.key2element,Gi=_e.element2key,Uo=Io(je.current,Yt),Ko=Nt??(Uo[0]?Gi.get(Uo[0]):(be=pe.find(function(Yi){return!Yi.props.disabled}))===null||be===void 0?void 0:be.key),Rt=Tn.get(Ko);if(Ko&&Rt){var kn;Rt==null||(kn=Rt.focus)===null||kn===void 0||kn.call(Rt,ge)}}}});var Ai=Ve(K||[],{value:U,postState:function(ge){return Array.isArray(ge)?ge:ge==null?ft:[ge]}}),Xo=ce(Ai,2),Gt=Xo[0],Hi=Xo[1],Fi=function(ge){if(z){var be=ge.key,De=Gt.includes(be),_e;T?De?_e=Gt.filter(function(Tn){return Tn!==be}):_e=[].concat(ve(Gt),[be]):_e=[be],Hi(_e);var Yt=J(J({},ge),{},{selectedKeys:_e});De?F==null||F(Yt):V==null||V(Yt)}!T&&ne.length&&lt!=="inline"&&se(ft)},Wi=Tt(function(we){re==null||re(cn(we)),Fi(we)}),qo=Tt(function(we,ge){var be=ne.filter(function(_e){return _e!==we});if(ge)be.push(we);else if(lt!=="inline"){var De=ki(we);be=be.filter(function(_e){return!De.has(_e)})}po(ne,be,!0)||se(be,!0)}),Vi=function(ge,be){var De=be??!ne.includes(ge);qo(ge,De)},Xi=Ac(lt,Nt,Le,Be,je,Wo,Ti,Mn,Vi,he);a.useEffect(function(){Me(!0)},[]);var qi=a.useMemo(function(){return{_internalRenderMenuItem:$e,_internalRenderSubMenuItem:Ce}},[$e,Ce]),Ui=lt!=="horizontal"||S?pe:pe.map(function(we,ge){return a.createElement(Dt,{key:we.key,overflowDisabled:ge>Kt},we)}),Ki=a.createElement(Bt,te({id:v,ref:je,prefixCls:"".concat(l,"-overflow"),component:"ul",itemComponent:xn,className:M(l,"".concat(l,"-root"),"".concat(l,"-").concat(lt),d,(o={},ee(o,"".concat(l,"-inline-collapsed"),Pi),ee(o,"".concat(l,"-rtl"),Le),o),s),dir:g,style:c,role:"menu",tabIndex:m,data:Ui,renderRawItem:function(ge){return ge},renderRawRest:function(ge){var be=ge.length,De=be?pe.slice(-be):null;return a.createElement(In,{eventKey:ro,title:ae,disabled:jn,internalPopupClose:be===0,popupClassName:Y},De)},maxCount:lt!=="horizontal"||S?Bt.INVALIDATE:Bt.RESPONSIVE,ssr:"full","data-menu-list":!0,onVisibleChange:function(ge){ji(ge)},onKeyDown:Xi},Pe));return a.createElement(xo.Provider,{value:qi},a.createElement(Wa.Provider,{value:Be},a.createElement(Dt,{prefixCls:l,rootClassName:s,mode:lt,openKeys:ne,rtl:Le,disabled:y,motion:ze?B:null,defaultMotions:ze?X:null,activeKey:Nt,onActive:Di,onInactive:_i,selectedKeys:Gt,inlineIndent:_,subMenuOpenDelay:w,subMenuCloseDelay:O,forceSubMenuRender:I,builtinPlacements:H,triggerSubMenuAction:q,getPopupContainer:ie,itemIcon:W,expandIcon:G,onItemClick:Wi,onOpenChange:qo},a.createElement(Ka.Provider,{value:Bi},Ki),a.createElement("div",{style:{display:"none"},"aria-hidden":!0},a.createElement(qa.Provider,{value:zi},nt)))))}),Xt=fd;Xt.Item=xn;Xt.SubMenu=In;Xt.ItemGroup=Ro;Xt.Divider=No;var gd={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M912 192H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 284H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 284H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM104 228a56 56 0 10112 0 56 56 0 10-112 0zm0 284a56 56 0 10112 0 56 56 0 10-112 0zm0 284a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"bars",theme:"outlined"},pd=function(t,n){return a.createElement(ke,te({},t,{ref:n,icon:gd}))},vd=a.forwardRef(pd),hd={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M724 218.3V141c0-6.7-7.7-10.4-12.9-6.3L260.3 486.8a31.86 31.86 0 000 50.3l450.8 352.1c5.3 4.1 12.9.4 12.9-6.3v-77.3c0-4.9-2.3-9.6-6.1-12.6l-360-281 360-281.1c3.8-3 6.1-7.7 6.1-12.6z"}}]},name:"left",theme:"outlined"},bd=function(t,n){return a.createElement(ke,te({},t,{ref:n,icon:hd}))},io=a.forwardRef(bd);const Cd=e=>!isNaN(parseFloat(e))&&isFinite(e),ei=a.createContext({siderHook:{addSider:()=>null,removeSider:()=>null}});var yd=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n};const Er={xs:"479.98px",sm:"575.98px",md:"767.98px",lg:"991.98px",xl:"1199.98px",xxl:"1599.98px"},On=a.createContext({}),$d=(()=>{let e=0;return function(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"";return e+=1,`${t}${e}`}})(),ti=a.forwardRef((e,t)=>{const{prefixCls:n,className:o,trigger:r,children:i,defaultCollapsed:l=!1,theme:s="dark",style:c={},collapsible:d=!1,reverseArrow:u=!1,width:m=200,collapsedWidth:f=80,zeroWidthTriggerStyle:p,breakpoint:g,onCollapse:v,onBreakpoint:$}=e,h=yd(e,["prefixCls","className","trigger","children","defaultCollapsed","theme","style","collapsible","reverseArrow","width","collapsedWidth","zeroWidthTriggerStyle","breakpoint","onCollapse","onBreakpoint"]),{siderHook:b}=a.useContext(ei),[y,S]=a.useState("collapsed"in e?e.collapsed:l),[C,w]=a.useState(!1);a.useEffect(()=>{"collapsed"in e&&S(e.collapsed)},[e.collapsed]);const x=(E,R)=>{"collapsed"in e||S(E),v==null||v(E,R)},O=a.useRef();O.current=E=>{w(E.matches),$==null||$(E.matches),y!==E.matches&&x(E.matches,"responsive")},a.useEffect(()=>{function E(z){return O.current(z)}let R;if(typeof window<"u"){const{matchMedia:z}=window;if(z&&g&&g in Er){R=z(`screen and (max-width: ${Er[g]})`);try{R.addEventListener("change",E)}catch{R.addListener(E)}E(R)}}return()=>{try{R==null||R.removeEventListener("change",E)}catch{R==null||R.removeListener(E)}}},[g]),a.useEffect(()=>{const E=$d("ant-sider-");return b.addSider(E),()=>b.removeSider(E)},[]);const I=()=>{x(!y,"clickTrigger")},{getPrefixCls:P}=a.useContext(ue),j=()=>{const E=P("layout-sider",n),R=qe(h,["collapsed"]),z=y?f:m,D=Cd(z)?`${z}px`:String(z),T=parseFloat(String(f||0))===0?a.createElement("span",{onClick:I,className:M(`${E}-zero-width-trigger`,`${E}-zero-width-trigger-${u?"right":"left"}`),style:p},r||a.createElement(vd,null)):null,V={expanded:u?a.createElement(sn,null):a.createElement(io,null),collapsed:u?a.createElement(io,null):a.createElement(sn,null)}[y?"collapsed":"expanded"],F=r!==null?T||a.createElement("div",{className:`${E}-trigger`,onClick:I,style:{width:D}},r||V):null,A=Object.assign(Object.assign({},c),{flex:`0 0 ${D}`,maxWidth:D,minWidth:D,width:D}),_=M(E,`${E}-${s}`,{[`${E}-collapsed`]:!!y,[`${E}-has-trigger`]:d&&r!==null&&!T,[`${E}-below`]:!!C,[`${E}-zero-width`]:parseFloat(D)===0},o);return a.createElement("aside",Object.assign({className:_},R,{style:A,ref:t}),a.createElement("div",{className:`${E}-children`},i),d||C&&T?F:null)},N=a.useMemo(()=>({siderCollapsed:y}),[y]);return a.createElement(On.Provider,{value:N},j())});var Sd={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M176 511a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"ellipsis",theme:"outlined"},wd=function(t,n){return a.createElement(ke,te({},t,{ref:n,icon:Sd}))},ni=a.forwardRef(wd);const dn=a.createContext({prefixCls:"",firstLevel:!0,inlineCollapsed:!1});var xd=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n};const oi=e=>{const{prefixCls:t,className:n,dashed:o}=e,r=xd(e,["prefixCls","className","dashed"]),{getPrefixCls:i}=a.useContext(ue),l=i("menu",t),s=M({[`${l}-item-divider-dashed`]:!!o},n);return a.createElement(No,Object.assign({className:s},r))},ri=e=>{var t;const{className:n,children:o,icon:r,title:i,danger:l}=e,{prefixCls:s,firstLevel:c,direction:d,disableMenuItemTitleTooltip:u,inlineCollapsed:m}=a.useContext(dn),f=b=>{const y=a.createElement("span",{className:`${s}-title-content`},o);return(!r||a.isValidElement(o)&&o.type==="span")&&o&&b&&c&&typeof o=="string"?a.createElement("div",{className:`${s}-inline-collapsed-noicon`},o.charAt(0)):y},{siderCollapsed:p}=a.useContext(On);let g=i;typeof i>"u"?g=c?o:"":i===!1&&(g="");const v={title:g};!p&&!m&&(v.title=null,v.open=!1);const $=Ft(o).length;let h=a.createElement(xn,Object.assign({},qe(e,["title","icon","danger"]),{className:M({[`${s}-item-danger`]:l,[`${s}-item-only-child`]:(r?$+1:$)===1},n),title:typeof i=="string"?i:void 0}),Je(r,{className:M(a.isValidElement(r)?(t=r.props)===null||t===void 0?void 0:t.className:"",`${s}-item-icon`)}),f(m));return u||(h=a.createElement(yn,Object.assign({},v,{placement:d==="rtl"?"left":"right",overlayClassName:`${s}-inline-collapsed-tooltip`}),h)),h};var Id=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n};const un=a.createContext(null),Od=a.forwardRef((e,t)=>{const{children:n}=e,o=Id(e,["children"]),r=a.useContext(un),i=a.useMemo(()=>Object.assign(Object.assign({},r),o),[r,o.prefixCls,o.mode,o.selectable,o.rootClassName]),l=sl(n),s=uo(t,l?n.ref:null);return a.createElement(un.Provider,{value:i},a.createElement(na,{space:!0},l?a.cloneElement(n,{ref:s}):n))}),Ed=e=>{const{componentCls:t,motionDurationSlow:n,horizontalLineHeight:o,colorSplit:r,lineWidth:i,lineType:l,itemPaddingInline:s}=e;return{[`${t}-horizontal`]:{lineHeight:o,border:0,borderBottom:`${L(i)} ${l} ${r}`,boxShadow:"none","&::after":{display:"block",clear:"both",height:0,content:'"\\20"'},[`${t}-item, ${t}-submenu`]:{position:"relative",display:"inline-block",verticalAlign:"bottom",paddingInline:s},[`> ${t}-item:hover,
        > ${t}-item-active,
        > ${t}-submenu ${t}-submenu-title:hover`]:{backgroundColor:"transparent"},[`${t}-item, ${t}-submenu-title`]:{transition:[`border-color ${n}`,`background ${n}`].join(",")},[`${t}-submenu-arrow`]:{display:"none"}}}},Pd=e=>{let{componentCls:t,menuArrowOffset:n,calc:o}=e;return{[`${t}-rtl`]:{direction:"rtl"},[`${t}-submenu-rtl`]:{transformOrigin:"100% 0"},[`${t}-rtl${t}-vertical,
    ${t}-submenu-rtl ${t}-vertical`]:{[`${t}-submenu-arrow`]:{"&::before":{transform:`rotate(-45deg) translateY(${L(o(n).mul(-1).equal())})`},"&::after":{transform:`rotate(45deg) translateY(${L(n)})`}}}}},Pr=e=>Object.assign({},cl(e)),Nr=(e,t)=>{const{componentCls:n,itemColor:o,itemSelectedColor:r,groupTitleColor:i,itemBg:l,subMenuItemBg:s,itemSelectedBg:c,activeBarHeight:d,activeBarWidth:u,activeBarBorderWidth:m,motionDurationSlow:f,motionEaseInOut:p,motionEaseOut:g,itemPaddingInline:v,motionDurationMid:$,itemHoverColor:h,lineType:b,colorSplit:y,itemDisabledColor:S,dangerItemColor:C,dangerItemHoverColor:w,dangerItemSelectedColor:x,dangerItemActiveBg:O,dangerItemSelectedBg:I,popupBg:P,itemHoverBg:j,itemActiveBg:N,menuSubMenuBg:E,horizontalItemSelectedColor:R,horizontalItemSelectedBg:z,horizontalItemBorderRadius:D,horizontalItemHoverBg:T}=e;return{[`${n}-${t}, ${n}-${t} > ${n}`]:{color:o,background:l,[`&${n}-root:focus-visible`]:Object.assign({},Pr(e)),[`${n}-item-group-title`]:{color:i},[`${n}-submenu-selected`]:{[`> ${n}-submenu-title`]:{color:r}},[`${n}-item, ${n}-submenu-title`]:{color:o,[`&:not(${n}-item-disabled):focus-visible`]:Object.assign({},Pr(e))},[`${n}-item-disabled, ${n}-submenu-disabled`]:{color:`${S} !important`},[`${n}-item:not(${n}-item-selected):not(${n}-submenu-selected)`]:{[`&:hover, > ${n}-submenu-title:hover`]:{color:h}},[`&:not(${n}-horizontal)`]:{[`${n}-item:not(${n}-item-selected)`]:{"&:hover":{backgroundColor:j},"&:active":{backgroundColor:N}},[`${n}-submenu-title`]:{"&:hover":{backgroundColor:j},"&:active":{backgroundColor:N}}},[`${n}-item-danger`]:{color:C,[`&${n}-item:hover`]:{[`&:not(${n}-item-selected):not(${n}-submenu-selected)`]:{color:w}},[`&${n}-item:active`]:{background:O}},[`${n}-item a`]:{"&, &:hover":{color:"inherit"}},[`${n}-item-selected`]:{color:r,[`&${n}-item-danger`]:{color:x},"a, a:hover":{color:"inherit"}},[`& ${n}-item-selected`]:{backgroundColor:c,[`&${n}-item-danger`]:{backgroundColor:I}},[`&${n}-submenu > ${n}`]:{backgroundColor:E},[`&${n}-popup > ${n}`]:{backgroundColor:P},[`&${n}-submenu-popup > ${n}`]:{backgroundColor:P},[`&${n}-horizontal`]:Object.assign(Object.assign({},t==="dark"?{borderBottom:0}:{}),{[`> ${n}-item, > ${n}-submenu`]:{top:m,marginTop:e.calc(m).mul(-1).equal(),marginBottom:0,borderRadius:D,"&::after":{position:"absolute",insetInline:v,bottom:0,borderBottom:`${L(d)} solid transparent`,transition:`border-color ${f} ${p}`,content:'""'},"&:hover, &-active, &-open":{background:T,"&::after":{borderBottomWidth:d,borderBottomColor:R}},"&-selected":{color:R,backgroundColor:z,"&:hover":{backgroundColor:z},"&::after":{borderBottomWidth:d,borderBottomColor:R}}}}),[`&${n}-root`]:{[`&${n}-inline, &${n}-vertical`]:{borderInlineEnd:`${L(m)} ${b} ${y}`}},[`&${n}-inline`]:{[`${n}-sub${n}-inline`]:{background:s},[`${n}-item`]:{position:"relative","&::after":{position:"absolute",insetBlock:0,insetInlineEnd:0,borderInlineEnd:`${L(u)} solid ${r}`,transform:"scaleY(0.0001)",opacity:0,transition:[`transform ${$} ${g}`,`opacity ${$} ${g}`].join(","),content:'""'},[`&${n}-item-danger`]:{"&::after":{borderInlineEndColor:x}}},[`${n}-selected, ${n}-item-selected`]:{"&::after":{transform:"scaleY(1)",opacity:1,transition:[`transform ${$} ${p}`,`opacity ${$} ${p}`].join(",")}}}}}},Rr=e=>{const{componentCls:t,itemHeight:n,itemMarginInline:o,padding:r,menuArrowSize:i,marginXS:l,itemMarginBlock:s,itemWidth:c}=e,d=e.calc(i).add(r).add(l).equal();return{[`${t}-item`]:{position:"relative",overflow:"hidden"},[`${t}-item, ${t}-submenu-title`]:{height:n,lineHeight:L(n),paddingInline:r,overflow:"hidden",textOverflow:"ellipsis",marginInline:o,marginBlock:s,width:c},[`> ${t}-item,
            > ${t}-submenu > ${t}-submenu-title`]:{height:n,lineHeight:L(n)},[`${t}-item-group-list ${t}-submenu-title,
            ${t}-submenu-title`]:{paddingInlineEnd:d}}},Nd=e=>{const{componentCls:t,iconCls:n,itemHeight:o,colorTextLightSolid:r,dropdownWidth:i,controlHeightLG:l,motionDurationMid:s,motionEaseOut:c,paddingXL:d,itemMarginInline:u,fontSizeLG:m,motionDurationSlow:f,paddingXS:p,boxShadowSecondary:g,collapsedWidth:v,collapsedIconSize:$}=e,h={height:o,lineHeight:L(o),listStylePosition:"inside",listStyleType:"disc"};return[{[t]:{"&-inline, &-vertical":Object.assign({[`&${t}-root`]:{boxShadow:"none"}},Rr(e))},[`${t}-submenu-popup`]:{[`${t}-vertical`]:Object.assign(Object.assign({},Rr(e)),{boxShadow:g})}},{[`${t}-submenu-popup ${t}-vertical${t}-sub`]:{minWidth:i,maxHeight:`calc(100vh - ${L(e.calc(l).mul(2.5).equal())})`,padding:"0",overflow:"hidden",borderInlineEnd:0,"&:not([class*='-active'])":{overflowX:"hidden",overflowY:"auto"}}},{[`${t}-inline`]:{width:"100%",[`&${t}-root`]:{[`${t}-item, ${t}-submenu-title`]:{display:"flex",alignItems:"center",transition:[`border-color ${f}`,`background ${f}`,`padding ${s} ${c}`,`padding-inline calc(50% - ${L(e.calc(m).div(2).equal())} - ${L(u)})`].join(","),[`> ${t}-title-content`]:{flex:"auto",minWidth:0,overflow:"hidden",textOverflow:"ellipsis"},"> *":{flex:"none"}}},[`${t}-sub${t}-inline`]:{padding:0,border:0,borderRadius:0,boxShadow:"none",[`& > ${t}-submenu > ${t}-submenu-title`]:h,[`& ${t}-item-group-title`]:{paddingInlineStart:d}},[`${t}-item`]:h}},{[`${t}-inline-collapsed`]:{width:v,[`&${t}-root`]:{[`${t}-item, ${t}-submenu ${t}-submenu-title`]:{[`> ${t}-inline-collapsed-noicon`]:{fontSize:m,textAlign:"center"}}},[`> ${t}-item,
          > ${t}-item-group > ${t}-item-group-list > ${t}-item,
          > ${t}-item-group > ${t}-item-group-list > ${t}-submenu > ${t}-submenu-title,
          > ${t}-submenu > ${t}-submenu-title`]:{insetInlineStart:0,paddingInline:`calc(50% - ${L(e.calc(m).div(2).equal())} - ${L(u)})`,textOverflow:"clip",[`
            ${t}-submenu-arrow,
            ${t}-submenu-expand-icon
          `]:{opacity:0},[`${t}-item-icon, ${n}`]:{margin:0,fontSize:$,lineHeight:L(o),"+ span":{display:"inline-block",opacity:0}}},[`${t}-item-icon, ${n}`]:{display:"inline-block"},"&-tooltip":{pointerEvents:"none",[`${t}-item-icon, ${n}`]:{display:"none"},"a, a:hover":{color:r}},[`${t}-item-group-title`]:Object.assign(Object.assign({},hn),{paddingInline:p})}}]},jr=e=>{const{componentCls:t,motionDurationSlow:n,motionDurationMid:o,motionEaseInOut:r,motionEaseOut:i,iconCls:l,iconSize:s,iconMarginInlineEnd:c}=e;return{[`${t}-item, ${t}-submenu-title`]:{position:"relative",display:"block",margin:0,whiteSpace:"nowrap",cursor:"pointer",transition:[`border-color ${n}`,`background ${n}`,`padding ${n} ${r}`].join(","),[`${t}-item-icon, ${l}`]:{minWidth:s,fontSize:s,transition:[`font-size ${o} ${i}`,`margin ${n} ${r}`,`color ${n}`].join(","),"+ span":{marginInlineStart:c,opacity:1,transition:[`opacity ${n} ${r}`,`margin ${n}`,`color ${n}`].join(",")}},[`${t}-item-icon`]:Object.assign({},dl()),[`&${t}-item-only-child`]:{[`> ${l}, > ${t}-item-icon`]:{marginInlineEnd:0}}},[`${t}-item-disabled, ${t}-submenu-disabled`]:{background:"none !important",cursor:"not-allowed","&::after":{borderColor:"transparent !important"},a:{color:"inherit !important"},[`> ${t}-submenu-title`]:{color:"inherit !important",cursor:"not-allowed"}}}},Mr=e=>{const{componentCls:t,motionDurationSlow:n,motionEaseInOut:o,borderRadius:r,menuArrowSize:i,menuArrowOffset:l}=e;return{[`${t}-submenu`]:{"&-expand-icon, &-arrow":{position:"absolute",top:"50%",insetInlineEnd:e.margin,width:i,color:"currentcolor",transform:"translateY(-50%)",transition:`transform ${n} ${o}, opacity ${n}`},"&-arrow":{"&::before, &::after":{position:"absolute",width:e.calc(i).mul(.6).equal(),height:e.calc(i).mul(.15).equal(),backgroundColor:"currentcolor",borderRadius:r,transition:[`background ${n} ${o}`,`transform ${n} ${o}`,`top ${n} ${o}`,`color ${n} ${o}`].join(","),content:'""'},"&::before":{transform:`rotate(45deg) translateY(${L(e.calc(l).mul(-1).equal())})`},"&::after":{transform:`rotate(-45deg) translateY(${L(l)})`}}}}},Rd=e=>{const{antCls:t,componentCls:n,fontSize:o,motionDurationSlow:r,motionDurationMid:i,motionEaseInOut:l,paddingXS:s,padding:c,colorSplit:d,lineWidth:u,zIndexPopup:m,borderRadiusLG:f,subMenuItemBorderRadius:p,menuArrowSize:g,menuArrowOffset:v,lineType:$,groupTitleLineHeight:h,groupTitleFontSize:b}=e;return[{"":{[n]:Object.assign(Object.assign({},Lt()),{"&-hidden":{display:"none"}})},[`${n}-submenu-hidden`]:{display:"none"}},{[n]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},tt(e)),Lt()),{marginBottom:0,paddingInlineStart:0,fontSize:o,lineHeight:0,listStyle:"none",outline:"none",transition:`width ${r} cubic-bezier(0.2, 0, 0, 1) 0s`,"ul, ol":{margin:0,padding:0,listStyle:"none"},"&-overflow":{display:"flex",[`${n}-item`]:{flex:"none"}},[`${n}-item, ${n}-submenu, ${n}-submenu-title`]:{borderRadius:e.itemBorderRadius},[`${n}-item-group-title`]:{padding:`${L(s)} ${L(c)}`,fontSize:b,lineHeight:h,transition:`all ${r}`},[`&-horizontal ${n}-submenu`]:{transition:[`border-color ${r} ${l}`,`background ${r} ${l}`].join(",")},[`${n}-submenu, ${n}-submenu-inline`]:{transition:[`border-color ${r} ${l}`,`background ${r} ${l}`,`padding ${i} ${l}`].join(",")},[`${n}-submenu ${n}-sub`]:{cursor:"initial",transition:[`background ${r} ${l}`,`padding ${r} ${l}`].join(",")},[`${n}-title-content`]:{transition:`color ${r}`,[`> ${t}-typography-ellipsis-single-line`]:{display:"inline",verticalAlign:"unset"}},[`${n}-item a`]:{"&::before":{position:"absolute",inset:0,backgroundColor:"transparent",content:'""'}},[`${n}-item-divider`]:{overflow:"hidden",lineHeight:0,borderColor:d,borderStyle:$,borderWidth:0,borderTopWidth:u,marginBlock:u,padding:0,"&-dashed":{borderStyle:"dashed"}}}),jr(e)),{[`${n}-item-group`]:{[`${n}-item-group-list`]:{margin:0,padding:0,[`${n}-item, ${n}-submenu-title`]:{paddingInline:`${L(e.calc(o).mul(2).equal())} ${L(c)}`}}},"&-submenu":{"&-popup":{position:"absolute",zIndex:m,borderRadius:f,boxShadow:"none",transformOrigin:"0 0",[`&${n}-submenu`]:{background:"transparent"},"&::before":{position:"absolute",inset:0,zIndex:-1,width:"100%",height:"100%",opacity:0,content:'""'},[`> ${n}`]:Object.assign(Object.assign(Object.assign({borderRadius:f},jr(e)),Mr(e)),{[`${n}-item, ${n}-submenu > ${n}-submenu-title`]:{borderRadius:p},[`${n}-submenu-title::after`]:{transition:`transform ${r} ${l}`}})},"\n          &-placement-leftTop,\n          &-placement-bottomRight,\n          ":{transformOrigin:"100% 0"},"\n          &-placement-leftBottom,\n          &-placement-topRight,\n          ":{transformOrigin:"100% 100%"},"\n          &-placement-rightBottom,\n          &-placement-topLeft,\n          ":{transformOrigin:"0 100%"},"\n          &-placement-bottomLeft,\n          &-placement-rightTop,\n          ":{transformOrigin:"0 0"},"\n          &-placement-leftTop,\n          &-placement-leftBottom\n          ":{paddingInlineEnd:e.paddingXS},"\n          &-placement-rightTop,\n          &-placement-rightBottom\n          ":{paddingInlineStart:e.paddingXS},"\n          &-placement-topRight,\n          &-placement-topLeft\n          ":{paddingBottom:e.paddingXS},"\n          &-placement-bottomRight,\n          &-placement-bottomLeft\n          ":{paddingTop:e.paddingXS}}}),Mr(e)),{[`&-inline-collapsed ${n}-submenu-arrow,
        &-inline ${n}-submenu-arrow`]:{"&::before":{transform:`rotate(-45deg) translateX(${L(v)})`},"&::after":{transform:`rotate(45deg) translateX(${L(e.calc(v).mul(-1).equal())})`}},[`${n}-submenu-open${n}-submenu-inline > ${n}-submenu-title > ${n}-submenu-arrow`]:{transform:`translateY(${L(e.calc(g).mul(.2).mul(-1).equal())})`,"&::after":{transform:`rotate(-45deg) translateX(${L(e.calc(v).mul(-1).equal())})`},"&::before":{transform:`rotate(45deg) translateX(${L(v)})`}}})},{[`${t}-layout-header`]:{[n]:{lineHeight:"inherit"}}}]},jd=e=>{var t,n,o;const{colorPrimary:r,colorError:i,colorTextDisabled:l,colorErrorBg:s,colorText:c,colorTextDescription:d,colorBgContainer:u,colorFillAlter:m,colorFillContent:f,lineWidth:p,lineWidthBold:g,controlItemBgActive:v,colorBgTextHover:$,controlHeightLG:h,lineHeight:b,colorBgElevated:y,marginXXS:S,padding:C,fontSize:w,controlHeightSM:x,fontSizeLG:O,colorTextLightSolid:I,colorErrorHover:P}=e,j=(t=e.activeBarWidth)!==null&&t!==void 0?t:0,N=(n=e.activeBarBorderWidth)!==null&&n!==void 0?n:p,E=(o=e.itemMarginInline)!==null&&o!==void 0?o:e.marginXXS,R=new He(I).setAlpha(.65).toRgbString();return{dropdownWidth:160,zIndexPopup:e.zIndexPopupBase+50,radiusItem:e.borderRadiusLG,itemBorderRadius:e.borderRadiusLG,radiusSubMenuItem:e.borderRadiusSM,subMenuItemBorderRadius:e.borderRadiusSM,colorItemText:c,itemColor:c,colorItemTextHover:c,itemHoverColor:c,colorItemTextHoverHorizontal:r,horizontalItemHoverColor:r,colorGroupTitle:d,groupTitleColor:d,colorItemTextSelected:r,itemSelectedColor:r,colorItemTextSelectedHorizontal:r,horizontalItemSelectedColor:r,colorItemBg:u,itemBg:u,colorItemBgHover:$,itemHoverBg:$,colorItemBgActive:f,itemActiveBg:v,colorSubItemBg:m,subMenuItemBg:m,colorItemBgSelected:v,itemSelectedBg:v,colorItemBgSelectedHorizontal:"transparent",horizontalItemSelectedBg:"transparent",colorActiveBarWidth:0,activeBarWidth:j,colorActiveBarHeight:g,activeBarHeight:g,colorActiveBarBorderSize:p,activeBarBorderWidth:N,colorItemTextDisabled:l,itemDisabledColor:l,colorDangerItemText:i,dangerItemColor:i,colorDangerItemTextHover:i,dangerItemHoverColor:i,colorDangerItemTextSelected:i,dangerItemSelectedColor:i,colorDangerItemBgActive:s,dangerItemActiveBg:s,colorDangerItemBgSelected:s,dangerItemSelectedBg:s,itemMarginInline:E,horizontalItemBorderRadius:0,horizontalItemHoverBg:"transparent",itemHeight:h,groupTitleLineHeight:b,collapsedWidth:h*2,popupBg:y,itemMarginBlock:S,itemPaddingInline:C,horizontalLineHeight:`${h*1.15}px`,iconSize:w,iconMarginInlineEnd:x-w,collapsedIconSize:O,groupTitleFontSize:w,darkItemDisabledColor:new He(I).setAlpha(.25).toRgbString(),darkItemColor:R,darkDangerItemColor:i,darkItemBg:"#001529",darkPopupBg:"#001529",darkSubMenuItemBg:"#000c17",darkItemSelectedColor:I,darkItemSelectedBg:r,darkDangerItemSelectedBg:i,darkItemHoverBg:"transparent",darkGroupTitleColor:R,darkItemHoverColor:I,darkDangerItemHoverColor:P,darkDangerItemSelectedColor:I,darkDangerItemActiveBg:i,itemWidth:j?`calc(100% + ${N}px)`:`calc(100% - ${E*2}px)`}},Md=function(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:e,n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;return We("Menu",r=>{const{colorBgElevated:i,controlHeightLG:l,fontSize:s,darkItemColor:c,darkDangerItemColor:d,darkItemBg:u,darkSubMenuItemBg:m,darkItemSelectedColor:f,darkItemSelectedBg:p,darkDangerItemSelectedBg:g,darkItemHoverBg:v,darkGroupTitleColor:$,darkItemHoverColor:h,darkItemDisabledColor:b,darkDangerItemHoverColor:y,darkDangerItemSelectedColor:S,darkDangerItemActiveBg:C,popupBg:w,darkPopupBg:x}=r,O=r.calc(s).div(7).mul(5).equal(),I=Fe(r,{menuArrowSize:O,menuHorizontalHeight:r.calc(l).mul(1.15).equal(),menuArrowOffset:r.calc(O).mul(.25).equal(),menuSubMenuBg:i,calc:r.calc,popupBg:w}),P=Fe(I,{itemColor:c,itemHoverColor:h,groupTitleColor:$,itemSelectedColor:f,itemBg:u,popupBg:x,subMenuItemBg:m,itemActiveBg:"transparent",itemSelectedBg:p,activeBarHeight:0,activeBarBorderWidth:0,itemHoverBg:v,itemDisabledColor:b,dangerItemColor:d,dangerItemHoverColor:y,dangerItemSelectedColor:S,dangerItemActiveBg:C,dangerItemSelectedBg:g,menuSubMenuBg:m,horizontalItemSelectedColor:f,horizontalItemSelectedBg:p});return[Rd(I),Ed(I),Nd(I),Nr(I,"light"),Nr(P,"dark"),Pd(I),da(I),an(I,"slide-up"),an(I,"slide-down"),Ht(I,"zoom-big")]},jd,{deprecatedTokens:[["colorGroupTitle","groupTitleColor"],["radiusItem","itemBorderRadius"],["radiusSubMenuItem","subMenuItemBorderRadius"],["colorItemText","itemColor"],["colorItemTextHover","itemHoverColor"],["colorItemTextHoverHorizontal","horizontalItemHoverColor"],["colorItemTextSelected","itemSelectedColor"],["colorItemTextSelectedHorizontal","horizontalItemSelectedColor"],["colorItemTextDisabled","itemDisabledColor"],["colorDangerItemText","dangerItemColor"],["colorDangerItemTextHover","dangerItemHoverColor"],["colorDangerItemTextSelected","dangerItemSelectedColor"],["colorDangerItemBgActive","dangerItemActiveBg"],["colorDangerItemBgSelected","dangerItemSelectedBg"],["colorItemBg","itemBg"],["colorItemBgHover","itemHoverBg"],["colorSubItemBg","subMenuItemBg"],["colorItemBgActive","itemActiveBg"],["colorItemBgSelectedHorizontal","horizontalItemSelectedBg"],["colorActiveBarWidth","activeBarWidth"],["colorActiveBarHeight","activeBarHeight"],["colorActiveBarBorderSize","activeBarBorderWidth"],["colorItemBgSelected","itemSelectedBg"]],injectStyle:n,unitless:{groupTitleLineHeight:!0}})(e,t)},ai=e=>{var t;const{popupClassName:n,icon:o,title:r,theme:i}=e,l=a.useContext(dn),{prefixCls:s,inlineCollapsed:c,theme:d}=l,u=Et();let m;if(!o)m=c&&!u.length&&r&&typeof r=="string"?a.createElement("div",{className:`${s}-inline-collapsed-noicon`},r.charAt(0)):a.createElement("span",{className:`${s}-title-content`},r);else{const g=a.isValidElement(r)&&r.type==="span";m=a.createElement(a.Fragment,null,Je(o,{className:M(a.isValidElement(o)?(t=o.props)===null||t===void 0?void 0:t.className:"",`${s}-item-icon`)}),g?r:a.createElement("span",{className:`${s}-title-content`},r))}const f=a.useMemo(()=>Object.assign(Object.assign({},l),{firstLevel:!1}),[l]),[p]=At("Menu");return a.createElement(dn.Provider,{value:f},a.createElement(In,Object.assign({},qe(e,["icon"]),{title:m,popupClassName:M(s,n,`${s}-${i||d}`),popupStyle:{zIndex:p}})))};var Td=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n};function An(e){return e===null||e===!1}const kd={item:ri,submenu:ai,divider:oi},zd=a.forwardRef((e,t)=>{var n;const o=a.useContext(un),r=o||{},{getPrefixCls:i,getPopupContainer:l,direction:s,menu:c}=a.useContext(ue),d=i(),{prefixCls:u,className:m,style:f,theme:p="light",expandIcon:g,_internalDisableMenuItemTitleTooltip:v,inlineCollapsed:$,siderCollapsed:h,rootClassName:b,mode:y,selectable:S,onClick:C,overflowedIndicatorPopupClassName:w}=e,x=Td(e,["prefixCls","className","style","theme","expandIcon","_internalDisableMenuItemTitleTooltip","inlineCollapsed","siderCollapsed","rootClassName","mode","selectable","onClick","overflowedIndicatorPopupClassName"]),O=qe(x,["collapsedWidth"]);(n=r.validator)===null||n===void 0||n.call(r,{mode:y});const I=mo(function(){var A;C==null||C.apply(void 0,arguments),(A=r.onClick)===null||A===void 0||A.call(r)}),P=r.mode||y,j=S??r.selectable,N=a.useMemo(()=>h!==void 0?h:$,[$,h]),E={horizontal:{motionName:`${d}-slide-up`},inline:ua(d),other:{motionName:`${d}-zoom-big`}},R=i("menu",u||r.prefixCls),z=ut(R),[D,T,K]=Md(R,z,!o),U=M(`${R}-${p}`,c==null?void 0:c.className,m),V=a.useMemo(()=>{var A,_;if(typeof g=="function"||An(g))return g||null;if(typeof r.expandIcon=="function"||An(r.expandIcon))return r.expandIcon||null;if(typeof(c==null?void 0:c.expandIcon)=="function"||An(c==null?void 0:c.expandIcon))return(c==null?void 0:c.expandIcon)||null;const B=(A=g??(r==null?void 0:r.expandIcon))!==null&&A!==void 0?A:c==null?void 0:c.expandIcon;return Je(B,{className:M(`${R}-submenu-expand-icon`,a.isValidElement(B)?(_=B.props)===null||_===void 0?void 0:_.className:void 0)})},[g,r==null?void 0:r.expandIcon,c==null?void 0:c.expandIcon,R]),F=a.useMemo(()=>({prefixCls:R,inlineCollapsed:N||!1,direction:s,firstLevel:!0,theme:p,mode:P,disableMenuItemTitleTooltip:v}),[R,N,s,v,p]);return D(a.createElement(un.Provider,{value:null},a.createElement(dn.Provider,{value:F},a.createElement(Xt,Object.assign({getPopupContainer:l,overflowedIndicator:a.createElement(ni,null),overflowedIndicatorPopupClassName:M(R,`${R}-${p}`,w),mode:P,selectable:j,onClick:I},O,{inlineCollapsed:N,style:Object.assign(Object.assign({},c==null?void 0:c.style),f),className:U,prefixCls:R,direction:s,defaultMotions:E,expandIcon:V,ref:t,rootClassName:M(b,T,r.rootClassName,K,z),_internalComponents:kd})))))}),Pt=a.forwardRef((e,t)=>{const n=a.useRef(null),o=a.useContext(On);return a.useImperativeHandle(t,()=>({menu:n.current,focus:r=>{var i;(i=n.current)===null||i===void 0||i.focus(r)}})),a.createElement(zd,Object.assign({ref:n},e,o))});Pt.Item=ri;Pt.SubMenu=ai;Pt.Divider=oi;Pt.ItemGroup=Ro;const Bd=e=>{const{componentCls:t,menuCls:n,colorError:o,colorTextLightSolid:r}=e,i=`${n}-item`;return{[`${t}, ${t}-menu-submenu`]:{[`${n} ${i}`]:{[`&${i}-danger:not(${i}-disabled)`]:{color:o,"&:hover":{color:r,backgroundColor:o}}}}}},Ld=e=>{const{componentCls:t,menuCls:n,zIndexPopup:o,dropdownArrowDistance:r,sizePopupArrow:i,antCls:l,iconCls:s,motionDurationMid:c,paddingBlock:d,fontSize:u,dropdownEdgeChildPadding:m,colorTextDisabled:f,fontSizeIcon:p,controlPaddingHorizontal:g,colorBgElevated:v}=e;return[{[t]:{position:"absolute",top:-9999,left:{_skip_check_:!0,value:-9999},zIndex:o,display:"block","&::before":{position:"absolute",insetBlock:e.calc(i).div(2).sub(r).equal(),zIndex:-9999,opacity:1e-4,content:'""'},[`&-trigger${l}-btn`]:{[`& > ${s}-down, & > ${l}-btn-icon > ${s}-down`]:{fontSize:p}},[`${t}-wrap`]:{position:"relative",[`${l}-btn > ${s}-down`]:{fontSize:p},[`${s}-down::before`]:{transition:`transform ${c}`}},[`${t}-wrap-open`]:{[`${s}-down::before`]:{transform:"rotate(180deg)"}},"\n        &-hidden,\n        &-menu-hidden,\n        &-menu-submenu-hidden\n      ":{display:"none"},[`&${l}-slide-down-enter${l}-slide-down-enter-active${t}-placement-bottomLeft,
          &${l}-slide-down-appear${l}-slide-down-appear-active${t}-placement-bottomLeft,
          &${l}-slide-down-enter${l}-slide-down-enter-active${t}-placement-bottom,
          &${l}-slide-down-appear${l}-slide-down-appear-active${t}-placement-bottom,
          &${l}-slide-down-enter${l}-slide-down-enter-active${t}-placement-bottomRight,
          &${l}-slide-down-appear${l}-slide-down-appear-active${t}-placement-bottomRight`]:{animationName:jl},[`&${l}-slide-up-enter${l}-slide-up-enter-active${t}-placement-topLeft,
          &${l}-slide-up-appear${l}-slide-up-appear-active${t}-placement-topLeft,
          &${l}-slide-up-enter${l}-slide-up-enter-active${t}-placement-top,
          &${l}-slide-up-appear${l}-slide-up-appear-active${t}-placement-top,
          &${l}-slide-up-enter${l}-slide-up-enter-active${t}-placement-topRight,
          &${l}-slide-up-appear${l}-slide-up-appear-active${t}-placement-topRight`]:{animationName:Ml},[`&${l}-slide-down-leave${l}-slide-down-leave-active${t}-placement-bottomLeft,
          &${l}-slide-down-leave${l}-slide-down-leave-active${t}-placement-bottom,
          &${l}-slide-down-leave${l}-slide-down-leave-active${t}-placement-bottomRight`]:{animationName:Tl},[`&${l}-slide-up-leave${l}-slide-up-leave-active${t}-placement-topLeft,
          &${l}-slide-up-leave${l}-slide-up-leave-active${t}-placement-top,
          &${l}-slide-up-leave${l}-slide-up-leave-active${t}-placement-topRight`]:{animationName:kl}}},ra(e,v,{arrowPlacement:{top:!0,bottom:!0}}),{[`${t} ${n}`]:{position:"relative",margin:0},[`${n}-submenu-popup`]:{position:"absolute",zIndex:o,background:"transparent",boxShadow:"none",transformOrigin:"0 0","ul, li":{listStyle:"none",margin:0}},[`${t}, ${t}-menu-submenu`]:Object.assign(Object.assign({},tt(e)),{[n]:Object.assign(Object.assign({padding:m,listStyleType:"none",backgroundColor:v,backgroundClip:"padding-box",borderRadius:e.borderRadiusLG,outline:"none",boxShadow:e.boxShadowSecondary},on(e)),{"&:empty":{padding:0,boxShadow:"none"},[`${n}-item-group-title`]:{padding:`${L(d)} ${L(g)}`,color:e.colorTextDescription,transition:`all ${c}`},[`${n}-item`]:{position:"relative",display:"flex",alignItems:"center",whiteSpace:"nowrap"},[`${n}-item-icon`]:{minWidth:u,marginInlineEnd:e.marginXS,fontSize:e.fontSizeSM},[`${n}-title-content`]:{flex:"auto","> a":{color:"inherit",transition:`all ${c}`,"&:hover":{color:"inherit"},"&::after":{position:"absolute",inset:0,content:'""'}}},[`${n}-item, ${n}-submenu-title`]:Object.assign(Object.assign({clear:"both",margin:0,padding:`${L(d)} ${L(g)}`,color:e.colorText,fontWeight:"normal",fontSize:u,lineHeight:e.lineHeight,cursor:"pointer",transition:`all ${c}`,borderRadius:e.borderRadiusSM,"&:hover, &-active":{backgroundColor:e.controlItemBgHover}},on(e)),{"&-selected":{color:e.colorPrimary,backgroundColor:e.controlItemBgActive,"&:hover, &-active":{backgroundColor:e.controlItemBgActiveHover}},"&-disabled":{color:f,cursor:"not-allowed","&:hover":{color:f,backgroundColor:v,cursor:"not-allowed"},a:{pointerEvents:"none"}},"&-divider":{height:1,margin:`${L(e.marginXXS)} 0`,overflow:"hidden",lineHeight:0,backgroundColor:e.colorSplit},[`${t}-menu-submenu-expand-icon`]:{position:"absolute",insetInlineEnd:e.paddingXS,[`${t}-menu-submenu-arrow-icon`]:{marginInlineEnd:"0 !important",color:e.colorTextDescription,fontSize:p,fontStyle:"normal"}}}),[`${n}-item-group-list`]:{margin:`0 ${L(e.marginXS)}`,padding:0,listStyle:"none"},[`${n}-submenu-title`]:{paddingInlineEnd:e.calc(g).add(e.fontSizeSM).equal()},[`${n}-submenu-vertical`]:{position:"relative"},[`${n}-submenu${n}-submenu-disabled ${t}-menu-submenu-title`]:{[`&, ${t}-menu-submenu-arrow-icon`]:{color:f,backgroundColor:v,cursor:"not-allowed"}},[`${n}-submenu-selected ${t}-menu-submenu-title`]:{color:e.colorPrimary}})})},[an(e,"slide-up"),an(e,"slide-down"),or(e,"move-up"),or(e,"move-down"),Ht(e,"zoom-big")]]},Dd=e=>Object.assign(Object.assign({zIndexPopup:e.zIndexPopupBase+50,paddingBlock:(e.controlHeight-e.fontSize*e.lineHeight)/2},ia({contentRadius:e.borderRadiusLG,limitVerticalRadius:!0})),aa(e)),_d=We("Dropdown",e=>{const{marginXXS:t,sizePopupArrow:n,paddingXXS:o,componentCls:r}=e,i=Fe(e,{menuCls:`${r}-menu`,dropdownArrowDistance:e.calc(n).div(2).add(t).equal(),dropdownEdgeChildPadding:o});return[Ld(i),Bd(i)]},Dd,{resetStyle:!1}),En=e=>{var t;const{menu:n,arrow:o,prefixCls:r,children:i,trigger:l,disabled:s,dropdownRender:c,getPopupContainer:d,overlayClassName:u,rootClassName:m,overlayStyle:f,open:p,onOpenChange:g,visible:v,onVisibleChange:$,mouseEnterDelay:h=.15,mouseLeaveDelay:b=.1,autoAdjustOverflow:y=!0,placement:S="",overlay:C,transitionName:w}=e,{getPopupContainer:x,getPrefixCls:O,direction:I,dropdown:P}=a.useContext(ue);ul();const j=a.useMemo(()=>{const Y=O();return w!==void 0?w:S.includes("top")?`${Y}-slide-down`:`${Y}-slide-up`},[O,S,w]),N=a.useMemo(()=>S?S.includes("Center")?S.slice(0,S.indexOf("Center")):S:I==="rtl"?"bottomRight":"bottomLeft",[S,I]),E=O("dropdown",r),R=ut(E),[z,D,T]=_d(E,R),[,K]=fo(),U=a.Children.only(i),V=Je(U,{className:M(`${E}-trigger`,{[`${E}-rtl`]:I==="rtl"},U.props.className),disabled:(t=U.props.disabled)!==null&&t!==void 0?t:s}),F=s?[]:l,A=!!(F!=null&&F.includes("contextMenu")),[_,B]=Ve(!1,{value:p??v}),X=mo(Y=>{g==null||g(Y,{source:"trigger"}),$==null||$(Y),B(Y)}),Q=M(u,m,D,T,R,P==null?void 0:P.className,{[`${E}-rtl`]:I==="rtl"}),q=zl({arrowPointAtCenter:typeof o=="object"&&o.pointAtCenter,autoAdjustOverflow:y,offset:K.marginXXS,arrowWidth:o?K.sizePopupArrow:0,borderRadius:K.borderRadius}),H=a.useCallback(()=>{n!=null&&n.selectable&&(n!=null&&n.multiple)||(g==null||g(!1,{source:"menu"}),B(!1))},[n==null?void 0:n.selectable,n==null?void 0:n.multiple]),W=()=>{let Y;return n!=null&&n.items?Y=a.createElement(Pt,Object.assign({},n)):typeof C=="function"?Y=C():Y=C,c&&(Y=c(Y)),Y=a.Children.only(typeof Y=="string"?a.createElement("span",null,Y):Y),a.createElement(Od,{prefixCls:`${E}-menu`,rootClassName:M(T,R),expandIcon:a.createElement("span",{className:`${E}-menu-submenu-arrow`},a.createElement(sn,{className:`${E}-menu-submenu-arrow-icon`})),mode:"vertical",selectable:!1,onClick:H,validator:ie=>{}},Y)},[G,le]=At("Dropdown",f==null?void 0:f.zIndex);let ae=a.createElement(Mc,Object.assign({alignPoint:A},qe(e,["rootClassName"]),{mouseEnterDelay:h,mouseLeaveDelay:b,visible:_,builtinPlacements:q,arrow:!!o,overlayClassName:Q,prefixCls:E,getPopupContainer:d||x,transitionName:j,trigger:F,overlay:W,placement:N,onVisibleChange:X,overlayStyle:Object.assign(Object.assign(Object.assign({},P==null?void 0:P.style),f),{zIndex:G})}),V);return G&&(ae=a.createElement(Ur.Provider,{value:le},ae)),z(ae)};function Ad(e){return Object.assign(Object.assign({},e),{align:{overflow:{adjustX:!1,adjustY:!1}}})}const Hd=Bl(En,"dropdown",e=>e,Ad),Fd=e=>a.createElement(Hd,Object.assign({},e),a.createElement("span",null));En._InternalPanelDoNotUseOrYouWillBeFired=Fd;var Wd={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M482 152h60q8 0 8 8v704q0 8-8 8h-60q-8 0-8-8V160q0-8 8-8z"}},{tag:"path",attrs:{d:"M192 474h672q8 0 8 8v60q0 8-8 8H160q-8 0-8-8v-60q0-8 8-8z"}}]},name:"plus",theme:"outlined"},Vd=function(t,n){return a.createElement(ke,te({},t,{ref:n,icon:Wd}))},Xd=a.forwardRef(Vd);function Tr(e){return["small","middle","large"].includes(e)}function kr(e){return e?typeof e=="number"&&!Number.isNaN(e):!1}const ii=Z.createContext({latestIndex:0}),qd=ii.Provider,Ud=e=>{let{className:t,index:n,children:o,split:r,style:i}=e;const{latestIndex:l}=a.useContext(ii);return o==null?null:a.createElement(a.Fragment,null,a.createElement("div",{className:t,style:i},o),n<l&&r&&a.createElement("span",{className:`${t}-split`},r))};var Kd=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n};const Gd=a.forwardRef((e,t)=>{var n,o,r;const{getPrefixCls:i,space:l,direction:s}=a.useContext(ue),{size:c=(n=l==null?void 0:l.size)!==null&&n!==void 0?n:"small",align:d,className:u,rootClassName:m,children:f,direction:p="horizontal",prefixCls:g,split:v,style:$,wrap:h=!1,classNames:b,styles:y}=e,S=Kd(e,["size","align","className","rootClassName","children","direction","prefixCls","split","style","wrap","classNames","styles"]),[C,w]=Array.isArray(c)?c:[c,c],x=Tr(w),O=Tr(C),I=kr(w),P=kr(C),j=Ft(f,{keepEmpty:!0}),N=d===void 0&&p==="horizontal"?"center":d,E=i("space",g),[R,z,D]=Dl(E),T=M(E,l==null?void 0:l.className,z,`${E}-${p}`,{[`${E}-rtl`]:s==="rtl",[`${E}-align-${N}`]:N,[`${E}-gap-row-${w}`]:x,[`${E}-gap-col-${C}`]:O},u,m,D),K=M(`${E}-item`,(o=b==null?void 0:b.item)!==null&&o!==void 0?o:(r=l==null?void 0:l.classNames)===null||r===void 0?void 0:r.item);let U=0;const V=j.map((_,B)=>{var X,Q;_!=null&&(U=B);const q=(_==null?void 0:_.key)||`${K}-${B}`;return a.createElement(Ud,{className:K,key:q,index:B,split:v,style:(X=y==null?void 0:y.item)!==null&&X!==void 0?X:(Q=l==null?void 0:l.styles)===null||Q===void 0?void 0:Q.item},_)}),F=a.useMemo(()=>({latestIndex:U}),[U]);if(j.length===0)return null;const A={};return h&&(A.flexWrap="wrap"),!O&&P&&(A.columnGap=C),!x&&I&&(A.rowGap=w),R(a.createElement("div",Object.assign({ref:t,className:T,style:Object.assign(Object.assign(Object.assign({},A),l==null?void 0:l.style),$)},S),a.createElement(qd,{value:F},V)))}),jo=Gd;jo.Compact=Ll;var Yd=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n};const li=e=>{const{getPopupContainer:t,getPrefixCls:n,direction:o}=a.useContext(ue),{prefixCls:r,type:i="default",danger:l,disabled:s,loading:c,onClick:d,htmlType:u,children:m,className:f,menu:p,arrow:g,autoFocus:v,overlay:$,trigger:h,align:b,open:y,onOpenChange:S,placement:C,getPopupContainer:w,href:x,icon:O=a.createElement(ni,null),title:I,buttonsRender:P=H=>H,mouseEnterDelay:j,mouseLeaveDelay:N,overlayClassName:E,overlayStyle:R,destroyPopupOnHide:z,dropdownRender:D}=e,T=Yd(e,["prefixCls","type","danger","disabled","loading","onClick","htmlType","children","className","menu","arrow","autoFocus","overlay","trigger","align","open","onOpenChange","placement","getPopupContainer","href","icon","title","buttonsRender","mouseEnterDelay","mouseLeaveDelay","overlayClassName","overlayStyle","destroyPopupOnHide","dropdownRender"]),K=n("dropdown",r),U=`${K}-button`,V={menu:p,arrow:g,autoFocus:v,align:b,disabled:s,trigger:s?[]:h,onOpenChange:S,getPopupContainer:w||t,mouseEnterDelay:j,mouseLeaveDelay:N,overlayClassName:E,overlayStyle:R,destroyPopupOnHide:z,dropdownRender:D},{compactSize:F,compactItemClassnames:A}=_l(K,o),_=M(U,A,f);"overlay"in e&&(V.overlay=$),"open"in e&&(V.open=y),"placement"in e?V.placement=C:V.placement=o==="rtl"?"bottomLeft":"bottomRight";const B=a.createElement(ht,{type:i,danger:l,disabled:s,loading:c,onClick:d,htmlType:u,href:x,title:I},m),X=a.createElement(ht,{type:i,danger:l,icon:O}),[Q,q]=P([B,X]);return a.createElement(jo.Compact,Object.assign({className:_,size:F,block:!0},T),Q,a.createElement(En,Object.assign({},V),q))};li.__ANT_BUTTON=!0;const si=En;si.Button=li;function ci(){var e=document.documentElement.clientWidth,t=window.innerHeight||document.documentElement.clientHeight;return{width:e,height:t}}function Zd(e){var t=e.getBoundingClientRect(),n=document.documentElement;return{left:t.left+(window.pageXOffset||n.scrollLeft)-(n.clientLeft||document.body.clientLeft||0),top:t.top+(window.pageYOffset||n.scrollTop)-(n.clientTop||document.body.clientTop||0)}}function St(e,t,n,o){var r=Yo.unstable_batchedUpdates?function(l){Yo.unstable_batchedUpdates(n,l)}:n;return e!=null&&e.addEventListener&&e.addEventListener(t,r,o),{remove:function(){e!=null&&e.removeEventListener&&e.removeEventListener(t,r,o)}}}var qt=a.createContext(null),Qd=function(t){var n=t.visible,o=t.maskTransitionName,r=t.getContainer,i=t.prefixCls,l=t.rootClassName,s=t.icons,c=t.countRender,d=t.showSwitch,u=t.showProgress,m=t.current,f=t.transform,p=t.count,g=t.scale,v=t.minScale,$=t.maxScale,h=t.closeIcon,b=t.onSwitchLeft,y=t.onSwitchRight,S=t.onClose,C=t.onZoomIn,w=t.onZoomOut,x=t.onRotateRight,O=t.onRotateLeft,I=t.onFlipX,P=t.onFlipY,j=t.onReset,N=t.toolbarRender,E=t.zIndex,R=t.image,z=a.useContext(qt),D=s.rotateLeft,T=s.rotateRight,K=s.zoomIn,U=s.zoomOut,V=s.close,F=s.left,A=s.right,_=s.flipX,B=s.flipY,X="".concat(i,"-operations-operation");a.useEffect(function(){var W=function(le){le.keyCode===Ee.ESC&&S()};return n&&window.addEventListener("keydown",W),function(){window.removeEventListener("keydown",W)}},[n]);var Q=[{icon:B,onClick:P,type:"flipY"},{icon:_,onClick:I,type:"flipX"},{icon:D,onClick:O,type:"rotateLeft"},{icon:T,onClick:x,type:"rotateRight"},{icon:U,onClick:w,type:"zoomOut",disabled:g<=v},{icon:K,onClick:C,type:"zoomIn",disabled:g===$}],q=Q.map(function(W){var G,le=W.icon,ae=W.onClick,Y=W.type,ie=W.disabled;return a.createElement("div",{className:M(X,(G={},ee(G,"".concat(i,"-operations-operation-").concat(Y),!0),ee(G,"".concat(i,"-operations-operation-disabled"),!!ie),G)),onClick:ae,key:Y},le)}),H=a.createElement("div",{className:"".concat(i,"-operations")},q);return a.createElement(bt,{visible:n,motionName:o},function(W){var G=W.className,le=W.style;return a.createElement(ta,{open:!0,getContainer:r??document.body},a.createElement("div",{className:M("".concat(i,"-operations-wrapper"),G,l),style:J(J({},le),{},{zIndex:E})},h===null?null:a.createElement("button",{className:"".concat(i,"-close"),onClick:S},h||V),d&&a.createElement(a.Fragment,null,a.createElement("div",{className:M("".concat(i,"-switch-left"),ee({},"".concat(i,"-switch-left-disabled"),m===0)),onClick:b},F),a.createElement("div",{className:M("".concat(i,"-switch-right"),ee({},"".concat(i,"-switch-right-disabled"),m===p-1)),onClick:y},A)),a.createElement("div",{className:"".concat(i,"-footer")},u&&a.createElement("div",{className:"".concat(i,"-progress")},c?c(m+1,p):"".concat(m+1," / ").concat(p)),N?N(H,J(J({icons:{flipYIcon:q[0],flipXIcon:q[1],rotateLeftIcon:q[2],rotateRightIcon:q[3],zoomOutIcon:q[4],zoomInIcon:q[5]},actions:{onFlipY:P,onFlipX:I,onRotateLeft:O,onRotateRight:x,onZoomOut:w,onZoomIn:C,onReset:j,onClose:S},transform:f},z?{current:m,total:p}:{}),{},{image:R})):H)))})},Qt={x:0,y:0,rotate:0,scale:1,flipX:!1,flipY:!1};function Jd(e,t,n,o){var r=a.useRef(null),i=a.useRef([]),l=a.useState(Qt),s=ce(l,2),c=s[0],d=s[1],u=function(g){d(Qt),po(Qt,c)||o==null||o({transform:Qt,action:g})},m=function(g,v){r.current===null&&(i.current=[],r.current=vt(function(){d(function($){var h=$;return i.current.forEach(function(b){h=J(J({},h),b)}),r.current=null,o==null||o({transform:h,action:v}),h})})),i.current.push(J(J({},c),g))},f=function(g,v,$,h,b){var y=e.current,S=y.width,C=y.height,w=y.offsetWidth,x=y.offsetHeight,O=y.offsetLeft,I=y.offsetTop,P=g,j=c.scale*g;j>n?(j=n,P=n/c.scale):j<t&&(j=b?j:t,P=j/c.scale);var N=$??innerWidth/2,E=h??innerHeight/2,R=P-1,z=R*S*.5,D=R*C*.5,T=R*(N-c.x-O),K=R*(E-c.y-I),U=c.x-(T-z),V=c.y-(K-D);if(g<1&&j===1){var F=w*j,A=x*j,_=ci(),B=_.width,X=_.height;F<=B&&A<=X&&(U=0,V=0)}m({x:U,y:V,scale:j},v)};return{transform:c,resetTransform:u,updateTransform:m,dispatchZoomChange:f}}function zr(e,t,n,o){var r=t+n,i=(n-o)/2;if(n>o){if(t>0)return ee({},e,i);if(t<0&&r<o)return ee({},e,-i)}else if(t<0||r>o)return ee({},e,t<0?i:-i);return{}}function di(e,t,n,o){var r=ci(),i=r.width,l=r.height,s=null;return e<=i&&t<=l?s={x:0,y:0}:(e>i||t>l)&&(s=J(J({},zr("x",n,e,i)),zr("y",o,t,l))),s}var wt=1,eu=1;function tu(e,t,n,o,r,i,l){var s=r.rotate,c=r.scale,d=r.x,u=r.y,m=a.useState(!1),f=ce(m,2),p=f[0],g=f[1],v=a.useRef({diffX:0,diffY:0,transformX:0,transformY:0}),$=function(C){!t||C.button!==0||(C.preventDefault(),C.stopPropagation(),v.current={diffX:C.pageX-d,diffY:C.pageY-u,transformX:d,transformY:u},g(!0))},h=function(C){n&&p&&i({x:C.pageX-v.current.diffX,y:C.pageY-v.current.diffY},"move")},b=function(){if(n&&p){g(!1);var C=v.current,w=C.transformX,x=C.transformY,O=d!==w&&u!==x;if(!O)return;var I=e.current.offsetWidth*c,P=e.current.offsetHeight*c,j=e.current.getBoundingClientRect(),N=j.left,E=j.top,R=s%180!==0,z=di(R?P:I,R?I:P,N,E);z&&i(J({},z),"dragRebound")}},y=function(C){if(!(!n||C.deltaY==0)){var w=Math.abs(C.deltaY/100),x=Math.min(w,eu),O=wt+x*o;C.deltaY>0&&(O=wt/O),l(O,"wheel",C.clientX,C.clientY)}};return a.useEffect(function(){var S,C,w,x;if(t){w=St(window,"mouseup",b,!1),x=St(window,"mousemove",h,!1);try{window.top!==window.self&&(S=St(window.top,"mouseup",b,!1),C=St(window.top,"mousemove",h,!1))}catch{}}return function(){var O,I,P,j;(O=w)===null||O===void 0||O.remove(),(I=x)===null||I===void 0||I.remove(),(P=S)===null||P===void 0||P.remove(),(j=C)===null||j===void 0||j.remove()}},[n,p,d,u,s,t]),{isMoving:p,onMouseDown:$,onMouseMove:h,onMouseUp:b,onWheel:y}}function nu(e){return new Promise(function(t){var n=document.createElement("img");n.onerror=function(){return t(!1)},n.onload=function(){return t(!0)},n.src=e})}function ui(e){var t=e.src,n=e.isCustomPlaceholder,o=e.fallback,r=a.useState(n?"loading":"normal"),i=ce(r,2),l=i[0],s=i[1],c=a.useRef(!1),d=l==="error";a.useEffect(function(){var p=!0;return nu(t).then(function(g){!g&&p&&s("error")}),function(){p=!1}},[t]),a.useEffect(function(){n&&!c.current?s("loading"):d&&s("normal")},[t]);var u=function(){s("normal")},m=function(g){c.current=!1,l==="loading"&&g!==null&&g!==void 0&&g.complete&&(g.naturalWidth||g.naturalHeight)&&(c.current=!0,u())},f=d&&o?{src:o}:{onLoad:u,src:t};return[m,f,l]}function mn(e,t){var n=e.x-t.x,o=e.y-t.y;return Math.hypot(n,o)}function ou(e,t,n,o){var r=mn(e,n),i=mn(t,o);if(r===0&&i===0)return[e.x,e.y];var l=r/(r+i),s=e.x+l*(t.x-e.x),c=e.y+l*(t.y-e.y);return[s,c]}function ru(e,t,n,o,r,i,l){var s=r.rotate,c=r.scale,d=r.x,u=r.y,m=a.useState(!1),f=ce(m,2),p=f[0],g=f[1],v=a.useRef({point1:{x:0,y:0},point2:{x:0,y:0},eventType:"none"}),$=function(C){v.current=J(J({},v.current),C)},h=function(C){if(t){C.stopPropagation(),g(!0);var w=C.touches,x=w===void 0?[]:w;x.length>1?$({point1:{x:x[0].clientX,y:x[0].clientY},point2:{x:x[1].clientX,y:x[1].clientY},eventType:"touchZoom"}):$({point1:{x:x[0].clientX-d,y:x[0].clientY-u},eventType:"move"})}},b=function(C){var w=C.touches,x=w===void 0?[]:w,O=v.current,I=O.point1,P=O.point2,j=O.eventType;if(x.length>1&&j==="touchZoom"){var N={x:x[0].clientX,y:x[0].clientY},E={x:x[1].clientX,y:x[1].clientY},R=ou(I,P,N,E),z=ce(R,2),D=z[0],T=z[1],K=mn(N,E)/mn(I,P);l(K,"touchZoom",D,T,!0),$({point1:N,point2:E,eventType:"touchZoom"})}else j==="move"&&(i({x:x[0].clientX-I.x,y:x[0].clientY-I.y},"move"),$({eventType:"move"}))},y=function(){if(n){if(p&&g(!1),$({eventType:"none"}),o>c)return i({x:0,y:0,scale:o},"touchZoom");var C=e.current.offsetWidth*c,w=e.current.offsetHeight*c,x=e.current.getBoundingClientRect(),O=x.left,I=x.top,P=s%180!==0,j=di(P?w:C,P?C:w,O,I);j&&i(J({},j),"dragRebound")}};return a.useEffect(function(){var S;return n&&t&&(S=St(window,"touchmove",function(C){return C.preventDefault()},{passive:!1})),function(){var C;(C=S)===null||C===void 0||C.remove()}},[n,t]),{isTouching:p,onTouchStart:h,onTouchMove:b,onTouchEnd:y}}var au=["fallback","src","imgRef"],iu=["prefixCls","src","alt","imageInfo","fallback","movable","onClose","visible","icons","rootClassName","closeIcon","getContainer","current","count","countRender","scaleStep","minScale","maxScale","transitionName","maskTransitionName","imageRender","imgCommonProps","toolbarRender","onTransform","onChange"],lu=function(t){var n=t.fallback,o=t.src,r=t.imgRef,i=Oe(t,au),l=ui({src:o,fallback:n}),s=ce(l,2),c=s[0],d=s[1];return Z.createElement("img",te({ref:function(m){r.current=m,c(m)}},i,d))},mi=function(t){var n=t.prefixCls,o=t.src,r=t.alt,i=t.imageInfo,l=t.fallback,s=t.movable,c=s===void 0?!0:s,d=t.onClose,u=t.visible,m=t.icons,f=m===void 0?{}:m,p=t.rootClassName,g=t.closeIcon,v=t.getContainer,$=t.current,h=$===void 0?0:$,b=t.count,y=b===void 0?1:b,S=t.countRender,C=t.scaleStep,w=C===void 0?.5:C,x=t.minScale,O=x===void 0?1:x,I=t.maxScale,P=I===void 0?50:I,j=t.transitionName,N=j===void 0?"zoom":j,E=t.maskTransitionName,R=E===void 0?"fade":E,z=t.imageRender,D=t.imgCommonProps,T=t.toolbarRender,K=t.onTransform,U=t.onChange,V=Oe(t,iu),F=a.useRef(),A=a.useContext(qt),_=A&&y>1,B=A&&y>=1,X=a.useState(!0),Q=ce(X,2),q=Q[0],H=Q[1],W=Jd(F,O,P,K),G=W.transform,le=W.resetTransform,ae=W.updateTransform,Y=W.dispatchZoomChange,ie=tu(F,c,u,w,G,ae,Y),re=ie.isMoving,xe=ie.onMouseDown,he=ie.onWheel,$e=ru(F,c,u,O,G,ae,Y),Ce=$e.isTouching,Se=$e.onTouchStart,Pe=$e.onTouchMove,Re=$e.onTouchEnd,Ne=G.rotate,pe=G.scale,nt=M(ee({},"".concat(n,"-moving"),re));a.useEffect(function(){q||H(!0)},[q]);var ot=function(){le("close")},Ze=function(){Y(wt+w,"zoomIn")},ze=function(){Y(wt/(wt+w),"zoomOut")},Me=function(){ae({rotate:Ne+90},"rotateRight")},je=function(){ae({rotate:Ne-90},"rotateLeft")},Be=function(){ae({flipX:!G.flipX},"flipX")},Le=function(){ae({flipY:!G.flipY},"flipY")},Qe=function(){le("reset")},oe=function(fe){fe==null||fe.preventDefault(),fe==null||fe.stopPropagation(),h>0&&(H(!1),le("prev"),U==null||U(h-1,h))},ne=function(fe){fe==null||fe.preventDefault(),fe==null||fe.stopPropagation(),h<y-1&&(H(!1),le("next"),U==null||U(h+1,h))},de=function(fe){!u||!_||(fe.keyCode===Ee.LEFT?oe():fe.keyCode===Ee.RIGHT&&ne())},se=function(fe){u&&(pe!==1?ae({x:0,y:0,scale:1},"doubleClick"):Y(wt+w,"doubleClick",fe.clientX,fe.clientY))};a.useEffect(function(){var ye=St(window,"keydown",de,!1);return function(){ye.remove()}},[u,_,h]);var me=Z.createElement(lu,te({},D,{width:t.width,height:t.height,imgRef:F,className:"".concat(n,"-img"),alt:r,style:{transform:"translate3d(".concat(G.x,"px, ").concat(G.y,"px, 0) scale3d(").concat(G.flipX?"-":"").concat(pe,", ").concat(G.flipY?"-":"").concat(pe,", 1) rotate(").concat(Ne,"deg)"),transitionDuration:(!q||Ce)&&"0s"},fallback:l,src:o,onWheel:he,onMouseDown:xe,onDoubleClick:se,onTouchStart:Se,onTouchMove:Pe,onTouchEnd:Re,onTouchCancel:Re})),Ie=J({url:o,alt:r},i);return Z.createElement(Z.Fragment,null,Z.createElement(So,te({transitionName:N,maskTransitionName:R,closable:!1,keyboard:!0,prefixCls:n,onClose:d,visible:u,classNames:{wrapper:nt},rootClassName:p,getContainer:v},V,{afterClose:ot}),Z.createElement("div",{className:"".concat(n,"-img-wrapper")},z?z(me,J({transform:G,image:Ie},A?{current:h}:{})):me)),Z.createElement(Qd,{visible:u,transform:G,maskTransitionName:R,closeIcon:g,getContainer:v,prefixCls:n,rootClassName:p,icons:f,countRender:S,showSwitch:_,showProgress:B,current:h,count:y,scale:pe,minScale:O,maxScale:P,toolbarRender:T,onSwitchLeft:oe,onSwitchRight:ne,onZoomIn:Ze,onZoomOut:ze,onRotateRight:Me,onRotateLeft:je,onFlipX:Be,onFlipY:Le,onClose:d,onReset:Qe,zIndex:V.zIndex!==void 0?V.zIndex+1:void 0,image:Ie}))},lo=["crossOrigin","decoding","draggable","loading","referrerPolicy","sizes","srcSet","useMap","alt"];function su(e){var t=a.useState({}),n=ce(t,2),o=n[0],r=n[1],i=a.useCallback(function(s,c){return r(function(d){return J(J({},d),{},ee({},s,c))}),function(){r(function(d){var u=J({},d);return delete u[s],u})}},[]),l=a.useMemo(function(){return e?e.map(function(s){if(typeof s=="string")return{data:{src:s}};var c={};return Object.keys(s).forEach(function(d){["src"].concat(ve(lo)).includes(d)&&(c[d]=s[d])}),{data:c}}):Object.keys(o).reduce(function(s,c){var d=o[c],u=d.canPreview,m=d.data;return u&&s.push({data:m,id:c}),s},[])},[e,o]);return[l,i,!!e]}var cu=["visible","onVisibleChange","getContainer","current","movable","minScale","maxScale","countRender","closeIcon","onChange","onTransform","toolbarRender","imageRender"],du=["src"],uu=function(t){var n,o=t.previewPrefixCls,r=o===void 0?"rc-image-preview":o,i=t.children,l=t.icons,s=l===void 0?{}:l,c=t.items,d=t.preview,u=t.fallback,m=Ke(d)==="object"?d:{},f=m.visible,p=m.onVisibleChange,g=m.getContainer,v=m.current,$=m.movable,h=m.minScale,b=m.maxScale,y=m.countRender,S=m.closeIcon,C=m.onChange,w=m.onTransform,x=m.toolbarRender,O=m.imageRender,I=Oe(m,cu),P=su(c),j=ce(P,3),N=j[0],E=j[1],R=j[2],z=Ve(0,{value:v}),D=ce(z,2),T=D[0],K=D[1],U=a.useState(!1),V=ce(U,2),F=V[0],A=V[1],_=((n=N[T])===null||n===void 0?void 0:n.data)||{},B=_.src,X=Oe(_,du),Q=Ve(!!f,{value:f,onChange:function(Ce,Se){p==null||p(Ce,Se,T)}}),q=ce(Q,2),H=q[0],W=q[1],G=a.useState(null),le=ce(G,2),ae=le[0],Y=le[1],ie=a.useCallback(function($e,Ce,Se,Pe){var Re=R?N.findIndex(function(Ne){return Ne.data.src===Ce}):N.findIndex(function(Ne){return Ne.id===$e});K(Re<0?0:Re),W(!0),Y({x:Se,y:Pe}),A(!0)},[N,R]);a.useEffect(function(){H?F||K(0):A(!1)},[H]);var re=function(Ce,Se){K(Ce),C==null||C(Ce,Se)},xe=function(){W(!1),Y(null)},he=a.useMemo(function(){return{register:E,onPreview:ie}},[E,ie]);return a.createElement(qt.Provider,{value:he},i,a.createElement(mi,te({"aria-hidden":!H,movable:$,visible:H,prefixCls:r,closeIcon:S,onClose:xe,mousePosition:ae,imgCommonProps:X,src:B,fallback:u,icons:s,minScale:h,maxScale:b,getContainer:g,current:T,count:N.length,countRender:y,onTransform:w,toolbarRender:x,imageRender:O,onChange:re},I)))},Br=0;function mu(e,t){var n=a.useState(function(){return Br+=1,String(Br)}),o=ce(n,1),r=o[0],i=a.useContext(qt),l={data:t,canPreview:e};return a.useEffect(function(){if(i)return i.register(r,l)},[]),a.useEffect(function(){i&&i.register(r,l)},[e,t]),r}var fu=["src","alt","onPreviewClose","prefixCls","previewPrefixCls","placeholder","fallback","width","height","style","preview","className","onClick","onError","wrapperClassName","wrapperStyle","rootClassName"],gu=["src","visible","onVisibleChange","getContainer","mask","maskClassName","movable","icons","scaleStep","minScale","maxScale","imageRender","toolbarRender"],Mo=function(t){var n=t.src,o=t.alt,r=t.onPreviewClose,i=t.prefixCls,l=i===void 0?"rc-image":i,s=t.previewPrefixCls,c=s===void 0?"".concat(l,"-preview"):s,d=t.placeholder,u=t.fallback,m=t.width,f=t.height,p=t.style,g=t.preview,v=g===void 0?!0:g,$=t.className,h=t.onClick,b=t.onError,y=t.wrapperClassName,S=t.wrapperStyle,C=t.rootClassName,w=Oe(t,fu),x=d&&d!==!0,O=Ke(v)==="object"?v:{},I=O.src,P=O.visible,j=P===void 0?void 0:P,N=O.onVisibleChange,E=N===void 0?r:N,R=O.getContainer,z=R===void 0?void 0:R,D=O.mask,T=O.maskClassName,K=O.movable,U=O.icons,V=O.scaleStep,F=O.minScale,A=O.maxScale,_=O.imageRender,B=O.toolbarRender,X=Oe(O,gu),Q=I??n,q=Ve(!!j,{value:j,onChange:E}),H=ce(q,2),W=H[0],G=H[1],le=ui({src:n,isCustomPlaceholder:x,fallback:u}),ae=ce(le,3),Y=ae[0],ie=ae[1],re=ae[2],xe=a.useState(null),he=ce(xe,2),$e=he[0],Ce=he[1],Se=a.useContext(qt),Pe=!!v,Re=function(){G(!1),Ce(null)},Ne=M(l,y,C,ee({},"".concat(l,"-error"),re==="error")),pe=a.useMemo(function(){var ze={};return lo.forEach(function(Me){t[Me]!==void 0&&(ze[Me]=t[Me])}),ze},lo.map(function(ze){return t[ze]})),nt=a.useMemo(function(){return J(J({},pe),{},{src:Q})},[Q,pe]),ot=mu(Pe,nt),Ze=function(Me){var je=Zd(Me.target),Be=je.left,Le=je.top;Se?Se.onPreview(ot,Q,Be,Le):(Ce({x:Be,y:Le}),G(!0)),h==null||h(Me)};return a.createElement(a.Fragment,null,a.createElement("div",te({},w,{className:Ne,onClick:Pe?Ze:h,style:J({width:m,height:f},S)}),a.createElement("img",te({},pe,{className:M("".concat(l,"-img"),ee({},"".concat(l,"-img-placeholder"),d===!0),$),style:J({height:f},p),ref:Y},ie,{width:m,height:f,onError:b})),re==="loading"&&a.createElement("div",{"aria-hidden":"true",className:"".concat(l,"-placeholder")},d),D&&Pe&&a.createElement("div",{className:M("".concat(l,"-mask"),T),style:{display:(p==null?void 0:p.display)==="none"?"none":void 0}},D)),!Se&&Pe&&a.createElement(mi,te({"aria-hidden":!W,visible:W,prefixCls:c,onClose:Re,mousePosition:$e,src:Q,alt:o,imageInfo:{width:m,height:f},fallback:u,getContainer:z,icons:U,movable:K,scaleStep:V,minScale:F,maxScale:A,rootClassName:C,imageRender:_,imgCommonProps:pe,toolbarRender:B},X)))};Mo.PreviewGroup=uu;var pu={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M672 418H144c-17.7 0-32 14.3-32 32v414c0 17.7 14.3 32 32 32h528c17.7 0 32-14.3 32-32V450c0-17.7-14.3-32-32-32zm-44 402H188V494h440v326z"}},{tag:"path",attrs:{d:"M819.3 328.5c-78.8-100.7-196-153.6-314.6-154.2l-.2-64c0-6.5-7.6-10.1-12.6-6.1l-128 101c-4 3.1-3.9 9.1 0 12.3L492 318.6c5.1 4 12.7.4 12.6-6.1v-63.9c12.9.1 25.9.9 38.8 2.5 42.1 5.2 82.1 18.2 119 38.7 38.1 21.2 71.2 49.7 98.4 84.3 27.1 34.7 46.7 73.7 58.1 115.8a325.95 325.95 0 016.5 140.9h74.9c14.8-103.6-11.3-213-81-302.3z"}}]},name:"rotate-left",theme:"outlined"},vu=function(t,n){return a.createElement(ke,te({},t,{ref:n,icon:pu}))},hu=a.forwardRef(vu),bu={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M480.5 251.2c13-1.6 25.9-2.4 38.8-2.5v63.9c0 6.5 7.5 10.1 12.6 6.1L660 217.6c4-3.2 4-9.2 0-12.3l-128-101c-5.1-4-12.6-.4-12.6 6.1l-.2 64c-118.6.5-235.8 53.4-314.6 154.2A399.75 399.75 0 00123.5 631h74.9c-.9-5.3-1.7-10.7-2.4-16.1-5.1-42.1-2.1-84.1 8.9-124.8 11.4-42.2 31-81.1 58.1-115.8 27.2-34.7 60.3-63.2 98.4-84.3 37-20.6 76.9-33.6 119.1-38.8z"}},{tag:"path",attrs:{d:"M880 418H352c-17.7 0-32 14.3-32 32v414c0 17.7 14.3 32 32 32h528c17.7 0 32-14.3 32-32V450c0-17.7-14.3-32-32-32zm-44 402H396V494h440v326z"}}]},name:"rotate-right",theme:"outlined"},Cu=function(t,n){return a.createElement(ke,te({},t,{ref:n,icon:bu}))},yu=a.forwardRef(Cu),$u={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M847.9 592H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h605.2L612.9 851c-4.1 5.2-.4 13 6.3 13h72.5c4.9 0 9.5-2.2 12.6-6.1l168.8-214.1c16.5-21 1.6-51.8-25.2-51.8zM872 356H266.8l144.3-183c4.1-5.2.4-13-6.3-13h-72.5c-4.9 0-9.5 2.2-12.6 6.1L150.9 380.2c-16.5 21-1.6 51.8 25.1 51.8h696c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"}}]},name:"swap",theme:"outlined"},Su=function(t,n){return a.createElement(ke,te({},t,{ref:n,icon:$u}))},Lr=a.forwardRef(Su),wu={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M637 443H519V309c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v134H325c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h118v134c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V519h118c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8zm284 424L775 721c122.1-148.9 113.6-369.5-26-509-148-148.1-388.4-148.1-537 0-148.1 148.6-148.1 389 0 537 139.5 139.6 360.1 148.1 509 26l146 146c3.2 2.8 8.3 2.8 11 0l43-43c2.8-2.7 2.8-7.8 0-11zM696 696c-118.8 118.7-311.2 118.7-430 0-118.7-118.8-118.7-311.2 0-430 118.8-118.7 311.2-118.7 430 0 118.7 118.8 118.7 311.2 0 430z"}}]},name:"zoom-in",theme:"outlined"},xu=function(t,n){return a.createElement(ke,te({},t,{ref:n,icon:wu}))},Iu=a.forwardRef(xu),Ou={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M637 443H325c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h312c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8zm284 424L775 721c122.1-148.9 113.6-369.5-26-509-148-148.1-388.4-148.1-537 0-148.1 148.6-148.1 389 0 537 139.5 139.6 360.1 148.1 509 26l146 146c3.2 2.8 8.3 2.8 11 0l43-43c2.8-2.7 2.8-7.8 0-11zM696 696c-118.8 118.7-311.2 118.7-430 0-118.7-118.8-118.7-311.2 0-430 118.8-118.7 311.2-118.7 430 0 118.7 118.8 118.7 311.2 0 430z"}}]},name:"zoom-out",theme:"outlined"},Eu=function(t,n){return a.createElement(ke,te({},t,{ref:n,icon:Ou}))},Pu=a.forwardRef(Eu);const so=e=>({position:e||"absolute",inset:0}),Nu=e=>{const{iconCls:t,motionDurationSlow:n,paddingXXS:o,marginXXS:r,prefixCls:i,colorTextLightSolid:l}=e;return{position:"absolute",inset:0,display:"flex",alignItems:"center",justifyContent:"center",color:l,background:new He("#000").setAlpha(.5).toRgbString(),cursor:"pointer",opacity:0,transition:`opacity ${n}`,[`.${i}-mask-info`]:Object.assign(Object.assign({},hn),{padding:`0 ${L(o)}`,[t]:{marginInlineEnd:r,svg:{verticalAlign:"baseline"}}})}},Ru=e=>{const{previewCls:t,modalMaskBg:n,paddingSM:o,marginXL:r,margin:i,paddingLG:l,previewOperationColorDisabled:s,previewOperationHoverColor:c,motionDurationSlow:d,iconCls:u,colorTextLightSolid:m}=e,f=new He(n).setAlpha(.1),p=f.clone().setAlpha(.2);return{[`${t}-footer`]:{position:"fixed",bottom:r,left:{_skip_check_:!0,value:"50%"},display:"flex",flexDirection:"column",alignItems:"center",color:e.previewOperationColor,transform:"translateX(-50%)"},[`${t}-progress`]:{marginBottom:i},[`${t}-close`]:{position:"fixed",top:r,right:{_skip_check_:!0,value:r},display:"flex",color:m,backgroundColor:f.toRgbString(),borderRadius:"50%",padding:o,outline:0,border:0,cursor:"pointer",transition:`all ${d}`,"&:hover":{backgroundColor:p.toRgbString()},[`& > ${u}`]:{fontSize:e.previewOperationSize}},[`${t}-operations`]:{display:"flex",alignItems:"center",padding:`0 ${L(l)}`,backgroundColor:f.toRgbString(),borderRadius:100,"&-operation":{marginInlineStart:o,padding:o,cursor:"pointer",transition:`all ${d}`,userSelect:"none",[`&:not(${t}-operations-operation-disabled):hover > ${u}`]:{color:c},"&-disabled":{color:s,cursor:"not-allowed"},"&:first-of-type":{marginInlineStart:0},[`& > ${u}`]:{fontSize:e.previewOperationSize}}}}},ju=e=>{const{modalMaskBg:t,iconCls:n,previewOperationColorDisabled:o,previewCls:r,zIndexPopup:i,motionDurationSlow:l}=e,s=new He(t).setAlpha(.1),c=s.clone().setAlpha(.2);return{[`${r}-switch-left, ${r}-switch-right`]:{position:"fixed",insetBlockStart:"50%",zIndex:e.calc(i).add(1).equal(),display:"flex",alignItems:"center",justifyContent:"center",width:e.imagePreviewSwitchSize,height:e.imagePreviewSwitchSize,marginTop:e.calc(e.imagePreviewSwitchSize).mul(-1).div(2).equal(),color:e.previewOperationColor,background:s.toRgbString(),borderRadius:"50%",transform:"translateY(-50%)",cursor:"pointer",transition:`all ${l}`,userSelect:"none","&:hover":{background:c.toRgbString()},"&-disabled":{"&, &:hover":{color:o,background:"transparent",cursor:"not-allowed",[`> ${n}`]:{cursor:"not-allowed"}}},[`> ${n}`]:{fontSize:e.previewOperationSize}},[`${r}-switch-left`]:{insetInlineStart:e.marginSM},[`${r}-switch-right`]:{insetInlineEnd:e.marginSM}}},Mu=e=>{const{motionEaseOut:t,previewCls:n,motionDurationSlow:o,componentCls:r}=e;return[{[`${r}-preview-root`]:{[n]:{height:"100%",textAlign:"center",pointerEvents:"none"},[`${n}-body`]:Object.assign(Object.assign({},so()),{overflow:"hidden"}),[`${n}-img`]:{maxWidth:"100%",maxHeight:"70%",verticalAlign:"middle",transform:"scale3d(1, 1, 1)",cursor:"grab",transition:`transform ${o} ${t} 0s`,userSelect:"none","&-wrapper":Object.assign(Object.assign({},so()),{transition:`transform ${o} ${t} 0s`,display:"flex",justifyContent:"center",alignItems:"center","& > *":{pointerEvents:"auto"},"&::before":{display:"inline-block",width:1,height:"50%",marginInlineEnd:-1,content:'""'}})},[`${n}-moving`]:{[`${n}-preview-img`]:{cursor:"grabbing","&-wrapper":{transitionDuration:"0s"}}}}},{[`${r}-preview-root`]:{[`${n}-wrap`]:{zIndex:e.zIndexPopup}}},{[`${r}-preview-operations-wrapper`]:{position:"fixed",zIndex:e.calc(e.zIndexPopup).add(1).equal()},"&":[Ru(e),ju(e)]}]},Tu=e=>{const{componentCls:t}=e;return{[t]:{position:"relative",display:"inline-block",[`${t}-img`]:{width:"100%",height:"auto",verticalAlign:"middle"},[`${t}-img-placeholder`]:{backgroundColor:e.colorBgContainerDisabled,backgroundImage:"url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMTQuNSAyLjVoLTEzQS41LjUgMCAwIDAgMSAzdjEwYS41LjUgMCAwIDAgLjUuNWgxM2EuNS41IDAgMCAwIC41LS41VjNhLjUuNSAwIDAgMC0uNS0uNXpNNS4yODEgNC43NWExIDEgMCAwIDEgMCAyIDEgMSAwIDAgMSAwLTJ6bTguMDMgNi44M2EuMTI3LjEyNyAwIDAgMS0uMDgxLjAzSDIuNzY5YS4xMjUuMTI1IDAgMCAxLS4wOTYtLjIwN2wyLjY2MS0zLjE1NmEuMTI2LjEyNiAwIDAgMSAuMTc3LS4wMTZsLjAxNi4wMTZMNy4wOCAxMC4wOWwyLjQ3LTIuOTNhLjEyNi4xMjYgMCAwIDEgLjE3Ny0uMDE2bC4wMTUuMDE2IDMuNTg4IDQuMjQ0YS4xMjcuMTI3IDAgMCAxLS4wMi4xNzV6IiBmaWxsPSIjOEM4QzhDIiBmaWxsLXJ1bGU9Im5vbnplcm8iLz48L3N2Zz4=')",backgroundRepeat:"no-repeat",backgroundPosition:"center center",backgroundSize:"30%"},[`${t}-mask`]:Object.assign({},Nu(e)),[`${t}-mask:hover`]:{opacity:1},[`${t}-placeholder`]:Object.assign({},so())}}},ku=e=>{const{previewCls:t}=e;return{[`${t}-root`]:Ht(e,"zoom"),"&":wo(e,!0)}},zu=e=>({zIndexPopup:e.zIndexPopupBase+80,previewOperationColor:new He(e.colorTextLightSolid).setAlpha(.65).toRgbString(),previewOperationHoverColor:new He(e.colorTextLightSolid).setAlpha(.85).toRgbString(),previewOperationColorDisabled:new He(e.colorTextLightSolid).setAlpha(.25).toRgbString(),previewOperationSize:e.fontSizeIcon*1.5}),fi=We("Image",e=>{const t=`${e.componentCls}-preview`,n=Fe(e,{previewCls:t,modalMaskBg:new He("#000").setAlpha(.45).toRgbString(),imagePreviewSwitchSize:e.controlHeightLG});return[Tu(n),Mu(n),ya(Fe(n,{componentCls:t})),ku(n)]},zu);var Bu=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n};const gi={rotateLeft:a.createElement(hu,null),rotateRight:a.createElement(yu,null),zoomIn:a.createElement(Iu,null),zoomOut:a.createElement(Pu,null),close:a.createElement(_t,null),left:a.createElement(io,null),right:a.createElement(sn,null),flipX:a.createElement(Lr,null),flipY:a.createElement(Lr,{rotate:90})},Lu=e=>{var{previewPrefixCls:t,preview:n}=e,o=Bu(e,["previewPrefixCls","preview"]);const{getPrefixCls:r}=a.useContext(ue),i=r("image",t),l=`${i}-preview`,s=r(),c=ut(i),[d,u,m]=fi(i,c),[f]=At("ImagePreview",typeof n=="object"?n.zIndex:void 0),p=a.useMemo(()=>{var g;if(n===!1)return n;const v=typeof n=="object"?n:{},$=M(u,m,c,(g=v.rootClassName)!==null&&g!==void 0?g:"");return Object.assign(Object.assign({},v),{transitionName:at(s,"zoom",v.transitionName),maskTransitionName:at(s,"fade",v.maskTransitionName),rootClassName:$,zIndex:f})},[n]);return d(a.createElement(Mo.PreviewGroup,Object.assign({preview:p,previewPrefixCls:l,icons:gi},o)))},Du=Lu;var Dr=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n};const pi=e=>{var t;const{prefixCls:n,preview:o,className:r,rootClassName:i,style:l}=e,s=Dr(e,["prefixCls","preview","className","rootClassName","style"]),{getPrefixCls:c,locale:d=rn,getPopupContainer:u,image:m}=a.useContext(ue),f=c("image",n),p=c(),g=d.Image||rn.Image,v=ut(f),[$,h,b]=fi(f,v),y=M(i,h,b,v),S=M(r,h,m==null?void 0:m.className),[C]=At("ImagePreview",typeof o=="object"?o.zIndex:void 0),w=a.useMemo(()=>{var O;if(o===!1)return o;const I=typeof o=="object"?o:{},{getContainer:P,closeIcon:j}=I,N=Dr(I,["getContainer","closeIcon"]);return Object.assign(Object.assign({mask:a.createElement("div",{className:`${f}-mask-info`},a.createElement(ma,null),g==null?void 0:g.preview),icons:gi},N),{getContainer:P??u,transitionName:at(p,"zoom",I.transitionName),maskTransitionName:at(p,"fade",I.maskTransitionName),zIndex:C,closeIcon:j??((O=m==null?void 0:m.preview)===null||O===void 0?void 0:O.closeIcon)})},[o,g,(t=m==null?void 0:m.preview)===null||t===void 0?void 0:t.closeIcon]),x=Object.assign(Object.assign({},m==null?void 0:m.style),l);return $(a.createElement(Mo,Object.assign({prefixCls:f,preview:w,rootClassName:y,className:S,style:x},s)))};pi.PreviewGroup=Du;function _u(e,t,n){return typeof n=="boolean"?n:e.length?!0:Ft(t).some(r=>r.type===ti)}const Au=e=>{const{componentCls:t,bodyBg:n,lightSiderBg:o,lightTriggerBg:r,lightTriggerColor:i}=e;return{[`${t}-sider-light`]:{background:o,[`${t}-sider-trigger`]:{color:i,background:r},[`${t}-sider-zero-width-trigger`]:{color:i,background:r,border:`1px solid ${n}`,borderInlineStart:0}}}},Hu=e=>{const{antCls:t,componentCls:n,colorText:o,triggerColor:r,footerBg:i,triggerBg:l,headerHeight:s,headerPadding:c,headerColor:d,footerPadding:u,triggerHeight:m,zeroTriggerHeight:f,zeroTriggerWidth:p,motionDurationMid:g,motionDurationSlow:v,fontSize:$,borderRadius:h,bodyBg:b,headerBg:y,siderBg:S}=e;return{[n]:Object.assign(Object.assign({display:"flex",flex:"auto",flexDirection:"column",minHeight:0,background:b,"&, *":{boxSizing:"border-box"},[`&${n}-has-sider`]:{flexDirection:"row",[`> ${n}, > ${n}-content`]:{width:0}},[`${n}-header, &${n}-footer`]:{flex:"0 0 auto"},[`${n}-sider`]:{position:"relative",minWidth:0,background:S,transition:`all ${g}, background 0s`,"&-children":{height:"100%",marginTop:-.1,paddingTop:.1,[`${t}-menu${t}-menu-inline-collapsed`]:{width:"auto"}},"&-has-trigger":{paddingBottom:m},"&-right":{order:1},"&-trigger":{position:"fixed",bottom:0,zIndex:1,height:m,color:r,lineHeight:L(m),textAlign:"center",background:l,cursor:"pointer",transition:`all ${g}`},"&-zero-width":{"> *":{overflow:"hidden"},"&-trigger":{position:"absolute",top:s,insetInlineEnd:e.calc(p).mul(-1).equal(),zIndex:1,width:p,height:f,color:r,fontSize:e.fontSizeXL,display:"flex",alignItems:"center",justifyContent:"center",background:S,borderStartStartRadius:0,borderStartEndRadius:h,borderEndEndRadius:h,borderEndStartRadius:0,cursor:"pointer",transition:`background ${v} ease`,"&::after":{position:"absolute",inset:0,background:"transparent",transition:`all ${v}`,content:'""'},"&:hover::after":{background:"rgba(255, 255, 255, 0.2)"},"&-right":{insetInlineStart:e.calc(p).mul(-1).equal(),borderStartStartRadius:h,borderStartEndRadius:0,borderEndEndRadius:0,borderEndStartRadius:h}}}}},Au(e)),{"&-rtl":{direction:"rtl"}}),[`${n}-header`]:{height:s,padding:c,color:d,lineHeight:L(s),background:y,[`${t}-menu`]:{lineHeight:"inherit"}},[`${n}-footer`]:{padding:u,color:o,fontSize:$,background:i},[`${n}-content`]:{flex:"auto",color:o,minHeight:0}}},Fu=e=>{const{colorBgLayout:t,controlHeight:n,controlHeightLG:o,colorText:r,controlHeightSM:i,marginXXS:l,colorTextLightSolid:s,colorBgContainer:c}=e,d=o*1.25;return{colorBgHeader:"#001529",colorBgBody:t,colorBgTrigger:"#002140",bodyBg:t,headerBg:"#001529",headerHeight:n*2,headerPadding:`0 ${d}px`,headerColor:r,footerPadding:`${i}px ${d}px`,footerBg:t,siderBg:"#001529",triggerHeight:o+l*2,triggerBg:"#002140",triggerColor:s,zeroTriggerWidth:o,zeroTriggerHeight:o,lightSiderBg:c,lightTriggerBg:c,lightTriggerColor:r}},vi=We("Layout",e=>[Hu(e)],Fu,{deprecatedTokens:[["colorBgBody","bodyBg"],["colorBgHeader","headerBg"],["colorBgTrigger","triggerBg"]]});var hi=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n};function Pn(e){let{suffixCls:t,tagName:n,displayName:o}=e;return r=>a.forwardRef((l,s)=>a.createElement(r,Object.assign({ref:s,suffixCls:t,tagName:n},l)))}const To=a.forwardRef((e,t)=>{const{prefixCls:n,suffixCls:o,className:r,tagName:i}=e,l=hi(e,["prefixCls","suffixCls","className","tagName"]),{getPrefixCls:s}=a.useContext(ue),c=s("layout",n),[d,u,m]=vi(c),f=o?`${c}-${o}`:c;return d(a.createElement(i,Object.assign({className:M(n||f,r,u,m),ref:t},l)))}),Wu=a.forwardRef((e,t)=>{const{direction:n}=a.useContext(ue),[o,r]=a.useState([]),{prefixCls:i,className:l,rootClassName:s,children:c,hasSider:d,tagName:u,style:m}=e,f=hi(e,["prefixCls","className","rootClassName","children","hasSider","tagName","style"]),p=qe(f,["suffixCls"]),{getPrefixCls:g,layout:v}=a.useContext(ue),$=g("layout",i),h=_u(o,c,d),[b,y,S]=vi($),C=M($,{[`${$}-has-sider`]:h,[`${$}-rtl`]:n==="rtl"},v==null?void 0:v.className,l,s,y,S),w=a.useMemo(()=>({siderHook:{addSider:x=>{r(O=>[].concat(ve(O),[x]))},removeSider:x=>{r(O=>O.filter(I=>I!==x))}}}),[]);return b(a.createElement(ei.Provider,{value:w},a.createElement(u,Object.assign({ref:t,className:C,style:Object.assign(Object.assign({},v==null?void 0:v.style),m)},p),c)))}),Vu=Pn({tagName:"div",displayName:"Layout"})(Wu),Xu=Pn({suffixCls:"header",tagName:"header",displayName:"Header"})(To),qu=Pn({suffixCls:"footer",tagName:"footer",displayName:"Footer"})(To),Uu=Pn({suffixCls:"content",tagName:"main",displayName:"Content"})(To),et=Vu;et.Header=Xu;et.Footer=qu;et.Content=Uu;et.Sider=ti;et._InternalSiderContext=On;var Ku=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n};const Gu=e=>{const{prefixCls:t,className:n,closeIcon:o,closable:r,type:i,title:l,children:s,footer:c}=e,d=Ku(e,["prefixCls","className","closeIcon","closable","type","title","children","footer"]),{getPrefixCls:u}=a.useContext(ue),m=u(),f=t||u("modal"),p=ut(m),[g,v,$]=wa(f,p),h=`${f}-confirm`;let b={};return i?b={closable:r??!1,title:"",footer:"",children:a.createElement(Ia,Object.assign({},e,{prefixCls:f,confirmPrefixCls:h,rootPrefixCls:m,content:s}))}:b={closable:r??!0,title:l,footer:c!==null&&a.createElement(Ca,Object.assign({},e)),children:s},g(a.createElement(va,Object.assign({prefixCls:f,className:M(v,`${f}-pure-panel`,i&&h,i&&`${h}-${i}`,n,$,p)},d,{closeIcon:ba(f,o),closable:r},b)))},Yu=Al(Gu);function bi(e){return Vt(Na(e))}const Ye=xa;Ye.useModal=Vs;Ye.info=function(t){return Vt(Ra(t))};Ye.success=function(t){return Vt(ja(t))};Ye.error=function(t){return Vt(Ma(t))};Ye.warning=bi;Ye.warn=bi;Ye.confirm=function(t){return Vt(Ta(t))};Ye.destroyAll=function(){for(;pt.length;){const t=pt.pop();t&&t()}};Ye.config=_s;Ye._InternalPanelDoNotUseOrYouWillBeFired=Yu;var Zu={percent:0,prefixCls:"rc-progress",strokeColor:"#2db7f5",strokeLinecap:"round",strokeWidth:1,trailColor:"#D9D9D9",trailWidth:1,gapPosition:"bottom"},Qu=function(){var t=a.useRef([]),n=a.useRef(null);return a.useEffect(function(){var o=Date.now(),r=!1;t.current.forEach(function(i){if(i){r=!0;var l=i.style;l.transitionDuration=".3s, .3s, .3s, .06s",n.current&&o-n.current<100&&(l.transitionDuration="0s, 0s")}}),r&&(n.current=Date.now())}),t.current},_r=0,Ju=Xr();function em(){var e;return Ju?(e=_r,_r+=1):e="TEST_OR_SSR",e}const tm=function(e){var t=a.useState(),n=ce(t,2),o=n[0],r=n[1];return a.useEffect(function(){r("rc_progress_".concat(em()))},[]),e||o};var Ar=function(t){var n=t.bg,o=t.children;return a.createElement("div",{style:{width:"100%",height:"100%",background:n}},o)};function Hr(e,t){return Object.keys(e).map(function(n){var o=parseFloat(n),r="".concat(Math.floor(o*t),"%");return"".concat(e[n]," ").concat(r)})}var nm=a.forwardRef(function(e,t){var n=e.prefixCls,o=e.color,r=e.gradientId,i=e.radius,l=e.style,s=e.ptg,c=e.strokeLinecap,d=e.strokeWidth,u=e.size,m=e.gapDegree,f=o&&Ke(o)==="object",p=f?"#FFF":void 0,g=u/2,v=a.createElement("circle",{className:"".concat(n,"-circle-path"),r:i,cx:g,cy:g,stroke:p,strokeLinecap:c,strokeWidth:d,opacity:s===0?0:1,style:l,ref:t});if(!f)return v;var $="".concat(r,"-conic"),h=m?"".concat(180+m/2,"deg"):"0deg",b=Hr(o,(360-m)/360),y=Hr(o,1),S="conic-gradient(from ".concat(h,", ").concat(b.join(", "),")"),C="linear-gradient(to ".concat(m?"bottom":"top",", ").concat(y.join(", "),")");return a.createElement(a.Fragment,null,a.createElement("mask",{id:$},v),a.createElement("foreignObject",{x:0,y:0,width:u,height:u,mask:"url(#".concat($,")")},a.createElement(Ar,{bg:C},a.createElement(Ar,{bg:S}))))}),kt=100,Hn=function(t,n,o,r,i,l,s,c,d,u){var m=arguments.length>10&&arguments[10]!==void 0?arguments[10]:0,f=o/100*360*((360-l)/360),p=l===0?0:{bottom:0,top:180,left:90,right:-90}[s],g=(100-r)/100*n;d==="round"&&r!==100&&(g+=u/2,g>=n&&(g=n-.01));var v=kt/2;return{stroke:typeof c=="string"?c:void 0,strokeDasharray:"".concat(n,"px ").concat(t),strokeDashoffset:g+m,transform:"rotate(".concat(i+f+p,"deg)"),transformOrigin:"".concat(v,"px ").concat(v,"px"),transition:"stroke-dashoffset .3s ease 0s, stroke-dasharray .3s ease 0s, stroke .3s, stroke-width .06s ease .3s, opacity .3s ease 0s",fillOpacity:0}},om=["id","prefixCls","steps","strokeWidth","trailWidth","gapDegree","gapPosition","trailColor","strokeLinecap","style","className","strokeColor","percent"];function Fr(e){var t=e??[];return Array.isArray(t)?t:[t]}var rm=function(t){var n=J(J({},Zu),t),o=n.id,r=n.prefixCls,i=n.steps,l=n.strokeWidth,s=n.trailWidth,c=n.gapDegree,d=c===void 0?0:c,u=n.gapPosition,m=n.trailColor,f=n.strokeLinecap,p=n.style,g=n.className,v=n.strokeColor,$=n.percent,h=Oe(n,om),b=kt/2,y=tm(o),S="".concat(y,"-gradient"),C=b-l/2,w=Math.PI*2*C,x=d>0?90+d/2:-90,O=w*((360-d)/360),I=Ke(i)==="object"?i:{count:i,gap:2},P=I.count,j=I.gap,N=Fr($),E=Fr(v),R=E.find(function(F){return F&&Ke(F)==="object"}),z=R&&Ke(R)==="object",D=z?"butt":f,T=Hn(w,O,0,100,x,d,u,m,D,l),K=Qu(),U=function(){var A=0;return N.map(function(_,B){var X=E[B]||E[E.length-1],Q=Hn(w,O,A,_,x,d,u,X,D,l);return A+=_,a.createElement(nm,{key:B,color:X,ptg:_,radius:C,prefixCls:r,gradientId:S,style:Q,strokeLinecap:D,strokeWidth:l,gapDegree:d,ref:function(H){K[B]=H},size:kt})}).reverse()},V=function(){var A=Math.round(P*(N[0]/100)),_=100/P,B=0;return new Array(P).fill(null).map(function(X,Q){var q=Q<=A-1?E[0]:m,H=q&&Ke(q)==="object"?"url(#".concat(S,")"):void 0,W=Hn(w,O,B,_,x,d,u,q,"butt",l,j);return B+=(O-W.strokeDashoffset+j)*100/O,a.createElement("circle",{key:Q,className:"".concat(r,"-circle-path"),r:C,cx:b,cy:b,stroke:H,strokeWidth:l,opacity:1,style:W,ref:function(le){K[Q]=le}})})};return a.createElement("svg",te({className:M("".concat(r,"-circle"),g),viewBox:"0 0 ".concat(kt," ").concat(kt),style:p,id:o,role:"presentation"},h),!P&&a.createElement("circle",{className:"".concat(r,"-circle-trail"),r:C,cx:b,cy:b,stroke:m,strokeLinecap:D,strokeWidth:s||l,style:T}),P?V():U())};function ct(e){return!e||e<0?0:e>100?100:e}function fn(e){let{success:t,successPercent:n}=e,o=n;return t&&"progress"in t&&(o=t.progress),t&&"percent"in t&&(o=t.percent),o}const am=e=>{let{percent:t,success:n,successPercent:o}=e;const r=ct(fn({success:n,successPercent:o}));return[r,ct(ct(t)-r)]},im=e=>{let{success:t={},strokeColor:n}=e;const{strokeColor:o}=t;return[o||Gn.green,n||null]},Nn=(e,t,n)=>{var o,r,i,l;let s=-1,c=-1;if(t==="step"){const d=n.steps,u=n.strokeWidth;typeof e=="string"||typeof e>"u"?(s=e==="small"?2:14,c=u??8):typeof e=="number"?[s,c]=[e,e]:[s=14,c=8]=Array.isArray(e)?e:[e.width,e.height],s*=d}else if(t==="line"){const d=n==null?void 0:n.strokeWidth;typeof e=="string"||typeof e>"u"?c=d||(e==="small"?6:8):typeof e=="number"?[s,c]=[e,e]:[s=-1,c=8]=Array.isArray(e)?e:[e.width,e.height]}else(t==="circle"||t==="dashboard")&&(typeof e=="string"||typeof e>"u"?[s,c]=e==="small"?[60,60]:[120,120]:typeof e=="number"?[s,c]=[e,e]:Array.isArray(e)&&(s=(r=(o=e[0])!==null&&o!==void 0?o:e[1])!==null&&r!==void 0?r:120,c=(l=(i=e[0])!==null&&i!==void 0?i:e[1])!==null&&l!==void 0?l:120));return[s,c]},lm=3,sm=e=>lm/e*100,cm=e=>{const{prefixCls:t,trailColor:n=null,strokeLinecap:o="round",gapPosition:r,gapDegree:i,width:l=120,type:s,children:c,success:d,size:u=l,steps:m}=e,[f,p]=Nn(u,"circle");let{strokeWidth:g}=e;g===void 0&&(g=Math.max(sm(f),6));const v={width:f,height:p,fontSize:f*.15+6},$=a.useMemo(()=>{if(i||i===0)return i;if(s==="dashboard")return 75},[i,s]),h=am(e),b=r||s==="dashboard"&&"bottom"||void 0,y=Object.prototype.toString.call(e.strokeColor)==="[object Object]",S=im({success:d,strokeColor:e.strokeColor}),C=M(`${t}-inner`,{[`${t}-circle-gradient`]:y}),w=a.createElement(rm,{steps:m,percent:m?h[1]:h,strokeWidth:g,trailWidth:g,strokeColor:m?S[1]:S,strokeLinecap:o,trailColor:n,prefixCls:t,gapDegree:$,gapPosition:b}),x=f<=20,O=a.createElement("div",{className:C,style:v},w,!x&&c);return x?a.createElement(yn,{title:c},O):O},gn="--progress-line-stroke-color",Ci="--progress-percent",Wr=e=>{const t=e?"100%":"-100%";return new Xe(`antProgress${e?"RTL":"LTR"}Active`,{"0%":{transform:`translateX(${t}) scaleX(0)`,opacity:.1},"20%":{transform:`translateX(${t}) scaleX(0)`,opacity:.5},to:{transform:"translateX(0) scaleX(1)",opacity:0}})},dm=e=>{const{componentCls:t,iconCls:n}=e;return{[t]:Object.assign(Object.assign({},tt(e)),{display:"inline-block","&-rtl":{direction:"rtl"},"&-line":{position:"relative",width:"100%",fontSize:e.fontSize},[`${t}-outer`]:{display:"inline-flex",alignItems:"center",width:"100%"},[`${t}-inner`]:{position:"relative",display:"inline-block",width:"100%",flex:1,overflow:"hidden",verticalAlign:"middle",backgroundColor:e.remainingColor,borderRadius:e.lineBorderRadius},[`${t}-inner:not(${t}-circle-gradient)`]:{[`${t}-circle-path`]:{stroke:e.defaultColor}},[`${t}-success-bg, ${t}-bg`]:{position:"relative",background:e.defaultColor,borderRadius:e.lineBorderRadius,transition:`all ${e.motionDurationSlow} ${e.motionEaseInOutCirc}`},[`${t}-layout-bottom`]:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",[`${t}-text`]:{width:"max-content",marginInlineStart:0,marginTop:e.marginXXS}},[`${t}-bg`]:{overflow:"hidden","&::after":{content:'""',background:{_multi_value_:!0,value:["inherit",`var(${gn})`]},height:"100%",width:`calc(1 / var(${Ci}) * 100%)`,display:"block"},[`&${t}-bg-inner`]:{minWidth:"max-content","&::after":{content:"none"},[`${t}-text-inner`]:{color:e.colorWhite,[`&${t}-text-bright`]:{color:"rgba(0, 0, 0, 0.45)"}}}},[`${t}-success-bg`]:{position:"absolute",insetBlockStart:0,insetInlineStart:0,backgroundColor:e.colorSuccess},[`${t}-text`]:{display:"inline-block",marginInlineStart:e.marginXS,color:e.colorText,lineHeight:1,width:"2em",whiteSpace:"nowrap",textAlign:"start",verticalAlign:"middle",wordBreak:"normal",[n]:{fontSize:e.fontSize},[`&${t}-text-outer`]:{width:"max-content"},[`&${t}-text-outer${t}-text-start`]:{width:"max-content",marginInlineStart:0,marginInlineEnd:e.marginXS}},[`${t}-text-inner`]:{display:"flex",justifyContent:"center",alignItems:"center",width:"100%",height:"100%",marginInlineStart:0,padding:`0 ${L(e.paddingXXS)}`,[`&${t}-text-start`]:{justifyContent:"start"},[`&${t}-text-end`]:{justifyContent:"end"}},[`&${t}-status-active`]:{[`${t}-bg::before`]:{position:"absolute",inset:0,backgroundColor:e.colorBgContainer,borderRadius:e.lineBorderRadius,opacity:0,animationName:Wr(),animationDuration:e.progressActiveMotionDuration,animationTimingFunction:e.motionEaseOutQuint,animationIterationCount:"infinite",content:'""'}},[`&${t}-rtl${t}-status-active`]:{[`${t}-bg::before`]:{animationName:Wr(!0)}},[`&${t}-status-exception`]:{[`${t}-bg`]:{backgroundColor:e.colorError},[`${t}-text`]:{color:e.colorError}},[`&${t}-status-exception ${t}-inner:not(${t}-circle-gradient)`]:{[`${t}-circle-path`]:{stroke:e.colorError}},[`&${t}-status-success`]:{[`${t}-bg`]:{backgroundColor:e.colorSuccess},[`${t}-text`]:{color:e.colorSuccess}},[`&${t}-status-success ${t}-inner:not(${t}-circle-gradient)`]:{[`${t}-circle-path`]:{stroke:e.colorSuccess}}})}},um=e=>{const{componentCls:t,iconCls:n}=e;return{[t]:{[`${t}-circle-trail`]:{stroke:e.remainingColor},[`&${t}-circle ${t}-inner`]:{position:"relative",lineHeight:1,backgroundColor:"transparent"},[`&${t}-circle ${t}-text`]:{position:"absolute",insetBlockStart:"50%",insetInlineStart:0,width:"100%",margin:0,padding:0,color:e.circleTextColor,fontSize:e.circleTextFontSize,lineHeight:1,whiteSpace:"normal",textAlign:"center",transform:"translateY(-50%)",[n]:{fontSize:e.circleIconFontSize}},[`${t}-circle&-status-exception`]:{[`${t}-text`]:{color:e.colorError}},[`${t}-circle&-status-success`]:{[`${t}-text`]:{color:e.colorSuccess}}},[`${t}-inline-circle`]:{lineHeight:1,[`${t}-inner`]:{verticalAlign:"bottom"}}}},mm=e=>{const{componentCls:t}=e;return{[t]:{[`${t}-steps`]:{display:"inline-block","&-outer":{display:"flex",flexDirection:"row",alignItems:"center"},"&-item":{flexShrink:0,minWidth:e.progressStepMinWidth,marginInlineEnd:e.progressStepMarginInlineEnd,backgroundColor:e.remainingColor,transition:`all ${e.motionDurationSlow}`,"&-active":{backgroundColor:e.defaultColor}}}}}},fm=e=>{const{componentCls:t,iconCls:n}=e;return{[t]:{[`${t}-small&-line, ${t}-small&-line ${t}-text ${n}`]:{fontSize:e.fontSizeSM}}}},gm=e=>({circleTextColor:e.colorText,defaultColor:e.colorInfo,remainingColor:e.colorFillSecondary,lineBorderRadius:100,circleTextFontSize:"1em",circleIconFontSize:`${e.fontSize/e.fontSizeSM}em`}),pm=We("Progress",e=>{const t=e.calc(e.marginXXS).div(2).equal(),n=Fe(e,{progressStepMarginInlineEnd:t,progressStepMinWidth:t,progressActiveMotionDuration:"2.4s"});return[dm(n),um(n),mm(n),fm(n)]},gm);var vm=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n};const hm=e=>{let t=[];return Object.keys(e).forEach(n=>{const o=parseFloat(n.replace(/%/g,""));isNaN(o)||t.push({key:o,value:e[n]})}),t=t.sort((n,o)=>n.key-o.key),t.map(n=>{let{key:o,value:r}=n;return`${r} ${o}%`}).join(", ")},bm=(e,t)=>{const{from:n=Gn.blue,to:o=Gn.blue,direction:r=t==="rtl"?"to left":"to right"}=e,i=vm(e,["from","to","direction"]);if(Object.keys(i).length!==0){const s=hm(i),c=`linear-gradient(${r}, ${s})`;return{background:c,[gn]:c}}const l=`linear-gradient(${r}, ${n}, ${o})`;return{background:l,[gn]:l}},Cm=e=>{const{prefixCls:t,direction:n,percent:o,size:r,strokeWidth:i,strokeColor:l,strokeLinecap:s="round",children:c,trailColor:d=null,percentPosition:u,success:m}=e,{align:f,type:p}=u,g=l&&typeof l!="string"?bm(l,n):{[gn]:l,background:l},v=s==="square"||s==="butt"?0:void 0,$=r??[-1,i||(r==="small"?6:8)],[h,b]=Nn($,"line",{strokeWidth:i}),y={backgroundColor:d||void 0,borderRadius:v},S=Object.assign(Object.assign({width:`${ct(o)}%`,height:b,borderRadius:v},g),{[Ci]:ct(o)/100}),C=fn(e),w={width:`${ct(C)}%`,height:b,borderRadius:v,backgroundColor:m==null?void 0:m.strokeColor},x={width:h<0?"100%":h},O=a.createElement("div",{className:`${t}-inner`,style:y},a.createElement("div",{className:M(`${t}-bg`,`${t}-bg-${p}`),style:S},p==="inner"&&c),C!==void 0&&a.createElement("div",{className:`${t}-success-bg`,style:w})),I=p==="outer"&&f==="start",P=p==="outer"&&f==="end";return p==="outer"&&f==="center"?a.createElement("div",{className:`${t}-layout-bottom`},O,c):a.createElement("div",{className:`${t}-outer`,style:x},I&&c,O,P&&c)},ym=e=>{const{size:t,steps:n,percent:o=0,strokeWidth:r=8,strokeColor:i,trailColor:l=null,prefixCls:s,children:c}=e,d=Math.round(n*(o/100)),m=t??[t==="small"?2:14,r],[f,p]=Nn(m,"step",{steps:n,strokeWidth:r}),g=f/n,v=new Array(n);for(let $=0;$<n;$++){const h=Array.isArray(i)?i[$]:i;v[$]=a.createElement("div",{key:$,className:M(`${s}-steps-item`,{[`${s}-steps-item-active`]:$<=d-1}),style:{backgroundColor:$<=d-1?h:l,width:g,height:p}})}return a.createElement("div",{className:`${s}-steps-outer`},v,c)};var $m=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n};const Sm=["normal","exception","active","success"],wm=a.forwardRef((e,t)=>{const{prefixCls:n,className:o,rootClassName:r,steps:i,strokeColor:l,percent:s=0,size:c="default",showInfo:d=!0,type:u="line",status:m,format:f,style:p,percentPosition:g={}}=e,v=$m(e,["prefixCls","className","rootClassName","steps","strokeColor","percent","size","showInfo","type","status","format","style","percentPosition"]),{align:$="end",type:h="outer"}=g,b=Array.isArray(l)?l[0]:l,y=typeof l=="string"||Array.isArray(l)?l:void 0,S=a.useMemo(()=>{if(b){const U=typeof b=="string"?b:Object.values(b)[0];return new He(U).isLight()}return!1},[l]),C=a.useMemo(()=>{var U,V;const F=fn(e);return parseInt(F!==void 0?(U=F??0)===null||U===void 0?void 0:U.toString():(V=s??0)===null||V===void 0?void 0:V.toString(),10)},[s,e.success,e.successPercent]),w=a.useMemo(()=>!Sm.includes(m)&&C>=100?"success":m||"normal",[m,C]),{getPrefixCls:x,direction:O,progress:I}=a.useContext(ue),P=x("progress",n),[j,N,E]=pm(P),R=u==="line",z=R&&!i,D=a.useMemo(()=>{if(!d)return null;const U=fn(e);let V;const F=f||(_=>`${_}%`),A=R&&S&&h==="inner";return h==="inner"||f||w!=="exception"&&w!=="success"?V=F(ct(s),ct(U)):w==="exception"?V=R?a.createElement(Gr,null):a.createElement(_t,null):w==="success"&&(V=R?a.createElement(Yr,null):a.createElement(Hl,null)),a.createElement("span",{className:M(`${P}-text`,{[`${P}-text-bright`]:A,[`${P}-text-${$}`]:z,[`${P}-text-${h}`]:z}),title:typeof V=="string"?V:void 0},V)},[d,s,C,w,u,P,f]);let T;u==="line"?T=i?a.createElement(ym,Object.assign({},e,{strokeColor:y,prefixCls:P,steps:typeof i=="object"?i.count:i}),D):a.createElement(Cm,Object.assign({},e,{strokeColor:b,prefixCls:P,direction:O,percentPosition:{align:$,type:h}}),D):(u==="circle"||u==="dashboard")&&(T=a.createElement(cm,Object.assign({},e,{strokeColor:b,prefixCls:P,progressStatus:w}),D));const K=M(P,`${P}-status-${w}`,{[`${P}-${u==="dashboard"&&"circle"||u}`]:u!=="line",[`${P}-inline-circle`]:u==="circle"&&Nn(c,"circle")[0]<=20,[`${P}-line`]:z,[`${P}-line-align-${$}`]:z,[`${P}-line-position-${h}`]:z,[`${P}-steps`]:i,[`${P}-show-info`]:d,[`${P}-${c}`]:typeof c=="string",[`${P}-rtl`]:O==="rtl"},I==null?void 0:I.className,o,r,N,E);return j(a.createElement("div",Object.assign({ref:t,style:Object.assign(Object.assign({},I==null?void 0:I.style),p),className:K,role:"progressbar","aria-valuenow":C,"aria-valuemin":0,"aria-valuemax":100},qe(v,["trailColor","strokeWidth","width","gapDegree","gapPosition","strokeLinecap","success","successPercent"])),T))});var xm={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M360 184h-8c4.4 0 8-3.6 8-8v8h304v-8c0 4.4 3.6 8 8 8h-8v72h72v-80c0-35.3-28.7-64-64-64H352c-35.3 0-64 28.7-64 64v80h72v-72zm504 72H160c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.7 523c1.6 34.1 29.8 61 63.9 61h454c34.2 0 62.3-26.8 63.9-61l24.7-523H888c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM731.3 840H292.7l-24.2-512h487l-24.2 512z"}}]},name:"delete",theme:"outlined"},Im=function(t,n){return a.createElement(ke,te({},t,{ref:n,icon:xm}))},Om=a.forwardRef(Im),Em={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M505.7 661a8 8 0 0012.6 0l112-141.7c4.1-5.2.4-12.9-6.3-12.9h-74.1V168c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v338.3H400c-6.7 0-10.4 7.7-6.3 12.9l112 141.8zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z"}}]},name:"download",theme:"outlined"},Pm=function(t,n){return a.createElement(ke,te({},t,{ref:n,icon:Em}))},Nm=a.forwardRef(Pm),Rm={icon:function(t,n){return{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M534 352V136H232v752h560V394H576a42 42 0 01-42-42z",fill:n}},{tag:"path",attrs:{d:"M854.6 288.6L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM602 137.8L790.2 326H602V137.8zM792 888H232V136h302v216a42 42 0 0042 42h216v494z",fill:t}}]}},name:"file",theme:"twotone"},jm=function(t,n){return a.createElement(ke,te({},t,{ref:n,icon:Rm}))},Mm=a.forwardRef(jm),Tm={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M779.3 196.6c-94.2-94.2-247.6-94.2-341.7 0l-261 260.8c-1.7 1.7-2.6 4-2.6 6.4s.9 4.7 2.6 6.4l36.9 36.9a9 9 0 0012.7 0l261-260.8c32.4-32.4 75.5-50.2 121.3-50.2s88.9 17.8 121.2 50.2c32.4 32.4 50.2 75.5 50.2 121.2 0 45.8-17.8 88.8-50.2 121.2l-266 265.9-43.1 43.1c-40.3 40.3-105.8 40.3-146.1 0-19.5-19.5-30.2-45.4-30.2-73s10.7-53.5 30.2-73l263.9-263.8c6.7-6.6 15.5-10.3 24.9-10.3h.1c9.4 0 18.1 3.7 24.7 10.3 6.7 6.7 10.3 15.5 10.3 24.9 0 9.3-3.7 18.1-10.3 24.7L372.4 653c-1.7 1.7-2.6 4-2.6 6.4s.9 4.7 2.6 6.4l36.9 36.9a9 9 0 0012.7 0l215.6-215.6c19.9-19.9 30.8-46.3 30.8-74.4s-11-54.6-30.8-74.4c-41.1-41.1-107.9-41-149 0L463 364 224.8 602.1A172.22 172.22 0 00174 724.8c0 46.3 18.1 89.8 50.8 122.5 33.9 33.8 78.3 50.7 122.7 50.7 44.4 0 88.8-16.9 122.6-50.7l309.2-309C824.8 492.7 850 432 850 367.5c.1-64.6-25.1-125.3-70.7-170.9z"}}]},name:"paper-clip",theme:"outlined"},km=function(t,n){return a.createElement(ke,te({},t,{ref:n,icon:Tm}))},zm=a.forwardRef(km),Bm={icon:function(t,n){return{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-40 632H136v-39.9l138.5-164.3 150.1 178L658.1 489 888 761.6V792zm0-129.8L664.2 396.8c-3.2-3.8-9-3.8-12.2 0L424.6 666.4l-144-170.7c-3.2-3.8-9-3.8-12.2 0L136 652.7V232h752v430.2z",fill:t}},{tag:"path",attrs:{d:"M424.6 765.8l-150.1-178L136 752.1V792h752v-30.4L658.1 489z",fill:n}},{tag:"path",attrs:{d:"M136 652.7l132.4-157c3.2-3.8 9-3.8 12.2 0l144 170.7L652 396.8c3.2-3.8 9-3.8 12.2 0L888 662.2V232H136v420.7zM304 280a88 88 0 110 176 88 88 0 010-176z",fill:n}},{tag:"path",attrs:{d:"M276 368a28 28 0 1056 0 28 28 0 10-56 0z",fill:n}},{tag:"path",attrs:{d:"M304 456a88 88 0 100-176 88 88 0 000 176zm0-116c15.5 0 28 12.5 28 28s-12.5 28-28 28-28-12.5-28-28 12.5-28 28-28z",fill:t}}]}},name:"picture",theme:"twotone"},Lm=function(t,n){return a.createElement(ke,te({},t,{ref:n,icon:Bm}))},Dm=a.forwardRef(Lm),_m=["prefixCls","className","checked","defaultChecked","disabled","loadingIcon","checkedChildren","unCheckedChildren","onClick","onChange","onKeyDown"],yi=a.forwardRef(function(e,t){var n,o=e.prefixCls,r=o===void 0?"rc-switch":o,i=e.className,l=e.checked,s=e.defaultChecked,c=e.disabled,d=e.loadingIcon,u=e.checkedChildren,m=e.unCheckedChildren,f=e.onClick,p=e.onChange,g=e.onKeyDown,v=Oe(e,_m),$=Ve(!1,{value:l,defaultValue:s}),h=ce($,2),b=h[0],y=h[1];function S(O,I){var P=b;return c||(P=O,y(P),p==null||p(P,I)),P}function C(O){O.which===Ee.LEFT?S(!1,O):O.which===Ee.RIGHT&&S(!0,O),g==null||g(O)}function w(O){var I=S(!b,O);f==null||f(I,O)}var x=M(r,i,(n={},ee(n,"".concat(r,"-checked"),b),ee(n,"".concat(r,"-disabled"),c),n));return a.createElement("button",te({},v,{type:"button",role:"switch","aria-checked":b,disabled:c,className:x,ref:t,onKeyDown:C,onClick:w}),d,a.createElement("span",{className:"".concat(r,"-inner")},a.createElement("span",{className:"".concat(r,"-inner-checked")},u),a.createElement("span",{className:"".concat(r,"-inner-unchecked")},m)))});yi.displayName="Switch";const Am=e=>{const{componentCls:t,trackHeightSM:n,trackPadding:o,trackMinWidthSM:r,innerMinMarginSM:i,innerMaxMarginSM:l,handleSizeSM:s,calc:c}=e,d=`${t}-inner`,u=L(c(s).add(c(o).mul(2)).equal()),m=L(c(l).mul(2).equal());return{[t]:{[`&${t}-small`]:{minWidth:r,height:n,lineHeight:L(n),[`${t}-inner`]:{paddingInlineStart:l,paddingInlineEnd:i,[`${d}-checked, ${d}-unchecked`]:{minHeight:n},[`${d}-checked`]:{marginInlineStart:`calc(-100% + ${u} - ${m})`,marginInlineEnd:`calc(100% - ${u} + ${m})`},[`${d}-unchecked`]:{marginTop:c(n).mul(-1).equal(),marginInlineStart:0,marginInlineEnd:0}},[`${t}-handle`]:{width:s,height:s},[`${t}-loading-icon`]:{top:c(c(s).sub(e.switchLoadingIconSize)).div(2).equal(),fontSize:e.switchLoadingIconSize},[`&${t}-checked`]:{[`${t}-inner`]:{paddingInlineStart:i,paddingInlineEnd:l,[`${d}-checked`]:{marginInlineStart:0,marginInlineEnd:0},[`${d}-unchecked`]:{marginInlineStart:`calc(100% - ${u} + ${m})`,marginInlineEnd:`calc(-100% + ${u} - ${m})`}},[`${t}-handle`]:{insetInlineStart:`calc(100% - ${L(c(s).add(o).equal())})`}},[`&:not(${t}-disabled):active`]:{[`&:not(${t}-checked) ${d}`]:{[`${d}-unchecked`]:{marginInlineStart:c(e.marginXXS).div(2).equal(),marginInlineEnd:c(e.marginXXS).mul(-1).div(2).equal()}},[`&${t}-checked ${d}`]:{[`${d}-checked`]:{marginInlineStart:c(e.marginXXS).mul(-1).div(2).equal(),marginInlineEnd:c(e.marginXXS).div(2).equal()}}}}}}},Hm=e=>{const{componentCls:t,handleSize:n,calc:o}=e;return{[t]:{[`${t}-loading-icon${e.iconCls}`]:{position:"relative",top:o(o(n).sub(e.fontSize)).div(2).equal(),color:e.switchLoadingIconColor,verticalAlign:"top"},[`&${t}-checked ${t}-loading-icon`]:{color:e.switchColor}}}},Fm=e=>{const{componentCls:t,trackPadding:n,handleBg:o,handleShadow:r,handleSize:i,calc:l}=e,s=`${t}-handle`;return{[t]:{[s]:{position:"absolute",top:n,insetInlineStart:n,width:i,height:i,transition:`all ${e.switchDuration} ease-in-out`,"&::before":{position:"absolute",top:0,insetInlineEnd:0,bottom:0,insetInlineStart:0,backgroundColor:o,borderRadius:l(i).div(2).equal(),boxShadow:r,transition:`all ${e.switchDuration} ease-in-out`,content:'""'}},[`&${t}-checked ${s}`]:{insetInlineStart:`calc(100% - ${L(l(i).add(n).equal())})`},[`&:not(${t}-disabled):active`]:{[`${s}::before`]:{insetInlineEnd:e.switchHandleActiveInset,insetInlineStart:0},[`&${t}-checked ${s}::before`]:{insetInlineEnd:0,insetInlineStart:e.switchHandleActiveInset}}}}},Wm=e=>{const{componentCls:t,trackHeight:n,trackPadding:o,innerMinMargin:r,innerMaxMargin:i,handleSize:l,calc:s}=e,c=`${t}-inner`,d=L(s(l).add(s(o).mul(2)).equal()),u=L(s(i).mul(2).equal());return{[t]:{[c]:{display:"block",overflow:"hidden",borderRadius:100,height:"100%",paddingInlineStart:i,paddingInlineEnd:r,transition:`padding-inline-start ${e.switchDuration} ease-in-out, padding-inline-end ${e.switchDuration} ease-in-out`,[`${c}-checked, ${c}-unchecked`]:{display:"block",color:e.colorTextLightSolid,fontSize:e.fontSizeSM,transition:`margin-inline-start ${e.switchDuration} ease-in-out, margin-inline-end ${e.switchDuration} ease-in-out`,pointerEvents:"none",minHeight:n},[`${c}-checked`]:{marginInlineStart:`calc(-100% + ${d} - ${u})`,marginInlineEnd:`calc(100% - ${d} + ${u})`},[`${c}-unchecked`]:{marginTop:s(n).mul(-1).equal(),marginInlineStart:0,marginInlineEnd:0}},[`&${t}-checked ${c}`]:{paddingInlineStart:r,paddingInlineEnd:i,[`${c}-checked`]:{marginInlineStart:0,marginInlineEnd:0},[`${c}-unchecked`]:{marginInlineStart:`calc(100% - ${d} + ${u})`,marginInlineEnd:`calc(-100% + ${d} - ${u})`}},[`&:not(${t}-disabled):active`]:{[`&:not(${t}-checked) ${c}`]:{[`${c}-unchecked`]:{marginInlineStart:s(o).mul(2).equal(),marginInlineEnd:s(o).mul(-1).mul(2).equal()}},[`&${t}-checked ${c}`]:{[`${c}-checked`]:{marginInlineStart:s(o).mul(-1).mul(2).equal(),marginInlineEnd:s(o).mul(2).equal()}}}}}},Vm=e=>{const{componentCls:t,trackHeight:n,trackMinWidth:o}=e;return{[t]:Object.assign(Object.assign(Object.assign(Object.assign({},tt(e)),{position:"relative",display:"inline-block",boxSizing:"border-box",minWidth:o,height:n,lineHeight:L(n),verticalAlign:"middle",background:e.colorTextQuaternary,border:"0",borderRadius:100,cursor:"pointer",transition:`all ${e.motionDurationMid}`,userSelect:"none",[`&:hover:not(${t}-disabled)`]:{background:e.colorTextTertiary}}),on(e)),{[`&${t}-checked`]:{background:e.switchColor,[`&:hover:not(${t}-disabled)`]:{background:e.colorPrimaryHover}},[`&${t}-loading, &${t}-disabled`]:{cursor:"not-allowed",opacity:e.switchDisabledOpacity,"*":{boxShadow:"none",cursor:"not-allowed"}},[`&${t}-rtl`]:{direction:"rtl"}})}},Xm=e=>{const{fontSize:t,lineHeight:n,controlHeight:o,colorWhite:r}=e,i=t*n,l=o/2,s=2,c=i-s*2,d=l-s*2;return{trackHeight:i,trackHeightSM:l,trackMinWidth:c*2+s*4,trackMinWidthSM:d*2+s*2,trackPadding:s,handleBg:r,handleSize:c,handleSizeSM:d,handleShadow:`0 2px 4px 0 ${new He("#00230b").setAlpha(.2).toRgbString()}`,innerMinMargin:c/2,innerMaxMargin:c+s+s*2,innerMinMarginSM:d/2,innerMaxMarginSM:d+s+s*2}},qm=We("Switch",e=>{const t=Fe(e,{switchDuration:e.motionDurationMid,switchColor:e.colorPrimary,switchDisabledOpacity:e.opacityLoading,switchLoadingIconSize:e.calc(e.fontSizeIcon).mul(.75).equal(),switchLoadingIconColor:`rgba(0, 0, 0, ${e.opacityLoading})`,switchHandleActiveInset:"-30%"});return[Vm(t),Wm(t),Fm(t),Hm(t),Am(t)]},Xm);var Um=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n};const Km=a.forwardRef((e,t)=>{const{prefixCls:n,size:o,disabled:r,loading:i,className:l,rootClassName:s,style:c,checked:d,value:u,defaultChecked:m,defaultValue:f,onChange:p}=e,g=Um(e,["prefixCls","size","disabled","loading","className","rootClassName","style","checked","value","defaultChecked","defaultValue","onChange"]),[v,$]=Ve(!1,{value:d??u,defaultValue:m??f}),{getPrefixCls:h,direction:b,switch:y}=a.useContext(ue),S=a.useContext(Qr),C=(r??S)||i,w=h("switch",n),x=a.createElement("div",{className:`${w}-handle`},i&&a.createElement(Yn,{className:`${w}-loading-icon`})),[O,I,P]=qm(w),j=oa(o),N=M(y==null?void 0:y.className,{[`${w}-small`]:j==="small",[`${w}-loading`]:i,[`${w}-rtl`]:b==="rtl"},l,s,I,P),E=Object.assign(Object.assign({},y==null?void 0:y.style),c),R=function(){$(arguments.length<=0?void 0:arguments[0]),p==null||p.apply(void 0,arguments)};return O(a.createElement(Fl,{component:"Switch"},a.createElement(yi,Object.assign({},g,{checked:v,onChange:R,prefixCls:w,className:N,style:E,disabled:C,ref:t,loadingIcon:x}))))}),$i=Km;$i.__ANT_SWITCH=!0;const Gm=e=>{const t=e!=null&&e.algorithm?Zo(e.algorithm):Zo(bn),n=Object.assign(Object.assign({},ml),e==null?void 0:e.token);return fl(n,{override:e==null?void 0:e.token},t,gl)};function Ym(e){const{sizeUnit:t,sizeStep:n}=e,o=n-2;return{sizeXXL:t*(o+10),sizeXL:t*(o+6),sizeLG:t*(o+2),sizeMD:t*(o+2),sizeMS:t*(o+1),size:t*o,sizeSM:t*o,sizeXS:t*(o-1),sizeXXS:t*(o-1)}}const Zm=(e,t)=>{const n=t??bn(e),o=n.fontSizeSM,r=n.controlHeight-4;return Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},n),Ym(t??e)),pl(o)),{controlHeight:r}),vl(Object.assign(Object.assign({},n),{controlHeight:r})))},rt=(e,t)=>new He(e).setAlpha(t).toRgbString(),$t=(e,t)=>new He(e).lighten(t).toHexString(),Qm=e=>{const t=Jr(e,{theme:"dark"});return{1:t[0],2:t[1],3:t[2],4:t[3],5:t[6],6:t[5],7:t[4],8:t[6],9:t[5],10:t[4]}},Jm=(e,t)=>{const n=e||"#000",o=t||"#fff";return{colorBgBase:n,colorTextBase:o,colorText:rt(o,.85),colorTextSecondary:rt(o,.65),colorTextTertiary:rt(o,.45),colorTextQuaternary:rt(o,.25),colorFill:rt(o,.18),colorFillSecondary:rt(o,.12),colorFillTertiary:rt(o,.08),colorFillQuaternary:rt(o,.04),colorBgElevated:$t(n,12),colorBgContainer:$t(n,8),colorBgLayout:$t(n,0),colorBgSpotlight:$t(n,26),colorBgBlur:rt(o,.04),colorBorder:$t(n,26),colorBorderSecondary:$t(n,19)}},ef=(e,t)=>{const n=Object.keys(hl).map(r=>{const i=Jr(e[r],{theme:"dark"});return new Array(10).fill(1).reduce((l,s,c)=>(l[`${r}-${c+1}`]=i[c],l[`${r}${c+1}`]=i[c],l),{})}).reduce((r,i)=>(r=Object.assign(Object.assign({},r),i),r),{}),o=t??bn(e);return Object.assign(Object.assign(Object.assign({},o),n),bl(e,{generateColorPalettes:Qm,generateNeutralColorPalettes:Jm}))};function tf(){const[e,t,n]=fo();return{theme:e,token:t,hashId:n}}const Si={defaultConfig:Qo,defaultSeed:Qo.token,useToken:tf,defaultAlgorithm:bn,darkAlgorithm:ef,compactAlgorithm:Zm,getDesignToken:Gm},Fn=function(e,t){if(e&&t){var n=Array.isArray(t)?t:t.split(","),o=e.name||"",r=e.type||"",i=r.replace(/\/.*$/,"");return n.some(function(l){var s=l.trim();if(/^\*(\/\*)?$/.test(l))return!0;if(s.charAt(0)==="."){var c=o.toLowerCase(),d=s.toLowerCase(),u=[d];return(d===".jpg"||d===".jpeg")&&(u=[".jpg",".jpeg"]),u.some(function(m){return c.endsWith(m)})}return/\/\*$/.test(s)?i===s.replace(/\/.*$/,""):r===s?!0:/^\w+$/.test(s)?(vo(!1,"Upload takes an invalidate 'accept' type '".concat(s,"'.Skip for check.")),!0):!1})}return!0};function nf(e,t){var n="cannot ".concat(e.method," ").concat(e.action," ").concat(t.status,"'"),o=new Error(n);return o.status=t.status,o.method=e.method,o.url=e.action,o}function Vr(e){var t=e.responseText||e.response;if(!t)return t;try{return JSON.parse(t)}catch{return t}}function of(e){var t=new XMLHttpRequest;e.onProgress&&t.upload&&(t.upload.onprogress=function(i){i.total>0&&(i.percent=i.loaded/i.total*100),e.onProgress(i)});var n=new FormData;e.data&&Object.keys(e.data).forEach(function(r){var i=e.data[r];if(Array.isArray(i)){i.forEach(function(l){n.append("".concat(r,"[]"),l)});return}n.append(r,i)}),e.file instanceof Blob?n.append(e.filename,e.file,e.file.name):n.append(e.filename,e.file),t.onerror=function(i){e.onError(i)},t.onload=function(){return t.status<200||t.status>=300?e.onError(nf(e,t),Vr(t)):e.onSuccess(Vr(t),t)},t.open(e.method,e.action,!0),e.withCredentials&&"withCredentials"in t&&(t.withCredentials=!0);var o=e.headers||{};return o["X-Requested-With"]!==null&&t.setRequestHeader("X-Requested-With","XMLHttpRequest"),Object.keys(o).forEach(function(r){o[r]!==null&&t.setRequestHeader(r,o[r])}),t.send(n),{abort:function(){t.abort()}}}var rf=function(t,n,o){var r=[],i=[];t.forEach(function(d){return i.push(d.webkitGetAsEntry())});function l(d){var u=d.createReader();function m(){u.readEntries(function(f){var p=Array.prototype.slice.apply(f);i.push.apply(i,ve(p));var g=!p.length;g||m()})}m()}var s=function(u,m){u&&(u.path="",u.isFile?u.file(function(f){o(f)&&(u.fullPath&&!f.webkitRelativePath&&(Object.defineProperties(f,{webkitRelativePath:{writable:!0}}),f.webkitRelativePath=u.fullPath.replace(/^\//,""),Object.defineProperties(f,{webkitRelativePath:{writable:!1}})),r.push(f))}):u.isDirectory&&l(u))};function c(){for(var d=0;d<i.length;)s(i[d]),d++;n(r)}c()},af=+new Date,lf=0;function Wn(){return"rc-upload-".concat(af,"-").concat(++lf)}var sf=["component","prefixCls","className","classNames","disabled","id","style","styles","multiple","accept","capture","children","directory","openFileDialogOnClick","onMouseEnter","onMouseLeave","hasControlInside"],cf=function(e){ho(n,e);var t=bo(n);function n(){var o;Co(this,n);for(var r=arguments.length,i=new Array(r),l=0;l<r;l++)i[l]=arguments[l];return o=t.call.apply(t,[this].concat(i)),ee(Ae(o),"state",{uid:Wn()}),ee(Ae(o),"reqs",{}),ee(Ae(o),"fileInput",void 0),ee(Ae(o),"_isMounted",void 0),ee(Ae(o),"onChange",function(s){var c=o.props,d=c.accept,u=c.directory,m=s.target.files,f=ve(m).filter(function(p){return!u||Fn(p,d)});o.uploadFiles(f),o.reset()}),ee(Ae(o),"onClick",function(s){var c=o.fileInput;if(c){var d=s.target,u=o.props.onClick;if(d&&d.tagName==="BUTTON"){var m=c.parentNode;m.focus(),d.blur()}c.click(),u&&u(s)}}),ee(Ae(o),"onKeyDown",function(s){s.key==="Enter"&&o.onClick(s)}),ee(Ae(o),"onFileDrop",function(s){var c=o.props.multiple;if(s.preventDefault(),s.type!=="dragover")if(o.props.directory)rf(Array.prototype.slice.call(s.dataTransfer.items),o.uploadFiles,function(u){return Fn(u,o.props.accept)});else{var d=ve(s.dataTransfer.files).filter(function(u){return Fn(u,o.props.accept)});c===!1&&(d=d.slice(0,1)),o.uploadFiles(d)}}),ee(Ae(o),"uploadFiles",function(s){var c=ve(s),d=c.map(function(u){return u.uid=Wn(),o.processFile(u,c)});Promise.all(d).then(function(u){var m=o.props.onBatchStart;m==null||m(u.map(function(f){var p=f.origin,g=f.parsedFile;return{file:p,parsedFile:g}})),u.filter(function(f){return f.parsedFile!==null}).forEach(function(f){o.post(f)})})}),ee(Ae(o),"processFile",function(){var s=Cl(Jo().mark(function c(d,u){var m,f,p,g,v,$,h,b,y;return Jo().wrap(function(C){for(;;)switch(C.prev=C.next){case 0:if(m=o.props.beforeUpload,f=d,!m){C.next=14;break}return C.prev=3,C.next=6,m(d,u);case 6:f=C.sent,C.next=12;break;case 9:C.prev=9,C.t0=C.catch(3),f=!1;case 12:if(f!==!1){C.next=14;break}return C.abrupt("return",{origin:d,parsedFile:null,action:null,data:null});case 14:if(p=o.props.action,typeof p!="function"){C.next=21;break}return C.next=18,p(d);case 18:g=C.sent,C.next=22;break;case 21:g=p;case 22:if(v=o.props.data,typeof v!="function"){C.next=29;break}return C.next=26,v(d);case 26:$=C.sent,C.next=30;break;case 29:$=v;case 30:return h=(Ke(f)==="object"||typeof f=="string")&&f?f:d,h instanceof File?b=h:b=new File([h],d.name,{type:d.type}),y=b,y.uid=d.uid,C.abrupt("return",{origin:d,data:$,parsedFile:y,action:g});case 35:case"end":return C.stop()}},c,null,[[3,9]])}));return function(c,d){return s.apply(this,arguments)}}()),ee(Ae(o),"saveFileInput",function(s){o.fileInput=s}),o}return yo(n,[{key:"componentDidMount",value:function(){this._isMounted=!0}},{key:"componentWillUnmount",value:function(){this._isMounted=!1,this.abort()}},{key:"post",value:function(r){var i=this,l=r.data,s=r.origin,c=r.action,d=r.parsedFile;if(this._isMounted){var u=this.props,m=u.onStart,f=u.customRequest,p=u.name,g=u.headers,v=u.withCredentials,$=u.method,h=s.uid,b=f||of,y={action:c,filename:p,data:l,file:d,headers:g,withCredentials:v,method:$||"post",onProgress:function(C){var w=i.props.onProgress;w==null||w(C,d)},onSuccess:function(C,w){var x=i.props.onSuccess;x==null||x(C,d,w),delete i.reqs[h]},onError:function(C,w){var x=i.props.onError;x==null||x(C,w,d),delete i.reqs[h]}};m(s),this.reqs[h]=b(y)}}},{key:"reset",value:function(){this.setState({uid:Wn()})}},{key:"abort",value:function(r){var i=this.reqs;if(r){var l=r.uid?r.uid:r;i[l]&&i[l].abort&&i[l].abort(),delete i[l]}else Object.keys(i).forEach(function(s){i[s]&&i[s].abort&&i[s].abort(),delete i[s]})}},{key:"render",value:function(){var r=this.props,i=r.component,l=r.prefixCls,s=r.className,c=r.classNames,d=c===void 0?{}:c,u=r.disabled,m=r.id,f=r.style,p=r.styles,g=p===void 0?{}:p,v=r.multiple,$=r.accept,h=r.capture,b=r.children,y=r.directory,S=r.openFileDialogOnClick,C=r.onMouseEnter,w=r.onMouseLeave,x=r.hasControlInside,O=Oe(r,sf),I=M(ee(ee(ee({},l,!0),"".concat(l,"-disabled"),u),s,s)),P=y?{directory:"directory",webkitdirectory:"webkitdirectory"}:{},j=u?{}:{onClick:S?this.onClick:function(){},onKeyDown:S?this.onKeyDown:function(){},onMouseEnter:C,onMouseLeave:w,onDrop:this.onFileDrop,onDragOver:this.onFileDrop,tabIndex:x?void 0:"0"};return Z.createElement(i,te({},j,{className:I,role:x?void 0:"button",style:f}),Z.createElement("input",te({},vn(O,{aria:!0,data:!0}),{id:m,disabled:u,type:"file",ref:this.saveFileInput,onClick:function(E){return E.stopPropagation()},key:this.state.uid,style:J({display:"none"},g.input),className:d.input,accept:$},P,{multiple:v,onChange:this.onChange},h!=null?{capture:h}:{})),b)}}]),n}(a.Component);function Vn(){}var co=function(e){ho(n,e);var t=bo(n);function n(){var o;Co(this,n);for(var r=arguments.length,i=new Array(r),l=0;l<r;l++)i[l]=arguments[l];return o=t.call.apply(t,[this].concat(i)),ee(Ae(o),"uploader",void 0),ee(Ae(o),"saveUploader",function(s){o.uploader=s}),o}return yo(n,[{key:"abort",value:function(r){this.uploader.abort(r)}},{key:"render",value:function(){return Z.createElement(cf,te({},this.props,{ref:this.saveUploader}))}}]),n}(a.Component);ee(co,"defaultProps",{component:"span",prefixCls:"rc-upload",data:{},headers:{},name:"file",multipart:!1,onStart:Vn,onError:Vn,onSuccess:Vn,multiple:!1,beforeUpload:null,customRequest:null,withCredentials:!1,openFileDialogOnClick:!0,hasControlInside:!1});const df=e=>{const{componentCls:t,iconCls:n}=e;return{[`${t}-wrapper`]:{[`${t}-drag`]:{position:"relative",width:"100%",height:"100%",textAlign:"center",background:e.colorFillAlter,border:`${L(e.lineWidth)} dashed ${e.colorBorder}`,borderRadius:e.borderRadiusLG,cursor:"pointer",transition:`border-color ${e.motionDurationSlow}`,[t]:{padding:e.padding},[`${t}-btn`]:{display:"table",width:"100%",height:"100%",outline:"none",borderRadius:e.borderRadiusLG,"&:focus-visible":{outline:`${L(e.lineWidthFocus)} solid ${e.colorPrimaryBorder}`}},[`${t}-drag-container`]:{display:"table-cell",verticalAlign:"middle"},[`
          &:not(${t}-disabled):hover,
          &-hover:not(${t}-disabled)
        `]:{borderColor:e.colorPrimaryHover},[`p${t}-drag-icon`]:{marginBottom:e.margin,[n]:{color:e.colorPrimary,fontSize:e.uploadThumbnailSize}},[`p${t}-text`]:{margin:`0 0 ${L(e.marginXXS)}`,color:e.colorTextHeading,fontSize:e.fontSizeLG},[`p${t}-hint`]:{color:e.colorTextDescription,fontSize:e.fontSize},[`&${t}-disabled`]:{[`p${t}-drag-icon ${n},
            p${t}-text,
            p${t}-hint
          `]:{color:e.colorTextDisabled}}}}}},uf=e=>{const{componentCls:t,antCls:n,iconCls:o,fontSize:r,lineHeight:i,calc:l}=e,s=`${t}-list-item`,c=`${s}-actions`,d=`${s}-action`,u=e.fontHeightSM;return{[`${t}-wrapper`]:{[`${t}-list`]:Object.assign(Object.assign({},Lt()),{lineHeight:e.lineHeight,[s]:{position:"relative",height:l(e.lineHeight).mul(r).equal(),marginTop:e.marginXS,fontSize:r,display:"flex",alignItems:"center",transition:`background-color ${e.motionDurationSlow}`,"&:hover":{backgroundColor:e.controlItemBgHover},[`${s}-name`]:Object.assign(Object.assign({},hn),{padding:`0 ${L(e.paddingXS)}`,lineHeight:i,flex:"auto",transition:`all ${e.motionDurationSlow}`}),[c]:{whiteSpace:"nowrap",[d]:{opacity:0},[o]:{color:e.actionsColor,transition:`all ${e.motionDurationSlow}`},[`
              ${d}:focus-visible,
              &.picture ${d}
            `]:{opacity:1},[`${d}${n}-btn`]:{height:u,border:0,lineHeight:1}},[`${t}-icon ${o}`]:{color:e.colorTextDescription,fontSize:r},[`${s}-progress`]:{position:"absolute",bottom:e.calc(e.uploadProgressOffset).mul(-1).equal(),width:"100%",paddingInlineStart:l(r).add(e.paddingXS).equal(),fontSize:r,lineHeight:0,pointerEvents:"none","> div":{margin:0}}},[`${s}:hover ${d}`]:{opacity:1},[`${s}-error`]:{color:e.colorError,[`${s}-name, ${t}-icon ${o}`]:{color:e.colorError},[c]:{[`${o}, ${o}:hover`]:{color:e.colorError},[d]:{opacity:1}}},[`${t}-list-item-container`]:{transition:`opacity ${e.motionDurationSlow}, height ${e.motionDurationSlow}`,"&::before":{display:"table",width:0,height:0,content:'""'}}})}}},mf=e=>{const{componentCls:t}=e,n=new Xe("uploadAnimateInlineIn",{from:{width:0,height:0,padding:0,opacity:0,margin:e.calc(e.marginXS).div(-2).equal()}}),o=new Xe("uploadAnimateInlineOut",{to:{width:0,height:0,padding:0,opacity:0,margin:e.calc(e.marginXS).div(-2).equal()}}),r=`${t}-animate-inline`;return[{[`${t}-wrapper`]:{[`${r}-appear, ${r}-enter, ${r}-leave`]:{animationDuration:e.motionDurationSlow,animationTimingFunction:e.motionEaseInOutCirc,animationFillMode:"forwards"},[`${r}-appear, ${r}-enter`]:{animationName:n},[`${r}-leave`]:{animationName:o}}},{[`${t}-wrapper`]:wo(e)},n,o]},ff=e=>{const{componentCls:t,iconCls:n,uploadThumbnailSize:o,uploadProgressOffset:r,calc:i}=e,l=`${t}-list`,s=`${l}-item`;return{[`${t}-wrapper`]:{[`
        ${l}${l}-picture,
        ${l}${l}-picture-card,
        ${l}${l}-picture-circle
      `]:{[s]:{position:"relative",height:i(o).add(i(e.lineWidth).mul(2)).add(i(e.paddingXS).mul(2)).equal(),padding:e.paddingXS,border:`${L(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusLG,"&:hover":{background:"transparent"},[`${s}-thumbnail`]:Object.assign(Object.assign({},hn),{width:o,height:o,lineHeight:L(i(o).add(e.paddingSM).equal()),textAlign:"center",flex:"none",[n]:{fontSize:e.fontSizeHeading2,color:e.colorPrimary},img:{display:"block",width:"100%",height:"100%",overflow:"hidden"}}),[`${s}-progress`]:{bottom:r,width:`calc(100% - ${L(i(e.paddingSM).mul(2).equal())})`,marginTop:0,paddingInlineStart:i(o).add(e.paddingXS).equal()}},[`${s}-error`]:{borderColor:e.colorError,[`${s}-thumbnail ${n}`]:{[`svg path[fill='${er[0]}']`]:{fill:e.colorErrorBg},[`svg path[fill='${er.primary}']`]:{fill:e.colorError}}},[`${s}-uploading`]:{borderStyle:"dashed",[`${s}-name`]:{marginBottom:r}}},[`${l}${l}-picture-circle ${s}`]:{[`&, &::before, ${s}-thumbnail`]:{borderRadius:"50%"}}}}},gf=e=>{const{componentCls:t,iconCls:n,fontSizeLG:o,colorTextLightSolid:r,calc:i}=e,l=`${t}-list`,s=`${l}-item`,c=e.uploadPicCardSize;return{[`
      ${t}-wrapper${t}-picture-card-wrapper,
      ${t}-wrapper${t}-picture-circle-wrapper
    `]:Object.assign(Object.assign({},Lt()),{display:"block",[`${t}${t}-select`]:{width:c,height:c,textAlign:"center",verticalAlign:"top",backgroundColor:e.colorFillAlter,border:`${L(e.lineWidth)} dashed ${e.colorBorder}`,borderRadius:e.borderRadiusLG,cursor:"pointer",transition:`border-color ${e.motionDurationSlow}`,[`> ${t}`]:{display:"flex",alignItems:"center",justifyContent:"center",height:"100%",textAlign:"center"},[`&:not(${t}-disabled):hover`]:{borderColor:e.colorPrimary}},[`${l}${l}-picture-card, ${l}${l}-picture-circle`]:{display:"flex",flexWrap:"wrap","@supports not (gap: 1px)":{"& > *":{marginBlockEnd:e.marginXS,marginInlineEnd:e.marginXS}},"@supports (gap: 1px)":{gap:e.marginXS},[`${l}-item-container`]:{display:"inline-block",width:c,height:c,verticalAlign:"top"},"&::after":{display:"none"},"&::before":{display:"none"},[s]:{height:"100%",margin:0,"&::before":{position:"absolute",zIndex:1,width:`calc(100% - ${L(i(e.paddingXS).mul(2).equal())})`,height:`calc(100% - ${L(i(e.paddingXS).mul(2).equal())})`,backgroundColor:e.colorBgMask,opacity:0,transition:`all ${e.motionDurationSlow}`,content:'" "'}},[`${s}:hover`]:{[`&::before, ${s}-actions`]:{opacity:1}},[`${s}-actions`]:{position:"absolute",insetInlineStart:0,zIndex:10,width:"100%",whiteSpace:"nowrap",textAlign:"center",opacity:0,transition:`all ${e.motionDurationSlow}`,[`
            ${n}-eye,
            ${n}-download,
            ${n}-delete
          `]:{zIndex:10,width:o,margin:`0 ${L(e.marginXXS)}`,fontSize:o,cursor:"pointer",transition:`all ${e.motionDurationSlow}`,color:r,"&:hover":{color:r},svg:{verticalAlign:"baseline"}}},[`${s}-thumbnail, ${s}-thumbnail img`]:{position:"static",display:"block",width:"100%",height:"100%",objectFit:"contain"},[`${s}-name`]:{display:"none",textAlign:"center"},[`${s}-file + ${s}-name`]:{position:"absolute",bottom:e.margin,display:"block",width:`calc(100% - ${L(i(e.paddingXS).mul(2).equal())})`},[`${s}-uploading`]:{[`&${s}`]:{backgroundColor:e.colorFillAlter},[`&::before, ${n}-eye, ${n}-download, ${n}-delete`]:{display:"none"}},[`${s}-progress`]:{bottom:e.marginXL,width:`calc(100% - ${L(i(e.paddingXS).mul(2).equal())})`,paddingInlineStart:0}}}),[`${t}-wrapper${t}-picture-circle-wrapper`]:{[`${t}${t}-select`]:{borderRadius:"50%"}}}},pf=e=>{const{componentCls:t}=e;return{[`${t}-rtl`]:{direction:"rtl"}}},vf=e=>{const{componentCls:t,colorTextDisabled:n}=e;return{[`${t}-wrapper`]:Object.assign(Object.assign({},tt(e)),{[t]:{outline:0,"input[type='file']":{cursor:"pointer"}},[`${t}-select`]:{display:"inline-block"},[`${t}-disabled`]:{color:n,cursor:"not-allowed"}})}},hf=e=>({actionsColor:e.colorTextDescription}),bf=We("Upload",e=>{const{fontSizeHeading3:t,fontHeight:n,lineWidth:o,controlHeightLG:r,calc:i}=e,l=Fe(e,{uploadThumbnailSize:i(t).mul(2).equal(),uploadProgressOffset:i(i(n).div(2)).add(o).equal(),uploadPicCardSize:i(r).mul(2.55).equal()});return[vf(l),df(l),ff(l),gf(l),uf(l),mf(l),pf(l),da(l)]},hf);function Jt(e){return Object.assign(Object.assign({},e),{lastModified:e.lastModified,lastModifiedDate:e.lastModifiedDate,name:e.name,size:e.size,type:e.type,uid:e.uid,percent:0,originFileObj:e})}function en(e,t){const n=ve(t),o=n.findIndex(r=>{let{uid:i}=r;return i===e.uid});return o===-1?n.push(e):n[o]=e,n}function Xn(e,t){const n=e.uid!==void 0?"uid":"name";return t.filter(o=>o[n]===e[n])[0]}function Cf(e,t){const n=e.uid!==void 0?"uid":"name",o=t.filter(r=>r[n]!==e[n]);return o.length===t.length?null:o}const yf=function(){const t=(arguments.length>0&&arguments[0]!==void 0?arguments[0]:"").split("/"),o=t[t.length-1].split(/#|\?/)[0];return(/\.[^./\\]*$/.exec(o)||[""])[0]},wi=e=>e.indexOf("image/")===0,$f=e=>{if(e.type&&!e.thumbUrl)return wi(e.type);const t=e.thumbUrl||e.url||"",n=yf(t);return/^data:image\//.test(t)||/(webp|svg|png|gif|jpg|jpeg|jfif|bmp|dpg|ico|heic|heif)$/i.test(n)?!0:!(/^data:/.test(t)||n)},st=200;function Sf(e){return new Promise(t=>{if(!e.type||!wi(e.type)){t("");return}const n=document.createElement("canvas");n.width=st,n.height=st,n.style.cssText=`position: fixed; left: 0; top: 0; width: ${st}px; height: ${st}px; z-index: 9999; display: none;`,document.body.appendChild(n);const o=n.getContext("2d"),r=new Image;if(r.onload=()=>{const{width:i,height:l}=r;let s=st,c=st,d=0,u=0;i>l?(c=l*(st/i),u=-(c-s)/2):(s=i*(st/l),d=-(s-c)/2),o.drawImage(r,d,u,s,c);const m=n.toDataURL();document.body.removeChild(n),window.URL.revokeObjectURL(r.src),t(m)},r.crossOrigin="anonymous",e.type.startsWith("image/svg+xml")){const i=new FileReader;i.onload=()=>{i.result&&typeof i.result=="string"&&(r.src=i.result)},i.readAsDataURL(e)}else if(e.type.startsWith("image/gif")){const i=new FileReader;i.onload=()=>{i.result&&t(i.result)},i.readAsDataURL(e)}else r.src=window.URL.createObjectURL(e)})}const wf=a.forwardRef((e,t)=>{let{prefixCls:n,className:o,style:r,locale:i,listType:l,file:s,items:c,progress:d,iconRender:u,actionIconRender:m,itemRender:f,isImgUrl:p,showPreviewIcon:g,showRemoveIcon:v,showDownloadIcon:$,previewIcon:h,removeIcon:b,downloadIcon:y,extra:S,onPreview:C,onDownload:w,onClose:x}=e;var O,I;const{status:P}=s,[j,N]=a.useState(P);a.useEffect(()=>{P!=="removed"&&N(P)},[P]);const[E,R]=a.useState(!1);a.useEffect(()=>{const Y=setTimeout(()=>{R(!0)},300);return()=>{clearTimeout(Y)}},[]);const z=u(s);let D=a.createElement("div",{className:`${n}-icon`},z);if(l==="picture"||l==="picture-card"||l==="picture-circle")if(j==="uploading"||!s.thumbUrl&&!s.url){const Y=M(`${n}-list-item-thumbnail`,{[`${n}-list-item-file`]:j!=="uploading"});D=a.createElement("div",{className:Y},z)}else{const Y=p!=null&&p(s)?a.createElement("img",{src:s.thumbUrl||s.url,alt:s.name,className:`${n}-list-item-image`,crossOrigin:s.crossOrigin}):z,ie=M(`${n}-list-item-thumbnail`,{[`${n}-list-item-file`]:p&&!p(s)});D=a.createElement("a",{className:ie,onClick:re=>C(s,re),href:s.url||s.thumbUrl,target:"_blank",rel:"noopener noreferrer"},Y)}const T=M(`${n}-list-item`,`${n}-list-item-${j}`),K=typeof s.linkProps=="string"?JSON.parse(s.linkProps):s.linkProps,U=v?m((typeof b=="function"?b(s):b)||a.createElement(Om,null),()=>x(s),n,i.removeFile,!0):null,V=$&&j==="done"?m((typeof y=="function"?y(s):y)||a.createElement(Nm,null),()=>w(s),n,i.downloadFile):null,F=l!=="picture-card"&&l!=="picture-circle"&&a.createElement("span",{key:"download-delete",className:M(`${n}-list-item-actions`,{picture:l==="picture"})},V,U),A=typeof S=="function"?S(s):S,_=A&&a.createElement("span",{className:`${n}-list-item-extra`},A),B=M(`${n}-list-item-name`),X=s.url?a.createElement("a",Object.assign({key:"view",target:"_blank",rel:"noopener noreferrer",className:B,title:s.name},K,{href:s.url,onClick:Y=>C(s,Y)}),s.name,_):a.createElement("span",{key:"view",className:B,onClick:Y=>C(s,Y),title:s.name},s.name,_),Q=g&&(s.url||s.thumbUrl)?a.createElement("a",{href:s.url||s.thumbUrl,target:"_blank",rel:"noopener noreferrer",onClick:Y=>C(s,Y),title:i.previewFile},typeof h=="function"?h(s):h||a.createElement(ma,null)):null,q=(l==="picture-card"||l==="picture-circle")&&j!=="uploading"&&a.createElement("span",{className:`${n}-list-item-actions`},Q,j==="done"&&V,U),{getPrefixCls:H}=a.useContext(ue),W=H(),G=a.createElement("div",{className:T},D,X,F,q,E&&a.createElement(bt,{motionName:`${W}-fade`,visible:j==="uploading",motionDeadline:2e3},Y=>{let{className:ie}=Y;const re="percent"in s?a.createElement(wm,Object.assign({},d,{type:"line",percent:s.percent,"aria-label":s["aria-label"],"aria-labelledby":s["aria-labelledby"]})):null;return a.createElement("div",{className:M(`${n}-list-item-progress`,ie)},re)})),le=s.response&&typeof s.response=="string"?s.response:((O=s.error)===null||O===void 0?void 0:O.statusText)||((I=s.error)===null||I===void 0?void 0:I.message)||i.uploadError,ae=j==="error"?a.createElement(yn,{title:le,getPopupContainer:Y=>Y.parentNode},G):G;return a.createElement("div",{className:M(`${n}-list-item-container`,o),style:r,ref:t},f?f(ae,s,c,{download:w.bind(null,s),preview:C.bind(null,s),remove:x.bind(null,s)}):ae)}),xf=(e,t)=>{const{listType:n="text",previewFile:o=Sf,onPreview:r,onDownload:i,onRemove:l,locale:s,iconRender:c,isImageUrl:d=$f,prefixCls:u,items:m=[],showPreviewIcon:f=!0,showRemoveIcon:p=!0,showDownloadIcon:g=!1,removeIcon:v,previewIcon:$,downloadIcon:h,extra:b,progress:y={size:[-1,2],showInfo:!1},appendAction:S,appendActionVisible:C=!0,itemRender:w,disabled:x}=e,O=ka(),[I,P]=a.useState(!1);a.useEffect(()=>{n!=="picture"&&n!=="picture-card"&&n!=="picture-circle"||(m||[]).forEach(B=>{typeof document>"u"||typeof window>"u"||!window.FileReader||!window.File||!(B.originFileObj instanceof File||B.originFileObj instanceof Blob)||B.thumbUrl!==void 0||(B.thumbUrl="",o&&o(B.originFileObj).then(X=>{B.thumbUrl=X||"",O()}))})},[n,m,o]),a.useEffect(()=>{P(!0)},[]);const j=(B,X)=>{if(r)return X==null||X.preventDefault(),r(B)},N=B=>{typeof i=="function"?i(B):B.url&&window.open(B.url)},E=B=>{l==null||l(B)},R=B=>{if(c)return c(B,n);const X=B.status==="uploading",Q=d!=null&&d(B)?a.createElement(Dm,null):a.createElement(Mm,null);let q=X?a.createElement(Yn,null):a.createElement(zm,null);return n==="picture"?q=X?a.createElement(Yn,null):Q:(n==="picture-card"||n==="picture-circle")&&(q=X?s.uploading:Q),q},z=(B,X,Q,q,H)=>{const W={type:"text",size:"small",title:q,onClick:G=>{var le,ae;X(),a.isValidElement(B)&&((ae=(le=B.props).onClick)===null||ae===void 0||ae.call(le,G))},className:`${Q}-list-item-action`};if(H&&(W.disabled=x),a.isValidElement(B)){const G=Je(B,Object.assign(Object.assign({},B.props),{onClick:()=>{}}));return a.createElement(ht,Object.assign({},W,{icon:G}))}return a.createElement(ht,Object.assign({},W),a.createElement("span",null,B))};a.useImperativeHandle(t,()=>({handlePreview:j,handleDownload:N}));const{getPrefixCls:D}=a.useContext(ue),T=D("upload",u),K=D(),U=M(`${T}-list`,`${T}-list-${n}`),V=ve(m.map(B=>({key:B.uid,file:B})));let A={motionDeadline:2e3,motionName:`${T}-${n==="picture-card"||n==="picture-circle"?"animate-inline":"animate"}`,keys:V,motionAppear:I};const _=a.useMemo(()=>{const B=Object.assign({},ua(K));return delete B.onAppearEnd,delete B.onEnterEnd,delete B.onLeaveEnd,B},[K]);return n!=="picture-card"&&n!=="picture-circle"&&(A=Object.assign(Object.assign({},_),A)),a.createElement("div",{className:U},a.createElement(yl,Object.assign({},A,{component:!1}),B=>{let{key:X,file:Q,className:q,style:H}=B;return a.createElement(wf,{key:X,locale:s,prefixCls:T,className:q,style:H,file:Q,items:m,progress:y,listType:n,isImgUrl:d,showPreviewIcon:f,showRemoveIcon:p,showDownloadIcon:g,removeIcon:v,previewIcon:$,downloadIcon:h,extra:b,iconRender:R,actionIconRender:z,itemRender:w,onPreview:j,onDownload:N,onClose:E})}),S&&a.createElement(bt,Object.assign({},A,{visible:C,forceRender:!0}),B=>{let{className:X,style:Q}=B;return Je(S,q=>({className:M(q.className,X),style:Object.assign(Object.assign(Object.assign({},Q),{pointerEvents:X?"none":void 0}),q.style)}))}))},If=a.forwardRef(xf);var Of=function(e,t,n,o){function r(i){return i instanceof n?i:new n(function(l){l(i)})}return new(n||(n=Promise))(function(i,l){function s(u){try{d(o.next(u))}catch(m){l(m)}}function c(u){try{d(o.throw(u))}catch(m){l(m)}}function d(u){u.done?i(u.value):r(u.value).then(s,c)}d((o=o.apply(e,[])).next())})};const zt=`__LIST_IGNORE_${Date.now()}__`,Ef=(e,t)=>{const{fileList:n,defaultFileList:o,onRemove:r,showUploadList:i=!0,listType:l="text",onPreview:s,onDownload:c,onChange:d,onDrop:u,previewFile:m,disabled:f,locale:p,iconRender:g,isImageUrl:v,progress:$,prefixCls:h,className:b,type:y="select",children:S,style:C,itemRender:w,maxCount:x,data:O={},multiple:I=!1,hasControlInside:P=!0,action:j="",accept:N="",supportServerRender:E=!0,rootClassName:R}=e,z=a.useContext(Qr),D=f??z,[T,K]=Ve(o||[],{value:n,postState:oe=>oe??[]}),[U,V]=a.useState("drop"),F=a.useRef(null),A=a.useRef(null);a.useMemo(()=>{const oe=Date.now();(n||[]).forEach((ne,de)=>{!ne.uid&&!Object.isFrozen(ne)&&(ne.uid=`__AUTO__${oe}_${de}__`)})},[n]);const _=(oe,ne,de)=>{let se=ve(ne),me=!1;x===1?se=se.slice(-1):x&&(me=se.length>x,se=se.slice(0,x)),Kn.flushSync(()=>{K(se)});const Ie={file:oe,fileList:se};de&&(Ie.event=de),(!me||oe.status==="removed"||se.some(ye=>ye.uid===oe.uid))&&Kn.flushSync(()=>{d==null||d(Ie)})},B=(oe,ne)=>Of(void 0,void 0,void 0,function*(){const{beforeUpload:de,transformFile:se}=e;let me=oe;if(de){const Ie=yield de(oe,ne);if(Ie===!1)return!1;if(delete oe[zt],Ie===zt)return Object.defineProperty(oe,zt,{value:!0,configurable:!0}),!1;typeof Ie=="object"&&Ie&&(me=Ie)}return se&&(me=yield se(me)),me}),X=oe=>{const ne=oe.filter(me=>!me.file[zt]);if(!ne.length)return;const de=ne.map(me=>Jt(me.file));let se=ve(T);de.forEach(me=>{se=en(me,se)}),de.forEach((me,Ie)=>{let ye=me;if(ne[Ie].parsedFile)me.status="uploading";else{const{originFileObj:fe}=me;let Ue;try{Ue=new File([fe],fe.name,{type:fe.type})}catch{Ue=new Blob([fe],{type:fe.type}),Ue.name=fe.name,Ue.lastModifiedDate=new Date,Ue.lastModified=new Date().getTime()}Ue.uid=me.uid,ye=Ue}_(ye,se)})},Q=(oe,ne,de)=>{try{typeof oe=="string"&&(oe=JSON.parse(oe))}catch{}if(!Xn(ne,T))return;const se=Jt(ne);se.status="done",se.percent=100,se.response=oe,se.xhr=de;const me=en(se,T);_(se,me)},q=(oe,ne)=>{if(!Xn(ne,T))return;const de=Jt(ne);de.status="uploading",de.percent=oe.percent;const se=en(de,T);_(de,se,oe)},H=(oe,ne,de)=>{if(!Xn(de,T))return;const se=Jt(de);se.error=oe,se.response=ne,se.status="error";const me=en(se,T);_(se,me)},W=oe=>{let ne;Promise.resolve(typeof r=="function"?r(oe):r).then(de=>{var se;if(de===!1)return;const me=Cf(oe,T);me&&(ne=Object.assign(Object.assign({},oe),{status:"removed"}),T==null||T.forEach(Ie=>{const ye=ne.uid!==void 0?"uid":"name";Ie[ye]===ne[ye]&&!Object.isFrozen(Ie)&&(Ie.status="removed")}),(se=F.current)===null||se===void 0||se.abort(ne),_(ne,me))})},G=oe=>{V(oe.type),oe.type==="drop"&&(u==null||u(oe))};a.useImperativeHandle(t,()=>({onBatchStart:X,onSuccess:Q,onProgress:q,onError:H,fileList:T,upload:F.current,nativeElement:A.current}));const{getPrefixCls:le,direction:ae,upload:Y}=a.useContext(ue),ie=le("upload",h),re=Object.assign(Object.assign({onBatchStart:X,onError:H,onProgress:q,onSuccess:Q},e),{data:O,multiple:I,action:j,accept:N,supportServerRender:E,prefixCls:ie,disabled:D,beforeUpload:B,onChange:void 0,hasControlInside:P});delete re.className,delete re.style,(!S||D)&&delete re.id;const xe=`${ie}-wrapper`,[he,$e,Ce]=bf(ie,xe),[Se]=Cn("Upload",rn.Upload),{showRemoveIcon:Pe,showPreviewIcon:Re,showDownloadIcon:Ne,removeIcon:pe,previewIcon:nt,downloadIcon:ot,extra:Ze}=typeof i=="boolean"?{}:i,ze=typeof Pe>"u"?!D:!!Pe,Me=(oe,ne)=>i?a.createElement(If,{prefixCls:ie,listType:l,items:T,previewFile:m,onPreview:s,onDownload:c,onRemove:W,showRemoveIcon:ze,showPreviewIcon:Re,showDownloadIcon:Ne,removeIcon:pe,previewIcon:nt,downloadIcon:ot,iconRender:g,extra:Ze,locale:Object.assign(Object.assign({},Se),p),isImageUrl:v,progress:$,appendAction:oe,appendActionVisible:ne,itemRender:w,disabled:D}):oe,je=M(xe,b,R,$e,Ce,Y==null?void 0:Y.className,{[`${ie}-rtl`]:ae==="rtl",[`${ie}-picture-card-wrapper`]:l==="picture-card",[`${ie}-picture-circle-wrapper`]:l==="picture-circle"}),Be=Object.assign(Object.assign({},Y==null?void 0:Y.style),C);if(y==="drag"){const oe=M($e,ie,`${ie}-drag`,{[`${ie}-drag-uploading`]:T.some(ne=>ne.status==="uploading"),[`${ie}-drag-hover`]:U==="dragover",[`${ie}-disabled`]:D,[`${ie}-rtl`]:ae==="rtl"});return he(a.createElement("span",{className:je,ref:A},a.createElement("div",{className:oe,style:Be,onDrop:G,onDragOver:G,onDragLeave:G},a.createElement(co,Object.assign({},re,{ref:F,className:`${ie}-btn`}),a.createElement("div",{className:`${ie}-drag-container`},S))),Me()))}const Le=M(ie,`${ie}-select`,{[`${ie}-disabled`]:D}),Qe=a.createElement("div",{className:Le,style:S?void 0:{display:"none"}},a.createElement(co,Object.assign({},re,{ref:F})));return he(l==="picture-card"||l==="picture-circle"?a.createElement("span",{className:je,ref:A},Me(Qe,!!S)):a.createElement("span",{className:je,ref:A},Qe,Me()))},xi=a.forwardRef(Ef);var Pf=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n};const Nf=a.forwardRef((e,t)=>{var{style:n,height:o,hasControlInside:r=!1}=e,i=Pf(e,["style","height","hasControlInside"]);return a.createElement(xi,Object.assign({ref:t,hasControlInside:r},i,{type:"drag",style:Object.assign(Object.assign({},n),{height:o})}))}),pn=xi;pn.Dragger=Nf;pn.LIST_IGNORE=zt;const{Sider:Rf}=et,jf=()=>{const[e,t]=a.useState("1");let n=window.user.user,o=n==null?void 0:n.role;const{data:r,fetchApi:i}=dt("get_profile",{type:"mount",slug:`/${n._id}`,enablePagination:!1}),l=$l(),s={company:[{key:"1",icon:"/admin/assets/img/project-icon.png",activeIcon:"/admin/assets/img/project-active.png",label:"Projects",link:"/projects"},{key:"2",icon:"/admin/assets/img/userrole-icon.png",activeIcon:"/admin/assets/img/userrole-active.png",label:"User Roles",link:"/user-role"},{key:"3",icon:"/admin/assets/img/employees-icon.png",activeIcon:"/admin/assets/img/employees-active.png",label:"Employees",link:"/employees"},{key:"4",icon:"/admin/assets/img/assigned-icon.png",activeIcon:"/admin/assets/img/assigned-active.png",label:"Assigned",link:"/assign-task"},{key:"5",icon:"/admin/assets/img/subscription-icon.png",activeIcon:"/admin/assets/img/subscription-active.png",label:"Subscription",link:"/subscription"},{key:"6",icon:"/admin/assets/img/notifications-icon.png",activeIcon:"/admin/assets/img/notifications-active.png",label:(r==null?void 0:r.unread_notifications)>0?k.jsx(Jn,{count:r==null?void 0:r.unread_notifications,size:"small",offset:[20,7],children:k.jsx("span",{children:"Notifications"})}):"Notifications",link:"/notification"}],employee:[{key:"1",icon:"/admin/assets/img/project-icon.png",activeIcon:"/admin/assets/img/project-active.png",label:"Projects",link:"/projects"},{key:"2",icon:"/admin/assets/img/assigned-icon.png",activeIcon:"/admin/assets/img/assigned-active.png",label:"Assigned Tasks",link:"/assign-task"},{key:"3",icon:"/admin/assets/img/notifications-icon.png",activeIcon:"/admin/assets/img/notifications-active.png",label:(r==null?void 0:r.unread_notifications)>0?k.jsx(Jn,{count:r==null?void 0:r.unread_notifications,size:"small",offset:[20,7],children:k.jsx("span",{children:"Notifications"})}):"Notifications",link:"/notification"},{key:"4",icon:"/admin/assets/img/assigned-icon.png",activeIcon:"/admin/assets/img/assigned-active.png",label:"About App",link:"/employe/aboutapp"},{key:"5",icon:"/admin/assets/img/terms-icon.png",activeIcon:"/admin/assets/img/terms-active.png",label:"Terms & Conditions",link:"/employe/terms-conditions"}]},c=s[o]||s.employee;a.useEffect(()=>{const f=c.reduce((p,g)=>(p[g.link]=g.key,p),{});t(f[l.pathname]||"1")},[l.pathname,c]);const d={overflow:"auto",height:"100vh",position:"fixed",insetInlineStart:0,top:0,bottom:0,scrollbarWidth:"thin",scrollbarColor:"unset"},u=c.map(f=>({key:f.key,icon:k.jsx(tr,{to:f.link,style:{display:"flex",alignItems:"center"},children:k.jsx("img",{src:e===f.key?f.activeIcon:f.icon,alt:f.label})}),label:k.jsx(tr,{to:f.link,style:{display:"flex",alignItems:"center"},children:typeof f.label=="string"?k.jsx("span",{children:f.label}):f.label})})),m=({key:f})=>{t(f);const p=c.find(g=>g.key===f);(p==null?void 0:p.link)==="/notification"&&(r==null?void 0:r.unread_notifications)>0&&i()};return k.jsxs(Rf,{style:d,children:[k.jsx("div",{className:"demo-logo-vertical",children:k.jsx("img",{src:"/admin/assets/img/auth-logo.svg",alt:"logo"})}),k.jsx(Pt,{onClick:m,selectedKeys:[e],theme:"dark",mode:"inline",items:u})]})},Mf=({title:e,items:t,className:n,icon:o})=>k.jsx(si,{menu:{items:t},trigger:["click"],className:n,children:k.jsx("a",{onClick:r=>r.preventDefault(),children:k.jsxs(jo,{children:[e,o&&k.jsx(Wl,{})]})})}),qn=({open:e,onOk:t,onCancel:n,className:o,children:r,footer:i,title:l,width:s})=>k.jsx(Ye,{title:l,open:e,onOk:t,onCancel:n,className:o,footer:i,width:s,children:r}),Un=[{key:"notify_on_new_job_assignment",label:"When someone assigned you a new job"},{key:"notify_on_task_start",label:"When task started"},{key:"notify_on_task_end",label:"When task ended"},{key:"notify_on_project_assignment",label:"When someone assigned you a project"},{key:"notify_on_drawing_revision",label:"When drawing is updated/revision"},{key:"notify_on_task_overdue",label:"When a task is overdue"}],Tf=({onCancel:e})=>{let t=window.user.user;const[n,o]=a.useState({}),[r,i]=a.useState({}),{loading:l,data:s}=dt("get_profile",{type:"mount",slug:`/${t._id}`,enablePagination:!1}),{loading:c,postData:d}=dt("update_profile",{type:"submit"});a.useEffect(()=>{if(s){const g=Un.reduce((v,{key:$})=>(v[$]=s[$]||!1,v),{});o(g),i(g)}},[s]);const u=g=>v=>{o($=>({...$,[g]:v}))},m=()=>{const g=new FormData;Un.forEach(({key:v})=>{g.append(v,n[v])}),d(g,f,t._id)},f=g=>{g.statusCode===200&&(e(),window.user.user=g.data)},p=()=>l||!s?k.jsx(k.Fragment,{children:[...Array(6)].map((g,v)=>k.jsxs("div",{className:"d-flex align-items-center justify-content-between mt-3",children:[k.jsx("div",{style:{width:"60%"},children:k.jsx(it.Input,{active:!0,size:"small",style:{width:"100%"}})}),k.jsx("div",{children:k.jsx(it.Button,{active:!0,size:"small",style:{width:40}})})]},v))}):k.jsx(k.Fragment,{children:Un.map(({key:g,label:v})=>k.jsxs("div",{className:"d-flex align-items-center justify-content-between mt-3",children:[k.jsx("div",{children:k.jsx("p",{className:"font-16",children:v})}),k.jsx("div",{children:k.jsx($i,{checked:n[g],onChange:u(g),loading:c})})]},g))});return k.jsxs(xt,{name:"notification-settings",layout:"vertical",initialValues:{remember:!0},autoComplete:"off",onFinish:m,children:[p(),k.jsx("div",{className:"text-end mt-5",children:k.jsx($o,{title:"Save",className:"add-new-btn",htmlType:"submit",loading:c||l,disabled:l||!s})})]})},kf=a.memo(Tf),zf=({onCancel:e})=>{const{loading:t,postData:n}=dt("change_password",{type:"submit"}),[o]=xt.useForm(),r=s=>{const c=new FormData;for(const d in s)c.append(d,s[d]);n(c,i)},i=s=>{s.statusCode==200&&(o.resetFields(),e())},l=[{required:!0,message:"New Password is required!"},{pattern:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,message:"Password must be at least 8 characters long, contain uppercase, lowercase, a number, and a special character."}];return k.jsxs(xt,{name:"changepassword",layout:"vertical",onFinish:r,initialValues:{remember:!0},form:o,autoComplete:"off",children:[k.jsx(gt,{type:"password",name:"old_password",id:"password",placeholder:"......",label:"Current Password",rules:[{required:!0,message:"Current Password is required!"}]}),k.jsx(gt,{type:"password",name:"new_password",id:"password",placeholder:"......",label:"New Password",rules:l}),k.jsx(gt,{type:"password",name:"confirmPassword",id:"confirmPassword",placeholder:".......",label:"Confirm Password",dependencies:["new_password"],rules:[{required:!0,message:"Confirm Password is required!"},({getFieldValue:s})=>({validator(c,d){return!d||s("new_password")===d?Promise.resolve():Promise.reject(new Error("Passwords do not match!"))}})]}),k.jsx("div",{className:"text-end mt-4",children:k.jsx($o,{title:"Submit",className:"add-new-btn",htmlType:"submit",loading:t})})]})},Bf=e=>new Promise((t,n)=>{const o=new FileReader;o.readAsDataURL(e),o.onload=()=>t(o.result),o.onerror=r=>n(r)}),Lf=({value:e=[],onChange:t,allowedTypes:n=["application/pdf"],maxSizeMB:o=20,resetImages:r=!1,maxFiles:i=10})=>{const[l,s]=a.useState([]),[c,d]=a.useState(""),[u,m]=a.useState(!1),f=a.useCallback(b=>b.map((y,S)=>({uid:`existing-${S}`,name:typeof y=="string"?y.split("/").pop():y.name||`File ${S+1}`,status:"done",url:typeof y=="string"?y:void 0,originFileObj:typeof y!="string"?y:void 0,type:typeof y!="string"?y.type:void 0})),[]);a.useEffect(()=>{if(r&&e.length===0){s([]);return}const b=f(e);s(b)},[e,r,f]);const p=a.useCallback(({file:b,fileList:y})=>{if(y.length>i){window.uploadErrorShown||(window.uploadErrorShown=!0,nr.error(`You can only upload up to ${i} files`),setTimeout(()=>{window.uploadErrorShown=!1},1e3));return}const S=y.map(C=>({...C,status:C.status||"done"}));if(s(S),t){const C=S.filter(w=>w.status==="done"||w.originFileObj).map(w=>w.originFileObj||w.url);t(C)}},[t,i]),g=a.useCallback(async b=>{if(b.type==="application/pdf"){window.open(b.url||URL.createObjectURL(b.originFileObj),"_blank");return}!b.url&&!b.preview&&(b.preview=await Bf(b.originFileObj)),d(b.url||b.preview),m(!0)},[]),v=a.useCallback((b,y)=>{const S=[];return l.length+y.length>i&&S.push(`Maximum ${i} files allowed`),n.includes(b.type)||S.push(`Please upload ${n.join(", ")} files only`),b.size/1024/1024<o||S.push(`File must be smaller than ${o}MB`),S.length>0?(window.uploadErrorShown||(window.uploadErrorShown=!0,nr.error(S.join("; ")),setTimeout(()=>{window.uploadErrorShown=!1},1e3)),pn.LIST_IGNORE):!0},[l.length,i,n,o]),$=a.useCallback(b=>{const y=l.filter(S=>S.uid!==b.uid);if(s(y),t){const S=y.filter(C=>C.status==="done"||C.originFileObj).map(C=>C.originFileObj||C.url);t(S)}},[l,t]),h=k.jsxs("div",{children:[k.jsx(Xd,{}),k.jsx("div",{style:{marginTop:8},children:"Upload"})]});return k.jsxs(k.Fragment,{children:[k.jsx(pn,{listType:"picture-card",fileList:l,onChange:p,beforeUpload:v,onRemove:$,onPreview:g,multiple:!0,customRequest:({onSuccess:b})=>setTimeout(()=>b("ok"),0),children:l.length>=i?null:h}),c&&k.jsx(pi,{wrapperStyle:{display:"none"},preview:{visible:u,onVisibleChange:b=>m(b)},src:c})]})},Df=Z.memo(Lf),_f=({onCancel:e})=>{let t=window.user.user;const[n,o]=a.useState([]),[r,i]=a.useState(!1),{loading:l,data:s}=dt("get_profile",{type:"mount",slug:`/${t._id}`,enablePagination:!1}),c=window.helper.generateRanges(50,1e3),{loading:d,postData:u}=dt("update_profile",{type:"submit"}),[m]=xt.useForm();a.useEffect(()=>{s!=null&&s.image_url&&(s==null?void 0:s.image_url.length)>0?o([s==null?void 0:s.image_url]):o([])},[s]);const f=g=>{const v=new FormData;if(i(!0),(n==null?void 0:n.length)>0){const $=n[0];v.append("image",$)}for(const $ in g)v.append($,g[$]);u(v,p,t._id)},p=g=>{g.statusCode===200&&(e(),window.user.user=g.data,m.resetFields(),setTimeout(()=>i(!1),0),window.location.reload())};return l||!s?k.jsx(it,{active:!0,paragraph:{rows:10}}):k.jsxs(xt,{name:"edit",layout:"vertical",onFinish:f,form:m,initialValues:{name:(t==null?void 0:t.role)==="user"?s&&(s==null?void 0:s.name):void 0,company_name:t.role==="company"?s==null?void 0:s.company_name:void 0,company_size:t.role==="company"?s==null?void 0:s.company_size:void 0,mobile_no:(s==null?void 0:s.mobile_no)||"",company_details:t.role==="company"?s==null?void 0:s.company_details:void 0,remember:!0},autoComplete:"off",children:[k.jsx(Df,{maxFiles:1,value:n,resetImages:r,onChange:g=>o(g),allowedTypes:["image/jpeg","image/jpg","image/png"]}),t.role==="user"?k.jsx(gt,{name:"name",placeholder:"Enter Name",label:"Name",rules:Zt.name}):k.jsxs(k.Fragment,{children:[k.jsx(gt,{name:"company_name",placeholder:"Enter company name",label:"Company Name",rules:Zt.company_name}),k.jsx(gt,{type:"select",name:"company_size",placeholder:"Company Size",label:"Company Size",options:c==null?void 0:c.map(g=>({value:g.key,label:g.value})),rules:Zt.company_size}),k.jsx("div",{className:"phone-input-container",children:k.jsx(xt.Item,{label:"Phone Number",name:"mobile_no",validateTrigger:"onBlur",rules:[{required:!0,message:"Phone Number is required!"},{validator:(g,v)=>v&&v.replace(/\D/g,"").slice(1).length!==10?Promise.reject("Phone number must be 10 digits"):Promise.resolve()}],children:k.jsx(Xl,{id:"mobile_no",placeholder:"Enter phone number",className:"base-input",international:!1,defaultCountry:"US",disabled:!0})})}),k.jsx(gt,{type:"textarea",name:"company_details",placeholder:"Enter company details",label:"Company Details",rules:Zt.company_details})]}),k.jsx("div",{className:"text-end mt-4",children:k.jsx($o,{title:"Update",className:"add-new-btn",htmlType:"submit",loading:d})})]})},Af=a.memo(_f),{Header:Hf}=et,Ff=()=>{let e=window.user.user;const t=Sl(),{showAlert:n}=Vl(),[o,r]=a.useState(!1),[i,l]=a.useState(!1),[s,c]=a.useState(!1),{data:d}=dt("get_profile",{type:"mount",slug:`/${e._id}`,enablePagination:!1}),{postData:u}=dt("remove_device_token",{type:"submit",skipNotification:!0}),m=async()=>{const h=new FormData;let b=await ql();b=`web|${b}`,h.append("device_token",b),(await n({title:"Are you sure?",text:"Do you want to proceed with this action?",icon:"warning",background:"#f6f6f6",showCancelButton:!0,confirmButtonText:"Yes, proceed",cancelButtonText:"No"})).isConfirmed&&u(h,f)},f=h=>{if(h.statusCode==200)return window.helper.removeStorageData(),t("/")},p=[{label:k.jsx("p",{className:"color-blue font-16",children:d==null?void 0:d.company_name}),key:"0"},{label:k.jsx("p",{children:d==null?void 0:d.mobile_no}),key:"1"},{label:k.jsxs("p",{children:[d==null?void 0:d.company_size," Employees"]}),key:"2"},{type:"divider"},{label:k.jsx("p",{className:"color-light",children:d==null?void 0:d.company_details}),key:"3"},{label:k.jsx("p",{onClick:()=>c(!0),className:"color-blue font-14",children:"Edit Profile"}),key:"4"},{label:k.jsx("p",{onClick:()=>l(!0),className:"color-blue font-14",children:"Change Password"}),key:"5"},{label:k.jsxs("p",{onClick:()=>r(!0),className:"color-blue font-14",children:[" ","Notification Settings"]}),key:"6"},{type:"divider"},{label:k.jsx("p",{onClick:m,className:"color-red",children:"Log Out"}),key:"7"}],g=[{label:k.jsx("p",{className:"color-blue font-16",children:d==null?void 0:d.name}),key:"0"},{label:k.jsx("p",{children:d!=null&&d.mobile_no?d==null?void 0:d.mobile_no:""}),key:"1"},{label:k.jsx("p",{children:d==null?void 0:d.email}),key:"2"},{type:"divider"},{label:k.jsx("p",{className:"color-light",children:d==null?void 0:d.company_details}),key:"3"},{label:k.jsx("p",{onClick:()=>c(!0),className:"color-blue font-14",children:"Edit Profile"}),key:"4"},{label:k.jsx("p",{onClick:()=>l(!0),className:"color-blue font-14",children:"Change Password"}),key:"5"},{label:k.jsxs("p",{onClick:()=>r(!0),className:"color-blue font-14",children:[" ","Notification Settings"]}),key:"6"},{type:"divider"},{label:k.jsx("p",{onClick:m,className:"color-red",children:"Log Out"}),key:"7"}],{token:{colorBgContainer:v,borderRadiusLG:$}}=Si.useToken();return k.jsxs(k.Fragment,{children:[k.jsx(Hf,{style:{padding:0,background:v},children:k.jsx("div",{children:k.jsx(Mf,{className:"profile-dropdown",items:(d==null?void 0:d.role)=="user"?g:p,title:k.jsx(Aa,{size:50,src:d==null?void 0:d.image_url})})})}),k.jsx(qn,{title:"Notification Settings",onCancel:()=>r(!1),open:o,className:"custom-modal notification-setting-modal",footer:!1,children:k.jsx(kf,{onCancel:()=>r(!1)})}),k.jsx(qn,{title:"Change Password",onCancel:()=>l(!1),open:i,className:"custom-modal",footer:!1,children:k.jsx(zf,{onCancel:()=>l(!1)})}),k.jsx(qn,{title:"Edit Profile",onCancel:()=>c(!1),open:s,className:"custom-modal",footer:!1,children:k.jsx(Af,{onCancel:()=>c(!1),visible:s})})]})},{Content:Wf}=et,Kf=({children:e})=>{const{token:{colorBgContainer:t,borderRadiusLG:n}}=Si.useToken();return a.useEffect(()=>{let o=null;return rr&&!window.firebaseMessageListenerSet&&(window.firebaseMessageListenerSet=!0,o=Ul(rr,r=>{console.log("Foreground message received:",r)})),()=>{o&&(o(),window.firebaseMessageListenerSet=!1)}},[]),k.jsxs(et,{children:[k.jsx(jf,{}),k.jsxs(et,{style:{marginInlineStart:200},children:[k.jsx(Ff,{}),k.jsx(Wf,{style:{margin:"24px 16px 0",overflow:"initial"},children:k.jsx("div",{style:{borderRadius:n},children:e})})]})]})},Gf=({title:e,buttons:t})=>k.jsxs("div",{className:"content-header",children:[k.jsx("div",{children:k.jsx("h2",{children:e})}),k.jsx("div",{className:"content-header-child",children:t})]});export{Aa as A,Df as C,si as D,Xt as E,Kf as I,Pt as M,Od as O,Gf as P,sn as R,it as S,qn as a,jo as b,Mf as c,pi as d,$i as e,St as f,Zd as g,Xs as h,io as i,xn as j,Mc as k,Xd as l,ni as m,ka as u};
