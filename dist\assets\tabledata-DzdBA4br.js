import{j as i,L as m,h as c}from"./index-BNo7PP7q.js";import{c as h}from"./index-C2aV_UZj.js";var p,x;(x=(p=window.user)==null?void 0:p.user)==null||x.role;const f=[{label:i.jsx("a",{href:"#",className:"color-blue",children:"Edit"}),key:"0"},{label:i.jsx("a",{href:"#",className:"color-red",children:"Delete"}),key:"1"}],_=i.jsx("img",{src:"/admin/assets/img/folder-icon.png",alt:""}),y=i.jsx("img",{src:"/admin/assets/img/table-delete-icon.png",alt:""}),w=i.jsx("img",{src:"/admin/assets/img/pdf-icon.png",alt:""}),I=i.jsx("img",{src:"/admin/assets/img/table-img.png",alt:""}),r=(l,t)=>i.jsxs("div",{className:"d-flex align-items-center",children:[i.jsx("div",{children:t}),i.jsx("div",{className:"ms-2 color-dark-blue ",children:l})]}),k=(l,t,a)=>[{title:"Name",dataIndex:"title",render:(e,n)=>i.jsxs("div",{className:`d-flex align-items-center ${t===1?"list-folder":"large-folder"}`,children:[i.jsx("img",{src:"/admin/assets/img/folder-icon.png",alt:n.title,style:{marginRight:8}}),i.jsx(m,{to:`/directories/${n.project._id}/${n._id}`,state:{record:n},children:n.title})]}),sorter:(e,n)=>e.title.length-n.title.length,width:"45%"},{title:"Last Updated",dataIndex:"created_at",render:(e,n)=>c(e).format("MMM D, YYYY, h:mm A"),width:"30%"},{title:"Updated by",dataIndex:"updatedby",width:"15%"},{title:"",dataIndex:"icon",render:(e,n)=>{var s,d,o;return((a==null?void 0:a.role)==="company"||(a==null?void 0:a._id)===((d=(s=n==null?void 0:n.project)==null?void 0:s.user)==null?void 0:d._id)&&((o=a==null?void 0:a.policies)==null?void 0:o.some(g=>g.module==="project"&&g.can_create)))&&i.jsx("div",{className:"cursor-pointer",onClick:()=>l(n._id),children:i.jsx("img",{src:"/admin/assets/img/table-delete-icon.png",alt:""})})},align:"center",width:"5%"}],A=(l,t)=>[{title:"Name",dataIndex:"title",render:(a,e)=>i.jsxs("div",{className:"d-flex align-items-center",children:[i.jsx("img",{src:"/admin/assets/img/folder-icon.png",alt:e.title,style:{marginRight:8}}),i.jsx(m,{to:`/directories-detail/${e.project._id}/${e._id}`,state:{record:e},children:e.title})]}),sorter:(a,e)=>a.title.length-e.title.length,width:"45%"},{title:"Last Updated",dataIndex:"created_at",render:(a,e)=>c(a).format("MMM D, YYYY, h:mm A"),width:"30%"},{title:"Updated by",dataIndex:"updatedby",width:"15%"},{title:"",dataIndex:"icon",render:(a,e)=>{var n,s,d;return((t==null?void 0:t.role)==="company"||(t==null?void 0:t._id)===((s=(n=e==null?void 0:e.project)==null?void 0:n.user)==null?void 0:s._id)&&((d=t==null?void 0:t.policies)==null?void 0:d.some(o=>o.module==="project"&&o.can_create)))&&i.jsx("div",{className:"cursor-pointer",onClick:()=>l(e._id),children:i.jsx("img",{src:"/admin/assets/img/table-delete-icon.png",alt:""})})},align:"center",width:"5%"}];Array.from({length:8},(l,t)=>({key:t+1,name:r(["Main Room","Mechanical Room","Electrical Room","First Floor","Basement","Parking","Stairs","Roof"][t],_),lastupdated:"July 7, 2024. 08:45 AM",updatedby:"Justin",icon:y}));const M=(l,t)=>[{title:"Name",dataIndex:"title",render:(a,e)=>i.jsxs("div",{className:"d-flex align-items-center",children:[i.jsx("img",{src:"/admin/assets/img/pdf-icon.png",alt:e.title,style:{marginRight:8}}),i.jsx(m,{to:`/view-pdf/${e.project._id}/${e.parent_id}`,state:{record:e},children:e.title})]}),sorter:(a,e)=>a.title.length-e.title.length,width:"30%"},{title:"Revision",dataIndex:"revision",width:"20%"},{title:"Last Updated",dataIndex:"created_at",render:(a,e)=>c(a).format("MMM D, YYYY, h:mm A"),width:"30%"},{title:"Updated by",dataIndex:"updatedby",width:"15%"},{title:"",dataIndex:"icon",render:(a,e)=>{var n,s,d;return((t==null?void 0:t.role)==="company"||(t==null?void 0:t._id)===((s=(n=e==null?void 0:e.project)==null?void 0:n.user)==null?void 0:s._id)&&((d=t==null?void 0:t.policies)==null?void 0:d.some(o=>o.module==="project"&&o.can_create)))&&i.jsx("div",{className:"cursor-pointer",onClick:()=>l(e._id),children:i.jsx("img",{src:"/admin/assets/img/table-delete-icon.png",alt:""})})},align:"center",width:"5%"}],R=({onViewPdf:l})=>[{title:"Name",dataIndex:"title",render:(t,a)=>i.jsxs("div",{className:"d-flex align-items-center",children:[i.jsx("img",{src:"/admin/assets/img/pdf-icon.png",alt:a.title,style:{marginRight:8}}),i.jsx("span",{style:{cursor:"pointer",color:"#000126"},onClick:()=>l(a),children:a.title})]}),width:"100%"}];Array.from({length:9},(l,t)=>({key:t+1,name:r("ASF Trench block 1",w),revision:["Revision "]+[t+1],lastupdated:"July 7, 2024. 08:45 AM",updatedby:"Justin",icon:y}));Array.from({length:5},(l,t)=>({key:t+1,name:r("Paul Harrisons",I),status:"Active",emailaddress:"<EMAIL>",company:"BluePrintFix",usertype:"Design & Office",action:i.jsx(h,{title:"Action",className:"table-dropdown",icon:"true",items:f})}));Array.from({length:8},(l,t)=>({key:t+1,usertype:["Sales Representative","Electrical Engineer"][t%2],action:i.jsx(h,{title:"Action",icon:"true",className:"table-dropdown",items:f})}));export{k as C,A as a,M as b,R as c};
