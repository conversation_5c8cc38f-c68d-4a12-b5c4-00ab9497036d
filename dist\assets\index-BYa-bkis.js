import{r as a,j as e,L as s}from"./index-BNo7PP7q.js";const l=()=>e.jsxs("div",{className:"user-role-page",children:[e.jsx("header",{className:"header-area py-3",children:e.jsx("div",{className:"container text-center",children:e.jsx(s,{to:"/",children:e.jsx("img",{src:"/admin/assets/img/auth-logo.svg",alt:"CONSTRUCTIFIED",className:"brand-logo"})})})}),e.jsx("main",{children:e.jsx("section",{className:"user-role-sec",children:e.jsxs("div",{className:"container text-center",children:[e.jsx("h2",{className:"display-5 mb-3",children:"Choose your Role!"}),e.jsx("p",{className:"lead",children:"Please select your role before moving further"}),e.jsxs("div",{className:"d-flex align-items-center justify-content-center gap-4 mt-4",children:[e.jsxs(s,{to:"/login/company",className:"role-box text-center",children:[e.jsx("div",{className:"icon-box",children:e.jsx("img",{src:"/admin/assets/img/company.png",alt:"Company",className:"img-fluid"})}),e.jsx("h3",{className:"h4 mt-2",children:"Company"})]}),e.jsxs(s,{to:"/login/user",className:"role-box text-center",children:[e.jsx("div",{className:"icon-box",children:e.jsx("img",{src:"/admin/assets/img/employee.png",alt:"Employee",className:"img-fluid"})}),e.jsx("h3",{className:"h4 mt-2",children:"Employee"})]})]})]})})})]}),i=a.memo(l);export{i as default};
