function e(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function t(t){for(var n=1;n<arguments.length;n++){var r=null!=arguments[n]?arguments[n]:{};n%2?e(Object(r),!0).forEach((function(e){s(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):e(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function n(){n=function(){return t};var e,t={},r=Object.prototype,o=r.hasOwnProperty,a=Object.defineProperty||function(e,t,n){e[t]=n.value},i="function"==typeof Symbol?Symbol:{},u=i.iterator||"@@iterator",l=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function s(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{s({},"")}catch(e){s=function(e,t,n){return e[t]=n}}function f(e,t,n,r){var o=t&&t.prototype instanceof A?t:A,i=Object.create(o.prototype),u=new R(r||[]);return a(i,"_invoke",{value:S(e,n,u)}),i}function d(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=f;var p="suspendedStart",h="suspendedYield",g="executing",v="completed",_={};function A(){}function m(){}function y(){}var P={};s(P,u,(function(){return this}));var M=Object.getPrototypeOf,w=M&&M(M(G([])));w&&w!==r&&o.call(w,u)&&(P=w);var x=y.prototype=A.prototype=Object.create(P);function I(e){["next","throw","return"].forEach((function(t){s(e,t,(function(e){return this._invoke(t,e)}))}))}function b(e,t){function n(r,a,i,u){var l=d(e[r],e,a);if("throw"!==l.type){var c=l.arg,s=c.value;return s&&"object"==typeof s&&o.call(s,"__await")?t.resolve(s.__await).then((function(e){n("next",e,i,u)}),(function(e){n("throw",e,i,u)})):t.resolve(s).then((function(e){c.value=e,i(c)}),(function(e){return n("throw",e,i,u)}))}u(l.arg)}var r;a(this,"_invoke",{value:function(e,o){function a(){return new t((function(t,r){n(e,o,t,r)}))}return r=r?r.then(a,a):a()}})}function S(t,n,r){var o=p;return function(a,i){if(o===g)throw Error("Generator is already running");if(o===v){if("throw"===a)throw i;return{value:e,done:!0}}for(r.method=a,r.arg=i;;){var u=r.delegate;if(u){var l=C(u,r);if(l){if(l===_)continue;return l}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===p)throw o=v,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=g;var c=d(t,n,r);if("normal"===c.type){if(o=r.done?v:h,c.arg===_)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(o=v,r.method="throw",r.arg=c.arg)}}}function C(t,n){var r=n.method,o=t.iterator[r];if(o===e)return n.delegate=null,"throw"===r&&t.iterator.return&&(n.method="return",n.arg=e,C(t,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),_;var a=d(o,t.iterator,n.arg);if("throw"===a.type)return n.method="throw",n.arg=a.arg,n.delegate=null,_;var i=a.arg;return i?i.done?(n[t.resultName]=i.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=e),n.delegate=null,_):i:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,_)}function T(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function E(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function R(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(T,this),this.reset(!0)}function G(t){if(t||""===t){var n=t[u];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var r=-1,a=function n(){for(;++r<t.length;)if(o.call(t,r))return n.value=t[r],n.done=!1,n;return n.value=e,n.done=!0,n};return a.next=a}}throw new TypeError(typeof t+" is not iterable")}return m.prototype=y,a(x,"constructor",{value:y,configurable:!0}),a(y,"constructor",{value:m,configurable:!0}),m.displayName=s(y,c,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===m||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,y):(e.__proto__=y,s(e,c,"GeneratorFunction")),e.prototype=Object.create(x),e},t.awrap=function(e){return{__await:e}},I(b.prototype),s(b.prototype,l,(function(){return this})),t.AsyncIterator=b,t.async=function(e,n,r,o,a){void 0===a&&(a=Promise);var i=new b(f(e,n,r,o),a);return t.isGeneratorFunction(n)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},I(x),s(x,c,"Generator"),s(x,u,(function(){return this})),s(x,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=G,R.prototype={constructor:R,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(E),!t)for(var n in this)"t"===n.charAt(0)&&o.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function r(r,o){return u.type="throw",u.arg=t,n.next=r,o&&(n.method="next",n.arg=e),!!o}for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a],u=i.completion;if("root"===i.tryLoc)return r("end");if(i.tryLoc<=this.prev){var l=o.call(i,"catchLoc"),c=o.call(i,"finallyLoc");if(l&&c){if(this.prev<i.catchLoc)return r(i.catchLoc,!0);if(this.prev<i.finallyLoc)return r(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return r(i.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return r(i.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&o.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var a=r;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=e,i.arg=t,a?(this.method="next",this.next=a.finallyLoc,_):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),_},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),E(n),_}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;E(n)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,n,r){return this.delegate={iterator:G(t),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=e),_}},t}function r(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}function o(e){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o(e)}function a(e,t,n,r,o,a,i){try{var u=e[a](i),l=u.value}catch(e){return void n(e)}u.done?t(l):Promise.resolve(l).then(r,o)}function i(e){return function(){var t=this,n=arguments;return new Promise((function(r,o){var i=e.apply(t,n);function u(e){a(i,r,o,u,l,"next",e)}function l(e){a(i,r,o,u,l,"throw",e)}u(void 0)}))}}function u(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function l(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,r(o.key),o)}}function c(e,t,n){return t&&l(e.prototype,t),n&&l(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function s(e,t,n){return(t=r(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function f(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,a,i,u=[],l=!0,c=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(r=a.call(n)).done)&&(u.push(r.value),u.length!==t);l=!0);}catch(e){c=!0,o=e}finally{try{if(!l&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(c)throw o}}return u}}(e,t)||p(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function d(e){return function(e){if(Array.isArray(e))return h(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||p(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function p(e,t){if(e){if("string"==typeof e)return h(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?h(e,t):void 0}}function h(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var g,v=new Uint8Array(16);function _(){if(!g&&!(g="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return g(v)}for(var A=[],m=0;m<256;++m)A.push((m+256).toString(16).slice(1));var y={randomUUID:"undefined"!=typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)};function P(e,t,n){if(y.randomUUID&&!t&&!e)return y.randomUUID();var r=(e=e||{}).random||(e.rng||_)();if(r[6]=15&r[6]|64,r[8]=63&r[8]|128,t){n=n||0;for(var o=0;o<16;++o)t[n+o]=r[o];return t}return function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return A[e[t+0]]+A[e[t+1]]+A[e[t+2]]+A[e[t+3]]+"-"+A[e[t+4]]+A[e[t+5]]+"-"+A[e[t+6]]+A[e[t+7]]+"-"+A[e[t+8]]+A[e[t+9]]+"-"+A[e[t+10]]+A[e[t+11]]+A[e[t+12]]+A[e[t+13]]+A[e[t+14]]+A[e[t+15]]}(r)}var M,w,x="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},I={};function b(e){var t=e.getTimezoneOffset(),n=t<0?"+":"-",r=("00"+Math.abs(t/60)).slice(-2),o=("00"+Math.abs(t%60)).slice(-2),a="".concat(n).concat(r,"'").concat(o,"'"),i=e.getFullYear(),u=("00"+(e.getMonth()+1)).slice(-2),l=("00"+e.getDate()).slice(-2),c=("00"+e.getHours()).slice(-2),s=("00"+e.getMinutes()).slice(-2),f=("00"+e.getSeconds()).slice(-2);return"D:".concat(i).concat(u).concat(l).concat(c).concat(s).concat(f).concat(a)}function S(e){if(!e)return new Date;var t=e.match(/D:(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})([-+]\d{2}'\d{2}')?/);if(t){var n=f(t,8),r=n[1],o=n[2],a=n[3],i=n[4],u=n[5],l=n[6];return n[7],new Date(parseInt(r),parseInt(o)-1,parseInt(a),parseInt(i),parseInt(u),parseInt(l))}throw new Error("Invalid timestamp format")}function C(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2,n=Math.pow(10,t);return Math.round(e*n)/n}M={get exports(){return I},set exports(e){I=e}},w=I,function(){var e,t="Expected a function",n="__lodash_hash_undefined__",r="__lodash_placeholder__",a=16,i=32,u=64,l=128,c=256,s=1/0,f=9007199254740991,d=NaN,p=4294967295,h=[["ary",l],["bind",1],["bindKey",2],["curry",8],["curryRight",a],["flip",512],["partial",i],["partialRight",u],["rearg",c]],g="[object Arguments]",v="[object Array]",_="[object Boolean]",A="[object Date]",m="[object Error]",y="[object Function]",P="[object GeneratorFunction]",I="[object Map]",b="[object Number]",S="[object Object]",C="[object Promise]",T="[object RegExp]",E="[object Set]",R="[object String]",G="[object Symbol]",F="[object WeakMap]",D="[object ArrayBuffer]",B="[object DataView]",U="[object Float32Array]",L="[object Float64Array]",N="[object Int8Array]",k="[object Int16Array]",O="[object Int32Array]",j="[object Uint8Array]",W="[object Uint8ClampedArray]",H="[object Uint16Array]",Y="[object Uint32Array]",Q=/\b__p \+= '';/g,z=/\b(__p \+=) '' \+/g,q=/(__e\(.*?\)|\b__t\)) \+\n'';/g,K=/&(?:amp|lt|gt|quot|#39);/g,J=/[&<>"']/g,X=RegExp(K.source),Z=RegExp(J.source),V=/<%-([\s\S]+?)%>/g,$=/<%([\s\S]+?)%>/g,ee=/<%=([\s\S]+?)%>/g,te=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,ne=/^\w*$/,re=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,oe=/[\\^$.*+?()[\]{}|]/g,ae=RegExp(oe.source),ie=/^\s+/,ue=/\s/,le=/\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/,ce=/\{\n\/\* \[wrapped with (.+)\] \*/,se=/,? & /,fe=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g,de=/[()=,{}\[\]\/\s]/,pe=/\\(\\)?/g,he=/\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g,ge=/\w*$/,ve=/^[-+]0x[0-9a-f]+$/i,_e=/^0b[01]+$/i,Ae=/^\[object .+?Constructor\]$/,me=/^0o[0-7]+$/i,ye=/^(?:0|[1-9]\d*)$/,Pe=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,Me=/($^)/,we=/['\n\r\u2028\u2029\\]/g,xe="\\ud800-\\udfff",Ie="\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff",be="\\u2700-\\u27bf",Se="a-z\\xdf-\\xf6\\xf8-\\xff",Ce="A-Z\\xc0-\\xd6\\xd8-\\xde",Te="\\ufe0e\\ufe0f",Ee="\\xac\\xb1\\xd7\\xf7\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf\\u2000-\\u206f \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",Re="['’]",Ge="["+xe+"]",Fe="["+Ee+"]",De="["+Ie+"]",Be="\\d+",Ue="["+be+"]",Le="["+Se+"]",Ne="[^"+xe+Ee+Be+be+Se+Ce+"]",ke="\\ud83c[\\udffb-\\udfff]",Oe="[^"+xe+"]",je="(?:\\ud83c[\\udde6-\\uddff]){2}",We="[\\ud800-\\udbff][\\udc00-\\udfff]",He="["+Ce+"]",Ye="\\u200d",Qe="(?:"+Le+"|"+Ne+")",ze="(?:"+He+"|"+Ne+")",qe="(?:['’](?:d|ll|m|re|s|t|ve))?",Ke="(?:['’](?:D|LL|M|RE|S|T|VE))?",Je="(?:"+De+"|"+ke+")?",Xe="["+Te+"]?",Ze=Xe+Je+"(?:"+Ye+"(?:"+[Oe,je,We].join("|")+")"+Xe+Je+")*",Ve="(?:"+[Ue,je,We].join("|")+")"+Ze,$e="(?:"+[Oe+De+"?",De,je,We,Ge].join("|")+")",et=RegExp(Re,"g"),tt=RegExp(De,"g"),nt=RegExp(ke+"(?="+ke+")|"+$e+Ze,"g"),rt=RegExp([He+"?"+Le+"+"+qe+"(?="+[Fe,He,"$"].join("|")+")",ze+"+"+Ke+"(?="+[Fe,He+Qe,"$"].join("|")+")",He+"?"+Qe+"+"+qe,He+"+"+Ke,"\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])","\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",Be,Ve].join("|"),"g"),ot=RegExp("["+Ye+xe+Ie+Te+"]"),at=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/,it=["Array","Buffer","DataView","Date","Error","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Map","Math","Object","Promise","RegExp","Set","String","Symbol","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","WeakMap","_","clearTimeout","isFinite","parseInt","setTimeout"],ut=-1,lt={};lt[U]=lt[L]=lt[N]=lt[k]=lt[O]=lt[j]=lt[W]=lt[H]=lt[Y]=!0,lt[g]=lt[v]=lt[D]=lt[_]=lt[B]=lt[A]=lt[m]=lt[y]=lt[I]=lt[b]=lt[S]=lt[T]=lt[E]=lt[R]=lt[F]=!1;var ct={};ct[g]=ct[v]=ct[D]=ct[B]=ct[_]=ct[A]=ct[U]=ct[L]=ct[N]=ct[k]=ct[O]=ct[I]=ct[b]=ct[S]=ct[T]=ct[E]=ct[R]=ct[G]=ct[j]=ct[W]=ct[H]=ct[Y]=!0,ct[m]=ct[y]=ct[F]=!1;var st={"\\":"\\","'":"'","\n":"n","\r":"r","\u2028":"u2028","\u2029":"u2029"},ft=parseFloat,dt=parseInt,pt="object"==o(x)&&x&&x.Object===Object&&x,ht="object"==("undefined"==typeof self?"undefined":o(self))&&self&&self.Object===Object&&self,gt=pt||ht||Function("return this")(),vt=w&&!w.nodeType&&w,_t=vt&&M&&!M.nodeType&&M,At=_t&&_t.exports===vt,mt=At&&pt.process,yt=function(){try{var e=_t&&_t.require&&_t.require("util").types;return e||mt&&mt.binding&&mt.binding("util")}catch(e){}}(),Pt=yt&&yt.isArrayBuffer,Mt=yt&&yt.isDate,wt=yt&&yt.isMap,xt=yt&&yt.isRegExp,It=yt&&yt.isSet,bt=yt&&yt.isTypedArray;function St(e,t,n){switch(n.length){case 0:return e.call(t);case 1:return e.call(t,n[0]);case 2:return e.call(t,n[0],n[1]);case 3:return e.call(t,n[0],n[1],n[2])}return e.apply(t,n)}function Ct(e,t,n,r){for(var o=-1,a=null==e?0:e.length;++o<a;){var i=e[o];t(r,i,n(i),e)}return r}function Tt(e,t){for(var n=-1,r=null==e?0:e.length;++n<r&&!1!==t(e[n],n,e););return e}function Et(e,t){for(var n=null==e?0:e.length;n--&&!1!==t(e[n],n,e););return e}function Rt(e,t){for(var n=-1,r=null==e?0:e.length;++n<r;)if(!t(e[n],n,e))return!1;return!0}function Gt(e,t){for(var n=-1,r=null==e?0:e.length,o=0,a=[];++n<r;){var i=e[n];t(i,n,e)&&(a[o++]=i)}return a}function Ft(e,t){return!(null==e||!e.length)&&Ht(e,t,0)>-1}function Dt(e,t,n){for(var r=-1,o=null==e?0:e.length;++r<o;)if(n(t,e[r]))return!0;return!1}function Bt(e,t){for(var n=-1,r=null==e?0:e.length,o=Array(r);++n<r;)o[n]=t(e[n],n,e);return o}function Ut(e,t){for(var n=-1,r=t.length,o=e.length;++n<r;)e[o+n]=t[n];return e}function Lt(e,t,n,r){var o=-1,a=null==e?0:e.length;for(r&&a&&(n=e[++o]);++o<a;)n=t(n,e[o],o,e);return n}function Nt(e,t,n,r){var o=null==e?0:e.length;for(r&&o&&(n=e[--o]);o--;)n=t(n,e[o],o,e);return n}function kt(e,t){for(var n=-1,r=null==e?0:e.length;++n<r;)if(t(e[n],n,e))return!0;return!1}var Ot=qt("length");function jt(e,t,n){var r;return n(e,(function(e,n,o){if(t(e,n,o))return r=n,!1})),r}function Wt(e,t,n,r){for(var o=e.length,a=n+(r?1:-1);r?a--:++a<o;)if(t(e[a],a,e))return a;return-1}function Ht(e,t,n){return t==t?function(e,t,n){for(var r=n-1,o=e.length;++r<o;)if(e[r]===t)return r;return-1}(e,t,n):Wt(e,Qt,n)}function Yt(e,t,n,r){for(var o=n-1,a=e.length;++o<a;)if(r(e[o],t))return o;return-1}function Qt(e){return e!=e}function zt(e,t){var n=null==e?0:e.length;return n?Xt(e,t)/n:d}function qt(t){return function(n){return null==n?e:n[t]}}function Kt(t){return function(n){return null==t?e:t[n]}}function Jt(e,t,n,r,o){return o(e,(function(e,o,a){n=r?(r=!1,e):t(n,e,o,a)})),n}function Xt(t,n){for(var r,o=-1,a=t.length;++o<a;){var i=n(t[o]);i!==e&&(r=r===e?i:r+i)}return r}function Zt(e,t){for(var n=-1,r=Array(e);++n<e;)r[n]=t(n);return r}function Vt(e){return e?e.slice(0,vn(e)+1).replace(ie,""):e}function $t(e){return function(t){return e(t)}}function en(e,t){return Bt(t,(function(t){return e[t]}))}function tn(e,t){return e.has(t)}function nn(e,t){for(var n=-1,r=e.length;++n<r&&Ht(t,e[n],0)>-1;);return n}function rn(e,t){for(var n=e.length;n--&&Ht(t,e[n],0)>-1;);return n}var on=Kt({"À":"A","Á":"A","Â":"A","Ã":"A","Ä":"A","Å":"A","à":"a","á":"a","â":"a","ã":"a","ä":"a","å":"a","Ç":"C","ç":"c","Ð":"D","ð":"d","È":"E","É":"E","Ê":"E","Ë":"E","è":"e","é":"e","ê":"e","ë":"e","Ì":"I","Í":"I","Î":"I","Ï":"I","ì":"i","í":"i","î":"i","ï":"i","Ñ":"N","ñ":"n","Ò":"O","Ó":"O","Ô":"O","Õ":"O","Ö":"O","Ø":"O","ò":"o","ó":"o","ô":"o","õ":"o","ö":"o","ø":"o","Ù":"U","Ú":"U","Û":"U","Ü":"U","ù":"u","ú":"u","û":"u","ü":"u","Ý":"Y","ý":"y","ÿ":"y","Æ":"Ae","æ":"ae","Þ":"Th","þ":"th","ß":"ss","Ā":"A","Ă":"A","Ą":"A","ā":"a","ă":"a","ą":"a","Ć":"C","Ĉ":"C","Ċ":"C","Č":"C","ć":"c","ĉ":"c","ċ":"c","č":"c","Ď":"D","Đ":"D","ď":"d","đ":"d","Ē":"E","Ĕ":"E","Ė":"E","Ę":"E","Ě":"E","ē":"e","ĕ":"e","ė":"e","ę":"e","ě":"e","Ĝ":"G","Ğ":"G","Ġ":"G","Ģ":"G","ĝ":"g","ğ":"g","ġ":"g","ģ":"g","Ĥ":"H","Ħ":"H","ĥ":"h","ħ":"h","Ĩ":"I","Ī":"I","Ĭ":"I","Į":"I","İ":"I","ĩ":"i","ī":"i","ĭ":"i","į":"i","ı":"i","Ĵ":"J","ĵ":"j","Ķ":"K","ķ":"k","ĸ":"k","Ĺ":"L","Ļ":"L","Ľ":"L","Ŀ":"L","Ł":"L","ĺ":"l","ļ":"l","ľ":"l","ŀ":"l","ł":"l","Ń":"N","Ņ":"N","Ň":"N","Ŋ":"N","ń":"n","ņ":"n","ň":"n","ŋ":"n","Ō":"O","Ŏ":"O","Ő":"O","ō":"o","ŏ":"o","ő":"o","Ŕ":"R","Ŗ":"R","Ř":"R","ŕ":"r","ŗ":"r","ř":"r","Ś":"S","Ŝ":"S","Ş":"S","Š":"S","ś":"s","ŝ":"s","ş":"s","š":"s","Ţ":"T","Ť":"T","Ŧ":"T","ţ":"t","ť":"t","ŧ":"t","Ũ":"U","Ū":"U","Ŭ":"U","Ů":"U","Ű":"U","Ų":"U","ũ":"u","ū":"u","ŭ":"u","ů":"u","ű":"u","ų":"u","Ŵ":"W","ŵ":"w","Ŷ":"Y","ŷ":"y","Ÿ":"Y","Ź":"Z","Ż":"Z","Ž":"Z","ź":"z","ż":"z","ž":"z","Ĳ":"IJ","ĳ":"ij","Œ":"Oe","œ":"oe","ŉ":"'n","ſ":"s"}),an=Kt({"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"});function un(e){return"\\"+st[e]}function ln(e){return ot.test(e)}function cn(e){var t=-1,n=Array(e.size);return e.forEach((function(e,r){n[++t]=[r,e]})),n}function sn(e,t){return function(n){return e(t(n))}}function fn(e,t){for(var n=-1,o=e.length,a=0,i=[];++n<o;){var u=e[n];u!==t&&u!==r||(e[n]=r,i[a++]=n)}return i}function dn(e){var t=-1,n=Array(e.size);return e.forEach((function(e){n[++t]=e})),n}function pn(e){var t=-1,n=Array(e.size);return e.forEach((function(e){n[++t]=[e,e]})),n}function hn(e){return ln(e)?function(e){for(var t=nt.lastIndex=0;nt.test(e);)++t;return t}(e):Ot(e)}function gn(e){return ln(e)?function(e){return e.match(nt)||[]}(e):function(e){return e.split("")}(e)}function vn(e){for(var t=e.length;t--&&ue.test(e.charAt(t)););return t}var _n=Kt({"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"}),An=function M(w){var x,ue=(w=null==w?gt:An.defaults(gt.Object(),w,An.pick(gt,it))).Array,xe=w.Date,Ie=w.Error,be=w.Function,Se=w.Math,Ce=w.Object,Te=w.RegExp,Ee=w.String,Re=w.TypeError,Ge=ue.prototype,Fe=be.prototype,De=Ce.prototype,Be=w["__core-js_shared__"],Ue=Fe.toString,Le=De.hasOwnProperty,Ne=0,ke=(x=/[^.]+$/.exec(Be&&Be.keys&&Be.keys.IE_PROTO||""))?"Symbol(src)_1."+x:"",Oe=De.toString,je=Ue.call(Ce),We=gt._,He=Te("^"+Ue.call(Le).replace(oe,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),Ye=At?w.Buffer:e,Qe=w.Symbol,ze=w.Uint8Array,qe=Ye?Ye.allocUnsafe:e,Ke=sn(Ce.getPrototypeOf,Ce),Je=Ce.create,Xe=De.propertyIsEnumerable,Ze=Ge.splice,Ve=Qe?Qe.isConcatSpreadable:e,$e=Qe?Qe.iterator:e,nt=Qe?Qe.toStringTag:e,ot=function(){try{var e=ha(Ce,"defineProperty");return e({},"",{}),e}catch(e){}}(),st=w.clearTimeout!==gt.clearTimeout&&w.clearTimeout,pt=xe&&xe.now!==gt.Date.now&&xe.now,ht=w.setTimeout!==gt.setTimeout&&w.setTimeout,vt=Se.ceil,_t=Se.floor,mt=Ce.getOwnPropertySymbols,yt=Ye?Ye.isBuffer:e,Ot=w.isFinite,Kt=Ge.join,mn=sn(Ce.keys,Ce),yn=Se.max,Pn=Se.min,Mn=xe.now,wn=w.parseInt,xn=Se.random,In=Ge.reverse,bn=ha(w,"DataView"),Sn=ha(w,"Map"),Cn=ha(w,"Promise"),Tn=ha(w,"Set"),En=ha(w,"WeakMap"),Rn=ha(Ce,"create"),Gn=En&&new En,Fn={},Dn=Oa(bn),Bn=Oa(Sn),Un=Oa(Cn),Ln=Oa(Tn),Nn=Oa(En),kn=Qe?Qe.prototype:e,On=kn?kn.valueOf:e,jn=kn?kn.toString:e;function Wn(e){if(ru(e)&&!zi(e)&&!(e instanceof zn)){if(e instanceof Qn)return e;if(Le.call(e,"__wrapped__"))return ja(e)}return new Qn(e)}var Hn=function(){function t(){}return function(n){if(!nu(n))return{};if(Je)return Je(n);t.prototype=n;var r=new t;return t.prototype=e,r}}();function Yn(){}function Qn(t,n){this.__wrapped__=t,this.__actions__=[],this.__chain__=!!n,this.__index__=0,this.__values__=e}function zn(e){this.__wrapped__=e,this.__actions__=[],this.__dir__=1,this.__filtered__=!1,this.__iteratees__=[],this.__takeCount__=p,this.__views__=[]}function qn(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function Kn(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function Jn(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function Xn(e){var t=-1,n=null==e?0:e.length;for(this.__data__=new Jn;++t<n;)this.add(e[t])}function Zn(e){var t=this.__data__=new Kn(e);this.size=t.size}function Vn(e,t){var n=zi(e),r=!n&&Qi(e),o=!n&&!r&&Xi(e),a=!n&&!r&&!o&&fu(e),i=n||r||o||a,u=i?Zt(e.length,Ee):[],l=u.length;for(var c in e)!t&&!Le.call(e,c)||i&&("length"==c||o&&("offset"==c||"parent"==c)||a&&("buffer"==c||"byteLength"==c||"byteOffset"==c)||Pa(c,l))||u.push(c);return u}function $n(t){var n=t.length;return n?t[Xr(0,n-1)]:e}function er(e,t){return La(Go(e),cr(t,0,e.length))}function tr(e){return La(Go(e))}function nr(t,n,r){(r!==e&&!Wi(t[n],r)||r===e&&!(n in t))&&ur(t,n,r)}function rr(t,n,r){var o=t[n];Le.call(t,n)&&Wi(o,r)&&(r!==e||n in t)||ur(t,n,r)}function or(e,t){for(var n=e.length;n--;)if(Wi(e[n][0],t))return n;return-1}function ar(e,t,n,r){return hr(e,(function(e,o,a){t(r,e,n(e),a)})),r}function ir(e,t){return e&&Fo(t,Fu(t),e)}function ur(e,t,n){"__proto__"==t&&ot?ot(e,t,{configurable:!0,enumerable:!0,value:n,writable:!0}):e[t]=n}function lr(t,n){for(var r=-1,o=n.length,a=ue(o),i=null==t;++r<o;)a[r]=i?e:Cu(t,n[r]);return a}function cr(t,n,r){return t==t&&(r!==e&&(t=t<=r?t:r),n!==e&&(t=t>=n?t:n)),t}function sr(t,n,r,o,a,i){var u,l=1&n,c=2&n,s=4&n;if(r&&(u=a?r(t,o,a,i):r(t)),u!==e)return u;if(!nu(t))return t;var f=zi(t);if(f){if(u=function(e){var t=e.length,n=new e.constructor(t);return t&&"string"==typeof e[0]&&Le.call(e,"index")&&(n.index=e.index,n.input=e.input),n}(t),!l)return Go(t,u)}else{var d=_a(t),p=d==y||d==P;if(Xi(t))return bo(t,l);if(d==S||d==g||p&&!a){if(u=c||p?{}:ma(t),!l)return c?function(e,t){return Fo(e,va(e),t)}(t,function(e,t){return e&&Fo(t,Du(t),e)}(u,t)):function(e,t){return Fo(e,ga(e),t)}(t,ir(u,t))}else{if(!ct[d])return a?t:{};u=function(e,t,n){var r,o=e.constructor;switch(t){case D:return So(e);case _:case A:return new o(+e);case B:return function(e,t){var n=t?So(e.buffer):e.buffer;return new e.constructor(n,e.byteOffset,e.byteLength)}(e,n);case U:case L:case N:case k:case O:case j:case W:case H:case Y:return Co(e,n);case I:return new o;case b:case R:return new o(e);case T:return function(e){var t=new e.constructor(e.source,ge.exec(e));return t.lastIndex=e.lastIndex,t}(e);case E:return new o;case G:return r=e,On?Ce(On.call(r)):{}}}(t,d,l)}}i||(i=new Zn);var h=i.get(t);if(h)return h;i.set(t,u),lu(t)?t.forEach((function(e){u.add(sr(e,n,r,e,t,i))})):ou(t)&&t.forEach((function(e,o){u.set(o,sr(e,n,r,o,t,i))}));var v=f?e:(s?c?ua:ia:c?Du:Fu)(t);return Tt(v||t,(function(e,o){v&&(e=t[o=e]),rr(u,o,sr(e,n,r,o,t,i))})),u}function fr(t,n,r){var o=r.length;if(null==t)return!o;for(t=Ce(t);o--;){var a=r[o],i=n[a],u=t[a];if(u===e&&!(a in t)||!i(u))return!1}return!0}function dr(n,r,o){if("function"!=typeof n)throw new Re(t);return Fa((function(){n.apply(e,o)}),r)}function pr(e,t,n,r){var o=-1,a=Ft,i=!0,u=e.length,l=[],c=t.length;if(!u)return l;n&&(t=Bt(t,$t(n))),r?(a=Dt,i=!1):t.length>=200&&(a=tn,i=!1,t=new Xn(t));e:for(;++o<u;){var s=e[o],f=null==n?s:n(s);if(s=r||0!==s?s:0,i&&f==f){for(var d=c;d--;)if(t[d]===f)continue e;l.push(s)}else a(t,f,r)||l.push(s)}return l}Wn.templateSettings={escape:V,evaluate:$,interpolate:ee,variable:"",imports:{_:Wn}},Wn.prototype=Yn.prototype,Wn.prototype.constructor=Wn,Qn.prototype=Hn(Yn.prototype),Qn.prototype.constructor=Qn,zn.prototype=Hn(Yn.prototype),zn.prototype.constructor=zn,qn.prototype.clear=function(){this.__data__=Rn?Rn(null):{},this.size=0},qn.prototype.delete=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t},qn.prototype.get=function(t){var r=this.__data__;if(Rn){var o=r[t];return o===n?e:o}return Le.call(r,t)?r[t]:e},qn.prototype.has=function(t){var n=this.__data__;return Rn?n[t]!==e:Le.call(n,t)},qn.prototype.set=function(t,r){var o=this.__data__;return this.size+=this.has(t)?0:1,o[t]=Rn&&r===e?n:r,this},Kn.prototype.clear=function(){this.__data__=[],this.size=0},Kn.prototype.delete=function(e){var t=this.__data__,n=or(t,e);return!(n<0||(n==t.length-1?t.pop():Ze.call(t,n,1),--this.size,0))},Kn.prototype.get=function(t){var n=this.__data__,r=or(n,t);return r<0?e:n[r][1]},Kn.prototype.has=function(e){return or(this.__data__,e)>-1},Kn.prototype.set=function(e,t){var n=this.__data__,r=or(n,e);return r<0?(++this.size,n.push([e,t])):n[r][1]=t,this},Jn.prototype.clear=function(){this.size=0,this.__data__={hash:new qn,map:new(Sn||Kn),string:new qn}},Jn.prototype.delete=function(e){var t=da(this,e).delete(e);return this.size-=t?1:0,t},Jn.prototype.get=function(e){return da(this,e).get(e)},Jn.prototype.has=function(e){return da(this,e).has(e)},Jn.prototype.set=function(e,t){var n=da(this,e),r=n.size;return n.set(e,t),this.size+=n.size==r?0:1,this},Xn.prototype.add=Xn.prototype.push=function(e){return this.__data__.set(e,n),this},Xn.prototype.has=function(e){return this.__data__.has(e)},Zn.prototype.clear=function(){this.__data__=new Kn,this.size=0},Zn.prototype.delete=function(e){var t=this.__data__,n=t.delete(e);return this.size=t.size,n},Zn.prototype.get=function(e){return this.__data__.get(e)},Zn.prototype.has=function(e){return this.__data__.has(e)},Zn.prototype.set=function(e,t){var n=this.__data__;if(n instanceof Kn){var r=n.__data__;if(!Sn||r.length<199)return r.push([e,t]),this.size=++n.size,this;n=this.__data__=new Jn(r)}return n.set(e,t),this.size=n.size,this};var hr=Uo(Mr),gr=Uo(wr,!0);function vr(e,t){var n=!0;return hr(e,(function(e,r,o){return n=!!t(e,r,o)})),n}function _r(t,n,r){for(var o=-1,a=t.length;++o<a;){var i=t[o],u=n(i);if(null!=u&&(l===e?u==u&&!su(u):r(u,l)))var l=u,c=i}return c}function Ar(e,t){var n=[];return hr(e,(function(e,r,o){t(e,r,o)&&n.push(e)})),n}function mr(e,t,n,r,o){var a=-1,i=e.length;for(n||(n=ya),o||(o=[]);++a<i;){var u=e[a];t>0&&n(u)?t>1?mr(u,t-1,n,r,o):Ut(o,u):r||(o[o.length]=u)}return o}var yr=Lo(),Pr=Lo(!0);function Mr(e,t){return e&&yr(e,t,Fu)}function wr(e,t){return e&&Pr(e,t,Fu)}function xr(e,t){return Gt(t,(function(t){return $i(e[t])}))}function Ir(t,n){for(var r=0,o=(n=Mo(n,t)).length;null!=t&&r<o;)t=t[ka(n[r++])];return r&&r==o?t:e}function br(e,t,n){var r=t(e);return zi(e)?r:Ut(r,n(e))}function Sr(t){return null==t?t===e?"[object Undefined]":"[object Null]":nt&&nt in Ce(t)?function(t){var n=Le.call(t,nt),r=t[nt];try{t[nt]=e;var o=!0}catch(e){}var a=Oe.call(t);return o&&(n?t[nt]=r:delete t[nt]),a}(t):function(e){return Oe.call(e)}(t)}function Cr(e,t){return e>t}function Tr(e,t){return null!=e&&Le.call(e,t)}function Er(e,t){return null!=e&&t in Ce(e)}function Rr(t,n,r){for(var o=r?Dt:Ft,a=t[0].length,i=t.length,u=i,l=ue(i),c=1/0,s=[];u--;){var f=t[u];u&&n&&(f=Bt(f,$t(n))),c=Pn(f.length,c),l[u]=!r&&(n||a>=120&&f.length>=120)?new Xn(u&&f):e}f=t[0];var d=-1,p=l[0];e:for(;++d<a&&s.length<c;){var h=f[d],g=n?n(h):h;if(h=r||0!==h?h:0,!(p?tn(p,g):o(s,g,r))){for(u=i;--u;){var v=l[u];if(!(v?tn(v,g):o(t[u],g,r)))continue e}p&&p.push(g),s.push(h)}}return s}function Gr(t,n,r){var o=null==(t=Ea(t,n=Mo(n,t)))?t:t[ka(Va(n))];return null==o?e:St(o,t,r)}function Fr(e){return ru(e)&&Sr(e)==g}function Dr(t,n,r,o,a){return t===n||(null==t||null==n||!ru(t)&&!ru(n)?t!=t&&n!=n:function(t,n,r,o,a,i){var u=zi(t),l=zi(n),c=u?v:_a(t),s=l?v:_a(n),f=(c=c==g?S:c)==S,d=(s=s==g?S:s)==S,p=c==s;if(p&&Xi(t)){if(!Xi(n))return!1;u=!0,f=!1}if(p&&!f)return i||(i=new Zn),u||fu(t)?oa(t,n,r,o,a,i):function(e,t,n,r,o,a,i){switch(n){case B:if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case D:return!(e.byteLength!=t.byteLength||!a(new ze(e),new ze(t)));case _:case A:case b:return Wi(+e,+t);case m:return e.name==t.name&&e.message==t.message;case T:case R:return e==t+"";case I:var u=cn;case E:var l=1&r;if(u||(u=dn),e.size!=t.size&&!l)return!1;var c=i.get(e);if(c)return c==t;r|=2,i.set(e,t);var s=oa(u(e),u(t),r,o,a,i);return i.delete(e),s;case G:if(On)return On.call(e)==On.call(t)}return!1}(t,n,c,r,o,a,i);if(!(1&r)){var h=f&&Le.call(t,"__wrapped__"),y=d&&Le.call(n,"__wrapped__");if(h||y){var P=h?t.value():t,M=y?n.value():n;return i||(i=new Zn),a(P,M,r,o,i)}}return!!p&&(i||(i=new Zn),function(t,n,r,o,a,i){var u=1&r,l=ia(t),c=l.length,s=ia(n),f=s.length;if(c!=f&&!u)return!1;for(var d=c;d--;){var p=l[d];if(!(u?p in n:Le.call(n,p)))return!1}var h=i.get(t),g=i.get(n);if(h&&g)return h==n&&g==t;var v=!0;i.set(t,n),i.set(n,t);for(var _=u;++d<c;){var A=t[p=l[d]],m=n[p];if(o)var y=u?o(m,A,p,n,t,i):o(A,m,p,t,n,i);if(!(y===e?A===m||a(A,m,r,o,i):y)){v=!1;break}_||(_="constructor"==p)}if(v&&!_){var P=t.constructor,M=n.constructor;P==M||!("constructor"in t)||!("constructor"in n)||"function"==typeof P&&P instanceof P&&"function"==typeof M&&M instanceof M||(v=!1)}return i.delete(t),i.delete(n),v}(t,n,r,o,a,i))}(t,n,r,o,Dr,a))}function Br(t,n,r,o){var a=r.length,i=a,u=!o;if(null==t)return!i;for(t=Ce(t);a--;){var l=r[a];if(u&&l[2]?l[1]!==t[l[0]]:!(l[0]in t))return!1}for(;++a<i;){var c=(l=r[a])[0],s=t[c],f=l[1];if(u&&l[2]){if(s===e&&!(c in t))return!1}else{var d=new Zn;if(o)var p=o(s,f,c,t,n,d);if(!(p===e?Dr(f,s,3,o,d):p))return!1}}return!0}function Ur(e){return!(!nu(e)||(t=e,ke&&ke in t))&&($i(e)?He:Ae).test(Oa(e));var t}function Lr(e){return"function"==typeof e?e:null==e?al:"object"==o(e)?zi(e)?Hr(e[0],e[1]):Wr(e):hl(e)}function Nr(e){if(!ba(e))return mn(e);var t=[];for(var n in Ce(e))Le.call(e,n)&&"constructor"!=n&&t.push(n);return t}function kr(e){if(!nu(e))return function(e){var t=[];if(null!=e)for(var n in Ce(e))t.push(n);return t}(e);var t=ba(e),n=[];for(var r in e)("constructor"!=r||!t&&Le.call(e,r))&&n.push(r);return n}function Or(e,t){return e<t}function jr(e,t){var n=-1,r=Ki(e)?ue(e.length):[];return hr(e,(function(e,o,a){r[++n]=t(e,o,a)})),r}function Wr(e){var t=pa(e);return 1==t.length&&t[0][2]?Ca(t[0][0],t[0][1]):function(n){return n===e||Br(n,e,t)}}function Hr(t,n){return wa(t)&&Sa(n)?Ca(ka(t),n):function(r){var o=Cu(r,t);return o===e&&o===n?Tu(r,t):Dr(n,o,3)}}function Yr(t,n,r,o,a){t!==n&&yr(n,(function(i,u){if(a||(a=new Zn),nu(i))!function(t,n,r,o,a,i,u){var l=Ra(t,r),c=Ra(n,r),s=u.get(c);if(s)nr(t,r,s);else{var f=i?i(l,c,r+"",t,n,u):e,d=f===e;if(d){var p=zi(c),h=!p&&Xi(c),g=!p&&!h&&fu(c);f=c,p||h||g?zi(l)?f=l:Ji(l)?f=Go(l):h?(d=!1,f=bo(c,!0)):g?(d=!1,f=Co(c,!0)):f=[]:iu(c)||Qi(c)?(f=l,Qi(l)?f=mu(l):nu(l)&&!$i(l)||(f=ma(c))):d=!1}d&&(u.set(c,f),a(f,c,o,i,u),u.delete(c)),nr(t,r,f)}}(t,n,u,r,Yr,o,a);else{var l=o?o(Ra(t,u),i,u+"",t,n,a):e;l===e&&(l=i),nr(t,u,l)}}),Du)}function Qr(t,n){var r=t.length;if(r)return Pa(n+=n<0?r:0,r)?t[n]:e}function zr(e,t,n){t=t.length?Bt(t,(function(e){return zi(e)?function(t){return Ir(t,1===e.length?e[0]:e)}:e})):[al];var r=-1;t=Bt(t,$t(fa()));var o=jr(e,(function(e,n,o){var a=Bt(t,(function(t){return t(e)}));return{criteria:a,index:++r,value:e}}));return function(e,t){var n=e.length;for(e.sort(t);n--;)e[n]=e[n].value;return e}(o,(function(e,t){return function(e,t,n){for(var r=-1,o=e.criteria,a=t.criteria,i=o.length,u=n.length;++r<i;){var l=To(o[r],a[r]);if(l)return r>=u?l:l*("desc"==n[r]?-1:1)}return e.index-t.index}(e,t,n)}))}function qr(e,t,n){for(var r=-1,o=t.length,a={};++r<o;){var i=t[r],u=Ir(e,i);n(u,i)&&to(a,Mo(i,e),u)}return a}function Kr(e,t,n,r){var o=r?Yt:Ht,a=-1,i=t.length,u=e;for(e===t&&(t=Go(t)),n&&(u=Bt(e,$t(n)));++a<i;)for(var l=0,c=t[a],s=n?n(c):c;(l=o(u,s,l,r))>-1;)u!==e&&Ze.call(u,l,1),Ze.call(e,l,1);return e}function Jr(e,t){for(var n=e?t.length:0,r=n-1;n--;){var o=t[n];if(n==r||o!==a){var a=o;Pa(o)?Ze.call(e,o,1):ho(e,o)}}return e}function Xr(e,t){return e+_t(xn()*(t-e+1))}function Zr(e,t){var n="";if(!e||t<1||t>f)return n;do{t%2&&(n+=e),(t=_t(t/2))&&(e+=e)}while(t);return n}function Vr(e,t){return Da(Ta(e,t,al),e+"")}function $r(e){return $n(Wu(e))}function eo(e,t){var n=Wu(e);return La(n,cr(t,0,n.length))}function to(t,n,r,o){if(!nu(t))return t;for(var a=-1,i=(n=Mo(n,t)).length,u=i-1,l=t;null!=l&&++a<i;){var c=ka(n[a]),s=r;if("__proto__"===c||"constructor"===c||"prototype"===c)return t;if(a!=u){var f=l[c];(s=o?o(f,c,l):e)===e&&(s=nu(f)?f:Pa(n[a+1])?[]:{})}rr(l,c,s),l=l[c]}return t}var no=Gn?function(e,t){return Gn.set(e,t),e}:al,ro=ot?function(e,t){return ot(e,"toString",{configurable:!0,enumerable:!1,value:nl(t),writable:!0})}:al;function oo(e){return La(Wu(e))}function ao(e,t,n){var r=-1,o=e.length;t<0&&(t=-t>o?0:o+t),(n=n>o?o:n)<0&&(n+=o),o=t>n?0:n-t>>>0,t>>>=0;for(var a=ue(o);++r<o;)a[r]=e[r+t];return a}function io(e,t){var n;return hr(e,(function(e,r,o){return!(n=t(e,r,o))})),!!n}function uo(e,t,n){var r=0,o=null==e?r:e.length;if("number"==typeof t&&t==t&&o<=2147483647){for(;r<o;){var a=r+o>>>1,i=e[a];null!==i&&!su(i)&&(n?i<=t:i<t)?r=a+1:o=a}return o}return lo(e,t,al,n)}function lo(t,n,r,o){var a=0,i=null==t?0:t.length;if(0===i)return 0;for(var u=(n=r(n))!=n,l=null===n,c=su(n),s=n===e;a<i;){var f=_t((a+i)/2),d=r(t[f]),p=d!==e,h=null===d,g=d==d,v=su(d);if(u)var _=o||g;else _=s?g&&(o||p):l?g&&p&&(o||!h):c?g&&p&&!h&&(o||!v):!h&&!v&&(o?d<=n:d<n);_?a=f+1:i=f}return Pn(i,4294967294)}function co(e,t){for(var n=-1,r=e.length,o=0,a=[];++n<r;){var i=e[n],u=t?t(i):i;if(!n||!Wi(u,l)){var l=u;a[o++]=0===i?0:i}}return a}function so(e){return"number"==typeof e?e:su(e)?d:+e}function fo(e){if("string"==typeof e)return e;if(zi(e))return Bt(e,fo)+"";if(su(e))return jn?jn.call(e):"";var t=e+"";return"0"==t&&1/e==-1/0?"-0":t}function po(e,t,n){var r=-1,o=Ft,a=e.length,i=!0,u=[],l=u;if(n)i=!1,o=Dt;else if(a>=200){var c=t?null:Vo(e);if(c)return dn(c);i=!1,o=tn,l=new Xn}else l=t?[]:u;e:for(;++r<a;){var s=e[r],f=t?t(s):s;if(s=n||0!==s?s:0,i&&f==f){for(var d=l.length;d--;)if(l[d]===f)continue e;t&&l.push(f),u.push(s)}else o(l,f,n)||(l!==u&&l.push(f),u.push(s))}return u}function ho(e,t){return null==(e=Ea(e,t=Mo(t,e)))||delete e[ka(Va(t))]}function go(e,t,n,r){return to(e,t,n(Ir(e,t)),r)}function vo(e,t,n,r){for(var o=e.length,a=r?o:-1;(r?a--:++a<o)&&t(e[a],a,e););return n?ao(e,r?0:a,r?a+1:o):ao(e,r?a+1:0,r?o:a)}function _o(e,t){var n=e;return n instanceof zn&&(n=n.value()),Lt(t,(function(e,t){return t.func.apply(t.thisArg,Ut([e],t.args))}),n)}function Ao(e,t,n){var r=e.length;if(r<2)return r?po(e[0]):[];for(var o=-1,a=ue(r);++o<r;)for(var i=e[o],u=-1;++u<r;)u!=o&&(a[o]=pr(a[o]||i,e[u],t,n));return po(mr(a,1),t,n)}function mo(t,n,r){for(var o=-1,a=t.length,i=n.length,u={};++o<a;){var l=o<i?n[o]:e;r(u,t[o],l)}return u}function yo(e){return Ji(e)?e:[]}function Po(e){return"function"==typeof e?e:al}function Mo(e,t){return zi(e)?e:wa(e,t)?[e]:Na(yu(e))}var wo=Vr;function xo(t,n,r){var o=t.length;return r=r===e?o:r,!n&&r>=o?t:ao(t,n,r)}var Io=st||function(e){return gt.clearTimeout(e)};function bo(e,t){if(t)return e.slice();var n=e.length,r=qe?qe(n):new e.constructor(n);return e.copy(r),r}function So(e){var t=new e.constructor(e.byteLength);return new ze(t).set(new ze(e)),t}function Co(e,t){var n=t?So(e.buffer):e.buffer;return new e.constructor(n,e.byteOffset,e.length)}function To(t,n){if(t!==n){var r=t!==e,o=null===t,a=t==t,i=su(t),u=n!==e,l=null===n,c=n==n,s=su(n);if(!l&&!s&&!i&&t>n||i&&u&&c&&!l&&!s||o&&u&&c||!r&&c||!a)return 1;if(!o&&!i&&!s&&t<n||s&&r&&a&&!o&&!i||l&&r&&a||!u&&a||!c)return-1}return 0}function Eo(e,t,n,r){for(var o=-1,a=e.length,i=n.length,u=-1,l=t.length,c=yn(a-i,0),s=ue(l+c),f=!r;++u<l;)s[u]=t[u];for(;++o<i;)(f||o<a)&&(s[n[o]]=e[o]);for(;c--;)s[u++]=e[o++];return s}function Ro(e,t,n,r){for(var o=-1,a=e.length,i=-1,u=n.length,l=-1,c=t.length,s=yn(a-u,0),f=ue(s+c),d=!r;++o<s;)f[o]=e[o];for(var p=o;++l<c;)f[p+l]=t[l];for(;++i<u;)(d||o<a)&&(f[p+n[i]]=e[o++]);return f}function Go(e,t){var n=-1,r=e.length;for(t||(t=ue(r));++n<r;)t[n]=e[n];return t}function Fo(t,n,r,o){var a=!r;r||(r={});for(var i=-1,u=n.length;++i<u;){var l=n[i],c=o?o(r[l],t[l],l,r,t):e;c===e&&(c=t[l]),a?ur(r,l,c):rr(r,l,c)}return r}function Do(e,t){return function(n,r){var o=zi(n)?Ct:ar,a=t?t():{};return o(n,e,fa(r,2),a)}}function Bo(t){return Vr((function(n,r){var o=-1,a=r.length,i=a>1?r[a-1]:e,u=a>2?r[2]:e;for(i=t.length>3&&"function"==typeof i?(a--,i):e,u&&Ma(r[0],r[1],u)&&(i=a<3?e:i,a=1),n=Ce(n);++o<a;){var l=r[o];l&&t(n,l,o,i)}return n}))}function Uo(e,t){return function(n,r){if(null==n)return n;if(!Ki(n))return e(n,r);for(var o=n.length,a=t?o:-1,i=Ce(n);(t?a--:++a<o)&&!1!==r(i[a],a,i););return n}}function Lo(e){return function(t,n,r){for(var o=-1,a=Ce(t),i=r(t),u=i.length;u--;){var l=i[e?u:++o];if(!1===n(a[l],l,a))break}return t}}function No(t){return function(n){var r=ln(n=yu(n))?gn(n):e,o=r?r[0]:n.charAt(0),a=r?xo(r,1).join(""):n.slice(1);return o[t]()+a}}function ko(e){return function(t){return Lt($u(Qu(t).replace(et,"")),e,"")}}function Oo(e){return function(){var t=arguments;switch(t.length){case 0:return new e;case 1:return new e(t[0]);case 2:return new e(t[0],t[1]);case 3:return new e(t[0],t[1],t[2]);case 4:return new e(t[0],t[1],t[2],t[3]);case 5:return new e(t[0],t[1],t[2],t[3],t[4]);case 6:return new e(t[0],t[1],t[2],t[3],t[4],t[5]);case 7:return new e(t[0],t[1],t[2],t[3],t[4],t[5],t[6])}var n=Hn(e.prototype),r=e.apply(n,t);return nu(r)?r:n}}function jo(t){return function(n,r,o){var a=Ce(n);if(!Ki(n)){var i=fa(r,3);n=Fu(n),r=function(e){return i(a[e],e,a)}}var u=t(n,r,o);return u>-1?a[i?n[u]:u]:e}}function Wo(n){return aa((function(r){var o=r.length,a=o,i=Qn.prototype.thru;for(n&&r.reverse();a--;){var u=r[a];if("function"!=typeof u)throw new Re(t);if(i&&!l&&"wrapper"==ca(u))var l=new Qn([],!0)}for(a=l?a:o;++a<o;){var c=ca(u=r[a]),s="wrapper"==c?la(u):e;l=s&&xa(s[0])&&424==s[1]&&!s[4].length&&1==s[9]?l[ca(s[0])].apply(l,s[3]):1==u.length&&xa(u)?l[c]():l.thru(u)}return function(){var e=arguments,t=e[0];if(l&&1==e.length&&zi(t))return l.plant(t).value();for(var n=0,a=o?r[n].apply(this,e):t;++n<o;)a=r[n].call(this,a);return a}}))}function Ho(t,n,r,o,a,i,u,c,s,f){var d=n&l,p=1&n,h=2&n,g=24&n,v=512&n,_=h?e:Oo(t);return function l(){for(var A=arguments.length,m=ue(A),y=A;y--;)m[y]=arguments[y];if(g)var P=sa(l),M=function(e,t){for(var n=e.length,r=0;n--;)e[n]===t&&++r;return r}(m,P);if(o&&(m=Eo(m,o,a,g)),i&&(m=Ro(m,i,u,g)),A-=M,g&&A<f){var w=fn(m,P);return Xo(t,n,Ho,l.placeholder,r,m,w,c,s,f-A)}var x=p?r:this,I=h?x[t]:t;return A=m.length,c?m=function(t,n){for(var r=t.length,o=Pn(n.length,r),a=Go(t);o--;){var i=n[o];t[o]=Pa(i,r)?a[i]:e}return t}(m,c):v&&A>1&&m.reverse(),d&&s<A&&(m.length=s),this&&this!==gt&&this instanceof l&&(I=_||Oo(I)),I.apply(x,m)}}function Yo(e,t){return function(n,r){return function(e,t,n,r){return Mr(e,(function(e,o,a){t(r,n(e),o,a)})),r}(n,e,t(r),{})}}function Qo(t,n){return function(r,o){var a;if(r===e&&o===e)return n;if(r!==e&&(a=r),o!==e){if(a===e)return o;"string"==typeof r||"string"==typeof o?(r=fo(r),o=fo(o)):(r=so(r),o=so(o)),a=t(r,o)}return a}}function zo(e){return aa((function(t){return t=Bt(t,$t(fa())),Vr((function(n){var r=this;return e(t,(function(e){return St(e,r,n)}))}))}))}function qo(t,n){var r=(n=n===e?" ":fo(n)).length;if(r<2)return r?Zr(n,t):n;var o=Zr(n,vt(t/hn(n)));return ln(n)?xo(gn(o),0,t).join(""):o.slice(0,t)}function Ko(t){return function(n,r,o){return o&&"number"!=typeof o&&Ma(n,r,o)&&(r=o=e),n=gu(n),r===e?(r=n,n=0):r=gu(r),function(e,t,n,r){for(var o=-1,a=yn(vt((t-e)/(n||1)),0),i=ue(a);a--;)i[r?a:++o]=e,e+=n;return i}(n,r,o=o===e?n<r?1:-1:gu(o),t)}}function Jo(e){return function(t,n){return"string"==typeof t&&"string"==typeof n||(t=Au(t),n=Au(n)),e(t,n)}}function Xo(t,n,r,o,a,l,c,s,f,d){var p=8&n;n|=p?i:u,4&(n&=~(p?u:i))||(n&=-4);var h=[t,n,a,p?l:e,p?c:e,p?e:l,p?e:c,s,f,d],g=r.apply(e,h);return xa(t)&&Ga(g,h),g.placeholder=o,Ba(g,t,n)}function Zo(e){var t=Se[e];return function(e,n){if(e=Au(e),(n=null==n?0:Pn(vu(n),292))&&Ot(e)){var r=(yu(e)+"e").split("e");return+((r=(yu(t(r[0]+"e"+(+r[1]+n)))+"e").split("e"))[0]+"e"+(+r[1]-n))}return t(e)}}var Vo=Tn&&1/dn(new Tn([,-0]))[1]==s?function(e){return new Tn(e)}:sl;function $o(e){return function(t){var n=_a(t);return n==I?cn(t):n==E?pn(t):function(e,t){return Bt(t,(function(t){return[t,e[t]]}))}(t,e(t))}}function ea(n,o,s,f,d,p,h,g){var v=2&o;if(!v&&"function"!=typeof n)throw new Re(t);var _=f?f.length:0;if(_||(o&=-97,f=d=e),h=h===e?h:yn(vu(h),0),g=g===e?g:vu(g),_-=d?d.length:0,o&u){var A=f,m=d;f=d=e}var y=v?e:la(n),P=[n,o,s,f,d,A,m,p,h,g];if(y&&function(e,t){var n=e[1],o=t[1],a=n|o,i=a<131,u=o==l&&8==n||o==l&&n==c&&e[7].length<=t[8]||384==o&&t[7].length<=t[8]&&8==n;if(!i&&!u)return e;1&o&&(e[2]=t[2],a|=1&n?0:4);var s=t[3];if(s){var f=e[3];e[3]=f?Eo(f,s,t[4]):s,e[4]=f?fn(e[3],r):t[4]}(s=t[5])&&(f=e[5],e[5]=f?Ro(f,s,t[6]):s,e[6]=f?fn(e[5],r):t[6]),(s=t[7])&&(e[7]=s),o&l&&(e[8]=null==e[8]?t[8]:Pn(e[8],t[8])),null==e[9]&&(e[9]=t[9]),e[0]=t[0],e[1]=a}(P,y),n=P[0],o=P[1],s=P[2],f=P[3],d=P[4],!(g=P[9]=P[9]===e?v?0:n.length:yn(P[9]-_,0))&&24&o&&(o&=-25),o&&1!=o)M=8==o||o==a?function(t,n,r){var o=Oo(t);return function a(){for(var i=arguments.length,u=ue(i),l=i,c=sa(a);l--;)u[l]=arguments[l];var s=i<3&&u[0]!==c&&u[i-1]!==c?[]:fn(u,c);return(i-=s.length)<r?Xo(t,n,Ho,a.placeholder,e,u,s,e,e,r-i):St(this&&this!==gt&&this instanceof a?o:t,this,u)}}(n,o,g):o!=i&&33!=o||d.length?Ho.apply(e,P):function(e,t,n,r){var o=1&t,a=Oo(e);return function t(){for(var i=-1,u=arguments.length,l=-1,c=r.length,s=ue(c+u),f=this&&this!==gt&&this instanceof t?a:e;++l<c;)s[l]=r[l];for(;u--;)s[l++]=arguments[++i];return St(f,o?n:this,s)}}(n,o,s,f);else var M=function(e,t,n){var r=1&t,o=Oo(e);return function t(){return(this&&this!==gt&&this instanceof t?o:e).apply(r?n:this,arguments)}}(n,o,s);return Ba((y?no:Ga)(M,P),n,o)}function ta(t,n,r,o){return t===e||Wi(t,De[r])&&!Le.call(o,r)?n:t}function na(t,n,r,o,a,i){return nu(t)&&nu(n)&&(i.set(n,t),Yr(t,n,e,na,i),i.delete(n)),t}function ra(t){return iu(t)?e:t}function oa(t,n,r,o,a,i){var u=1&r,l=t.length,c=n.length;if(l!=c&&!(u&&c>l))return!1;var s=i.get(t),f=i.get(n);if(s&&f)return s==n&&f==t;var d=-1,p=!0,h=2&r?new Xn:e;for(i.set(t,n),i.set(n,t);++d<l;){var g=t[d],v=n[d];if(o)var _=u?o(v,g,d,n,t,i):o(g,v,d,t,n,i);if(_!==e){if(_)continue;p=!1;break}if(h){if(!kt(n,(function(e,t){if(!tn(h,t)&&(g===e||a(g,e,r,o,i)))return h.push(t)}))){p=!1;break}}else if(g!==v&&!a(g,v,r,o,i)){p=!1;break}}return i.delete(t),i.delete(n),p}function aa(t){return Da(Ta(t,e,qa),t+"")}function ia(e){return br(e,Fu,ga)}function ua(e){return br(e,Du,va)}var la=Gn?function(e){return Gn.get(e)}:sl;function ca(e){for(var t=e.name+"",n=Fn[t],r=Le.call(Fn,t)?n.length:0;r--;){var o=n[r],a=o.func;if(null==a||a==e)return o.name}return t}function sa(e){return(Le.call(Wn,"placeholder")?Wn:e).placeholder}function fa(){var e=Wn.iteratee||il;return e=e===il?Lr:e,arguments.length?e(arguments[0],arguments[1]):e}function da(e,t){var n,r,a=e.__data__;return("string"==(r=o(n=t))||"number"==r||"symbol"==r||"boolean"==r?"__proto__"!==n:null===n)?a["string"==typeof t?"string":"hash"]:a.map}function pa(e){for(var t=Fu(e),n=t.length;n--;){var r=t[n],o=e[r];t[n]=[r,o,Sa(o)]}return t}function ha(t,n){var r=function(t,n){return null==t?e:t[n]}(t,n);return Ur(r)?r:e}var ga=mt?function(e){return null==e?[]:(e=Ce(e),Gt(mt(e),(function(t){return Xe.call(e,t)})))}:_l,va=mt?function(e){for(var t=[];e;)Ut(t,ga(e)),e=Ke(e);return t}:_l,_a=Sr;function Aa(e,t,n){for(var r=-1,o=(t=Mo(t,e)).length,a=!1;++r<o;){var i=ka(t[r]);if(!(a=null!=e&&n(e,i)))break;e=e[i]}return a||++r!=o?a:!!(o=null==e?0:e.length)&&tu(o)&&Pa(i,o)&&(zi(e)||Qi(e))}function ma(e){return"function"!=typeof e.constructor||ba(e)?{}:Hn(Ke(e))}function ya(e){return zi(e)||Qi(e)||!!(Ve&&e&&e[Ve])}function Pa(e,t){var n=o(e);return!!(t=null==t?f:t)&&("number"==n||"symbol"!=n&&ye.test(e))&&e>-1&&e%1==0&&e<t}function Ma(e,t,n){if(!nu(n))return!1;var r=o(t);return!!("number"==r?Ki(n)&&Pa(t,n.length):"string"==r&&t in n)&&Wi(n[t],e)}function wa(e,t){if(zi(e))return!1;var n=o(e);return!("number"!=n&&"symbol"!=n&&"boolean"!=n&&null!=e&&!su(e))||ne.test(e)||!te.test(e)||null!=t&&e in Ce(t)}function xa(e){var t=ca(e),n=Wn[t];if("function"!=typeof n||!(t in zn.prototype))return!1;if(e===n)return!0;var r=la(n);return!!r&&e===r[0]}(bn&&_a(new bn(new ArrayBuffer(1)))!=B||Sn&&_a(new Sn)!=I||Cn&&_a(Cn.resolve())!=C||Tn&&_a(new Tn)!=E||En&&_a(new En)!=F)&&(_a=function(t){var n=Sr(t),r=n==S?t.constructor:e,o=r?Oa(r):"";if(o)switch(o){case Dn:return B;case Bn:return I;case Un:return C;case Ln:return E;case Nn:return F}return n});var Ia=Be?$i:Al;function ba(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||De)}function Sa(e){return e==e&&!nu(e)}function Ca(t,n){return function(r){return null!=r&&r[t]===n&&(n!==e||t in Ce(r))}}function Ta(t,n,r){return n=yn(n===e?t.length-1:n,0),function(){for(var e=arguments,o=-1,a=yn(e.length-n,0),i=ue(a);++o<a;)i[o]=e[n+o];o=-1;for(var u=ue(n+1);++o<n;)u[o]=e[o];return u[n]=r(i),St(t,this,u)}}function Ea(e,t){return t.length<2?e:Ir(e,ao(t,0,-1))}function Ra(e,t){if(("constructor"!==t||"function"!=typeof e[t])&&"__proto__"!=t)return e[t]}var Ga=Ua(no),Fa=ht||function(e,t){return gt.setTimeout(e,t)},Da=Ua(ro);function Ba(e,t,n){var r=t+"";return Da(e,function(e,t){var n=t.length;if(!n)return e;var r=n-1;return t[r]=(n>1?"& ":"")+t[r],t=t.join(n>2?", ":" "),e.replace(le,"{\n/* [wrapped with "+t+"] */\n")}(r,function(e,t){return Tt(h,(function(n){var r="_."+n[0];t&n[1]&&!Ft(e,r)&&e.push(r)})),e.sort()}(function(e){var t=e.match(ce);return t?t[1].split(se):[]}(r),n)))}function Ua(t){var n=0,r=0;return function(){var o=Mn(),a=16-(o-r);if(r=o,a>0){if(++n>=800)return arguments[0]}else n=0;return t.apply(e,arguments)}}function La(t,n){var r=-1,o=t.length,a=o-1;for(n=n===e?o:n;++r<n;){var i=Xr(r,a),u=t[i];t[i]=t[r],t[r]=u}return t.length=n,t}var Na=function(e){var t=Ui(e,(function(e){return 500===n.size&&n.clear(),e})),n=t.cache;return t}((function(e){var t=[];return 46===e.charCodeAt(0)&&t.push(""),e.replace(re,(function(e,n,r,o){t.push(r?o.replace(pe,"$1"):n||e)})),t}));function ka(e){if("string"==typeof e||su(e))return e;var t=e+"";return"0"==t&&1/e==-1/0?"-0":t}function Oa(e){if(null!=e){try{return Ue.call(e)}catch(e){}try{return e+""}catch(e){}}return""}function ja(e){if(e instanceof zn)return e.clone();var t=new Qn(e.__wrapped__,e.__chain__);return t.__actions__=Go(e.__actions__),t.__index__=e.__index__,t.__values__=e.__values__,t}var Wa=Vr((function(e,t){return Ji(e)?pr(e,mr(t,1,Ji,!0)):[]})),Ha=Vr((function(t,n){var r=Va(n);return Ji(r)&&(r=e),Ji(t)?pr(t,mr(n,1,Ji,!0),fa(r,2)):[]})),Ya=Vr((function(t,n){var r=Va(n);return Ji(r)&&(r=e),Ji(t)?pr(t,mr(n,1,Ji,!0),e,r):[]}));function Qa(e,t,n){var r=null==e?0:e.length;if(!r)return-1;var o=null==n?0:vu(n);return o<0&&(o=yn(r+o,0)),Wt(e,fa(t,3),o)}function za(t,n,r){var o=null==t?0:t.length;if(!o)return-1;var a=o-1;return r!==e&&(a=vu(r),a=r<0?yn(o+a,0):Pn(a,o-1)),Wt(t,fa(n,3),a,!0)}function qa(e){return null!=e&&e.length?mr(e,1):[]}function Ka(t){return t&&t.length?t[0]:e}var Ja=Vr((function(e){var t=Bt(e,yo);return t.length&&t[0]===e[0]?Rr(t):[]})),Xa=Vr((function(t){var n=Va(t),r=Bt(t,yo);return n===Va(r)?n=e:r.pop(),r.length&&r[0]===t[0]?Rr(r,fa(n,2)):[]})),Za=Vr((function(t){var n=Va(t),r=Bt(t,yo);return(n="function"==typeof n?n:e)&&r.pop(),r.length&&r[0]===t[0]?Rr(r,e,n):[]}));function Va(t){var n=null==t?0:t.length;return n?t[n-1]:e}var $a=Vr(ei);function ei(e,t){return e&&e.length&&t&&t.length?Kr(e,t):e}var ti=aa((function(e,t){var n=null==e?0:e.length,r=lr(e,t);return Jr(e,Bt(t,(function(e){return Pa(e,n)?+e:e})).sort(To)),r}));function ni(e){return null==e?e:In.call(e)}var ri=Vr((function(e){return po(mr(e,1,Ji,!0))})),oi=Vr((function(t){var n=Va(t);return Ji(n)&&(n=e),po(mr(t,1,Ji,!0),fa(n,2))})),ai=Vr((function(t){var n=Va(t);return n="function"==typeof n?n:e,po(mr(t,1,Ji,!0),e,n)}));function ii(e){if(!e||!e.length)return[];var t=0;return e=Gt(e,(function(e){if(Ji(e))return t=yn(e.length,t),!0})),Zt(t,(function(t){return Bt(e,qt(t))}))}function ui(t,n){if(!t||!t.length)return[];var r=ii(t);return null==n?r:Bt(r,(function(t){return St(n,e,t)}))}var li=Vr((function(e,t){return Ji(e)?pr(e,t):[]})),ci=Vr((function(e){return Ao(Gt(e,Ji))})),si=Vr((function(t){var n=Va(t);return Ji(n)&&(n=e),Ao(Gt(t,Ji),fa(n,2))})),fi=Vr((function(t){var n=Va(t);return n="function"==typeof n?n:e,Ao(Gt(t,Ji),e,n)})),di=Vr(ii),pi=Vr((function(t){var n=t.length,r=n>1?t[n-1]:e;return r="function"==typeof r?(t.pop(),r):e,ui(t,r)}));function hi(e){var t=Wn(e);return t.__chain__=!0,t}function gi(e,t){return t(e)}var vi=aa((function(t){var n=t.length,r=n?t[0]:0,o=this.__wrapped__,a=function(e){return lr(e,t)};return!(n>1||this.__actions__.length)&&o instanceof zn&&Pa(r)?((o=o.slice(r,+r+(n?1:0))).__actions__.push({func:gi,args:[a],thisArg:e}),new Qn(o,this.__chain__).thru((function(t){return n&&!t.length&&t.push(e),t}))):this.thru(a)})),_i=Do((function(e,t,n){Le.call(e,n)?++e[n]:ur(e,n,1)})),Ai=jo(Qa),mi=jo(za);function yi(e,t){return(zi(e)?Tt:hr)(e,fa(t,3))}function Pi(e,t){return(zi(e)?Et:gr)(e,fa(t,3))}var Mi=Do((function(e,t,n){Le.call(e,n)?e[n].push(t):ur(e,n,[t])})),wi=Vr((function(e,t,n){var r=-1,o="function"==typeof t,a=Ki(e)?ue(e.length):[];return hr(e,(function(e){a[++r]=o?St(t,e,n):Gr(e,t,n)})),a})),xi=Do((function(e,t,n){ur(e,n,t)}));function Ii(e,t){return(zi(e)?Bt:jr)(e,fa(t,3))}var bi=Do((function(e,t,n){e[n?0:1].push(t)}),(function(){return[[],[]]})),Si=Vr((function(e,t){if(null==e)return[];var n=t.length;return n>1&&Ma(e,t[0],t[1])?t=[]:n>2&&Ma(t[0],t[1],t[2])&&(t=[t[0]]),zr(e,mr(t,1),[])})),Ci=pt||function(){return gt.Date.now()};function Ti(t,n,r){return n=r?e:n,n=t&&null==n?t.length:n,ea(t,l,e,e,e,e,n)}function Ei(n,r){var o;if("function"!=typeof r)throw new Re(t);return n=vu(n),function(){return--n>0&&(o=r.apply(this,arguments)),n<=1&&(r=e),o}}var Ri=Vr((function(e,t,n){var r=1;if(n.length){var o=fn(n,sa(Ri));r|=i}return ea(e,r,t,n,o)})),Gi=Vr((function(e,t,n){var r=3;if(n.length){var o=fn(n,sa(Gi));r|=i}return ea(t,r,e,n,o)}));function Fi(n,r,o){var a,i,u,l,c,s,f=0,d=!1,p=!1,h=!0;if("function"!=typeof n)throw new Re(t);function g(t){var r=a,o=i;return a=i=e,f=t,l=n.apply(o,r)}function v(t){var n=t-s;return s===e||n>=r||n<0||p&&t-f>=u}function _(){var e=Ci();if(v(e))return A(e);c=Fa(_,function(e){var t=r-(e-s);return p?Pn(t,u-(e-f)):t}(e))}function A(t){return c=e,h&&a?g(t):(a=i=e,l)}function m(){var t=Ci(),n=v(t);if(a=arguments,i=this,s=t,n){if(c===e)return function(e){return f=e,c=Fa(_,r),d?g(e):l}(s);if(p)return Io(c),c=Fa(_,r),g(s)}return c===e&&(c=Fa(_,r)),l}return r=Au(r)||0,nu(o)&&(d=!!o.leading,u=(p="maxWait"in o)?yn(Au(o.maxWait)||0,r):u,h="trailing"in o?!!o.trailing:h),m.cancel=function(){c!==e&&Io(c),f=0,a=s=i=c=e},m.flush=function(){return c===e?l:A(Ci())},m}var Di=Vr((function(e,t){return dr(e,1,t)})),Bi=Vr((function(e,t,n){return dr(e,Au(t)||0,n)}));function Ui(e,n){if("function"!=typeof e||null!=n&&"function"!=typeof n)throw new Re(t);var r=function t(){var r=arguments,o=n?n.apply(this,r):r[0],a=t.cache;if(a.has(o))return a.get(o);var i=e.apply(this,r);return t.cache=a.set(o,i)||a,i};return r.cache=new(Ui.Cache||Jn),r}function Li(e){if("function"!=typeof e)throw new Re(t);return function(){var t=arguments;switch(t.length){case 0:return!e.call(this);case 1:return!e.call(this,t[0]);case 2:return!e.call(this,t[0],t[1]);case 3:return!e.call(this,t[0],t[1],t[2])}return!e.apply(this,t)}}Ui.Cache=Jn;var Ni=wo((function(e,t){var n=(t=1==t.length&&zi(t[0])?Bt(t[0],$t(fa())):Bt(mr(t,1),$t(fa()))).length;return Vr((function(r){for(var o=-1,a=Pn(r.length,n);++o<a;)r[o]=t[o].call(this,r[o]);return St(e,this,r)}))})),ki=Vr((function(t,n){var r=fn(n,sa(ki));return ea(t,i,e,n,r)})),Oi=Vr((function(t,n){var r=fn(n,sa(Oi));return ea(t,u,e,n,r)})),ji=aa((function(t,n){return ea(t,c,e,e,e,n)}));function Wi(e,t){return e===t||e!=e&&t!=t}var Hi=Jo(Cr),Yi=Jo((function(e,t){return e>=t})),Qi=Fr(function(){return arguments}())?Fr:function(e){return ru(e)&&Le.call(e,"callee")&&!Xe.call(e,"callee")},zi=ue.isArray,qi=Pt?$t(Pt):function(e){return ru(e)&&Sr(e)==D};function Ki(e){return null!=e&&tu(e.length)&&!$i(e)}function Ji(e){return ru(e)&&Ki(e)}var Xi=yt||Al,Zi=Mt?$t(Mt):function(e){return ru(e)&&Sr(e)==A};function Vi(e){if(!ru(e))return!1;var t=Sr(e);return t==m||"[object DOMException]"==t||"string"==typeof e.message&&"string"==typeof e.name&&!iu(e)}function $i(e){if(!nu(e))return!1;var t=Sr(e);return t==y||t==P||"[object AsyncFunction]"==t||"[object Proxy]"==t}function eu(e){return"number"==typeof e&&e==vu(e)}function tu(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=f}function nu(e){var t=o(e);return null!=e&&("object"==t||"function"==t)}function ru(e){return null!=e&&"object"==o(e)}var ou=wt?$t(wt):function(e){return ru(e)&&_a(e)==I};function au(e){return"number"==typeof e||ru(e)&&Sr(e)==b}function iu(e){if(!ru(e)||Sr(e)!=S)return!1;var t=Ke(e);if(null===t)return!0;var n=Le.call(t,"constructor")&&t.constructor;return"function"==typeof n&&n instanceof n&&Ue.call(n)==je}var uu=xt?$t(xt):function(e){return ru(e)&&Sr(e)==T},lu=It?$t(It):function(e){return ru(e)&&_a(e)==E};function cu(e){return"string"==typeof e||!zi(e)&&ru(e)&&Sr(e)==R}function su(e){return"symbol"==o(e)||ru(e)&&Sr(e)==G}var fu=bt?$t(bt):function(e){return ru(e)&&tu(e.length)&&!!lt[Sr(e)]},du=Jo(Or),pu=Jo((function(e,t){return e<=t}));function hu(e){if(!e)return[];if(Ki(e))return cu(e)?gn(e):Go(e);if($e&&e[$e])return function(e){for(var t,n=[];!(t=e.next()).done;)n.push(t.value);return n}(e[$e]());var t=_a(e);return(t==I?cn:t==E?dn:Wu)(e)}function gu(e){return e?(e=Au(e))===s||e===-1/0?17976931348623157e292*(e<0?-1:1):e==e?e:0:0===e?e:0}function vu(e){var t=gu(e),n=t%1;return t==t?n?t-n:t:0}function _u(e){return e?cr(vu(e),0,p):0}function Au(e){if("number"==typeof e)return e;if(su(e))return d;if(nu(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=nu(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=Vt(e);var n=_e.test(e);return n||me.test(e)?dt(e.slice(2),n?2:8):ve.test(e)?d:+e}function mu(e){return Fo(e,Du(e))}function yu(e){return null==e?"":fo(e)}var Pu=Bo((function(e,t){if(ba(t)||Ki(t))Fo(t,Fu(t),e);else for(var n in t)Le.call(t,n)&&rr(e,n,t[n])})),Mu=Bo((function(e,t){Fo(t,Du(t),e)})),wu=Bo((function(e,t,n,r){Fo(t,Du(t),e,r)})),xu=Bo((function(e,t,n,r){Fo(t,Fu(t),e,r)})),Iu=aa(lr),bu=Vr((function(t,n){t=Ce(t);var r=-1,o=n.length,a=o>2?n[2]:e;for(a&&Ma(n[0],n[1],a)&&(o=1);++r<o;)for(var i=n[r],u=Du(i),l=-1,c=u.length;++l<c;){var s=u[l],f=t[s];(f===e||Wi(f,De[s])&&!Le.call(t,s))&&(t[s]=i[s])}return t})),Su=Vr((function(t){return t.push(e,na),St(Uu,e,t)}));function Cu(t,n,r){var o=null==t?e:Ir(t,n);return o===e?r:o}function Tu(e,t){return null!=e&&Aa(e,t,Er)}var Eu=Yo((function(e,t,n){null!=t&&"function"!=typeof t.toString&&(t=Oe.call(t)),e[t]=n}),nl(al)),Ru=Yo((function(e,t,n){null!=t&&"function"!=typeof t.toString&&(t=Oe.call(t)),Le.call(e,t)?e[t].push(n):e[t]=[n]}),fa),Gu=Vr(Gr);function Fu(e){return Ki(e)?Vn(e):Nr(e)}function Du(e){return Ki(e)?Vn(e,!0):kr(e)}var Bu=Bo((function(e,t,n){Yr(e,t,n)})),Uu=Bo((function(e,t,n,r){Yr(e,t,n,r)})),Lu=aa((function(e,t){var n={};if(null==e)return n;var r=!1;t=Bt(t,(function(t){return t=Mo(t,e),r||(r=t.length>1),t})),Fo(e,ua(e),n),r&&(n=sr(n,7,ra));for(var o=t.length;o--;)ho(n,t[o]);return n})),Nu=aa((function(e,t){return null==e?{}:function(e,t){return qr(e,t,(function(t,n){return Tu(e,n)}))}(e,t)}));function ku(e,t){if(null==e)return{};var n=Bt(ua(e),(function(e){return[e]}));return t=fa(t),qr(e,n,(function(e,n){return t(e,n[0])}))}var Ou=$o(Fu),ju=$o(Du);function Wu(e){return null==e?[]:en(e,Fu(e))}var Hu=ko((function(e,t,n){return t=t.toLowerCase(),e+(n?Yu(t):t)}));function Yu(e){return Vu(yu(e).toLowerCase())}function Qu(e){return(e=yu(e))&&e.replace(Pe,on).replace(tt,"")}var zu=ko((function(e,t,n){return e+(n?"-":"")+t.toLowerCase()})),qu=ko((function(e,t,n){return e+(n?" ":"")+t.toLowerCase()})),Ku=No("toLowerCase"),Ju=ko((function(e,t,n){return e+(n?"_":"")+t.toLowerCase()})),Xu=ko((function(e,t,n){return e+(n?" ":"")+Vu(t)})),Zu=ko((function(e,t,n){return e+(n?" ":"")+t.toUpperCase()})),Vu=No("toUpperCase");function $u(t,n,r){return t=yu(t),(n=r?e:n)===e?function(e){return at.test(e)}(t)?function(e){return e.match(rt)||[]}(t):function(e){return e.match(fe)||[]}(t):t.match(n)||[]}var el=Vr((function(t,n){try{return St(t,e,n)}catch(e){return Vi(e)?e:new Ie(e)}})),tl=aa((function(e,t){return Tt(t,(function(t){t=ka(t),ur(e,t,Ri(e[t],e))})),e}));function nl(e){return function(){return e}}var rl=Wo(),ol=Wo(!0);function al(e){return e}function il(e){return Lr("function"==typeof e?e:sr(e,1))}var ul=Vr((function(e,t){return function(n){return Gr(n,e,t)}})),ll=Vr((function(e,t){return function(n){return Gr(e,n,t)}}));function cl(e,t,n){var r=Fu(t),o=xr(t,r);null!=n||nu(t)&&(o.length||!r.length)||(n=t,t=e,e=this,o=xr(t,Fu(t)));var a=!(nu(n)&&"chain"in n&&!n.chain),i=$i(e);return Tt(o,(function(n){var r=t[n];e[n]=r,i&&(e.prototype[n]=function(){var t=this.__chain__;if(a||t){var n=e(this.__wrapped__);return(n.__actions__=Go(this.__actions__)).push({func:r,args:arguments,thisArg:e}),n.__chain__=t,n}return r.apply(e,Ut([this.value()],arguments))})})),e}function sl(){}var fl=zo(Bt),dl=zo(Rt),pl=zo(kt);function hl(e){return wa(e)?qt(ka(e)):function(e){return function(t){return Ir(t,e)}}(e)}var gl=Ko(),vl=Ko(!0);function _l(){return[]}function Al(){return!1}var ml,yl=Qo((function(e,t){return e+t}),0),Pl=Zo("ceil"),Ml=Qo((function(e,t){return e/t}),1),wl=Zo("floor"),xl=Qo((function(e,t){return e*t}),1),Il=Zo("round"),bl=Qo((function(e,t){return e-t}),0);return Wn.after=function(e,n){if("function"!=typeof n)throw new Re(t);return e=vu(e),function(){if(--e<1)return n.apply(this,arguments)}},Wn.ary=Ti,Wn.assign=Pu,Wn.assignIn=Mu,Wn.assignInWith=wu,Wn.assignWith=xu,Wn.at=Iu,Wn.before=Ei,Wn.bind=Ri,Wn.bindAll=tl,Wn.bindKey=Gi,Wn.castArray=function(){if(!arguments.length)return[];var e=arguments[0];return zi(e)?e:[e]},Wn.chain=hi,Wn.chunk=function(t,n,r){n=(r?Ma(t,n,r):n===e)?1:yn(vu(n),0);var o=null==t?0:t.length;if(!o||n<1)return[];for(var a=0,i=0,u=ue(vt(o/n));a<o;)u[i++]=ao(t,a,a+=n);return u},Wn.compact=function(e){for(var t=-1,n=null==e?0:e.length,r=0,o=[];++t<n;){var a=e[t];a&&(o[r++]=a)}return o},Wn.concat=function(){var e=arguments.length;if(!e)return[];for(var t=ue(e-1),n=arguments[0],r=e;r--;)t[r-1]=arguments[r];return Ut(zi(n)?Go(n):[n],mr(t,1))},Wn.cond=function(e){var n=null==e?0:e.length,r=fa();return e=n?Bt(e,(function(e){if("function"!=typeof e[1])throw new Re(t);return[r(e[0]),e[1]]})):[],Vr((function(t){for(var r=-1;++r<n;){var o=e[r];if(St(o[0],this,t))return St(o[1],this,t)}}))},Wn.conforms=function(e){return function(e){var t=Fu(e);return function(n){return fr(n,e,t)}}(sr(e,1))},Wn.constant=nl,Wn.countBy=_i,Wn.create=function(e,t){var n=Hn(e);return null==t?n:ir(n,t)},Wn.curry=function t(n,r,o){var a=ea(n,8,e,e,e,e,e,r=o?e:r);return a.placeholder=t.placeholder,a},Wn.curryRight=function t(n,r,o){var i=ea(n,a,e,e,e,e,e,r=o?e:r);return i.placeholder=t.placeholder,i},Wn.debounce=Fi,Wn.defaults=bu,Wn.defaultsDeep=Su,Wn.defer=Di,Wn.delay=Bi,Wn.difference=Wa,Wn.differenceBy=Ha,Wn.differenceWith=Ya,Wn.drop=function(t,n,r){var o=null==t?0:t.length;return o?ao(t,(n=r||n===e?1:vu(n))<0?0:n,o):[]},Wn.dropRight=function(t,n,r){var o=null==t?0:t.length;return o?ao(t,0,(n=o-(n=r||n===e?1:vu(n)))<0?0:n):[]},Wn.dropRightWhile=function(e,t){return e&&e.length?vo(e,fa(t,3),!0,!0):[]},Wn.dropWhile=function(e,t){return e&&e.length?vo(e,fa(t,3),!0):[]},Wn.fill=function(t,n,r,o){var a=null==t?0:t.length;return a?(r&&"number"!=typeof r&&Ma(t,n,r)&&(r=0,o=a),function(t,n,r,o){var a=t.length;for((r=vu(r))<0&&(r=-r>a?0:a+r),(o=o===e||o>a?a:vu(o))<0&&(o+=a),o=r>o?0:_u(o);r<o;)t[r++]=n;return t}(t,n,r,o)):[]},Wn.filter=function(e,t){return(zi(e)?Gt:Ar)(e,fa(t,3))},Wn.flatMap=function(e,t){return mr(Ii(e,t),1)},Wn.flatMapDeep=function(e,t){return mr(Ii(e,t),s)},Wn.flatMapDepth=function(t,n,r){return r=r===e?1:vu(r),mr(Ii(t,n),r)},Wn.flatten=qa,Wn.flattenDeep=function(e){return null!=e&&e.length?mr(e,s):[]},Wn.flattenDepth=function(t,n){return null!=t&&t.length?mr(t,n=n===e?1:vu(n)):[]},Wn.flip=function(e){return ea(e,512)},Wn.flow=rl,Wn.flowRight=ol,Wn.fromPairs=function(e){for(var t=-1,n=null==e?0:e.length,r={};++t<n;){var o=e[t];r[o[0]]=o[1]}return r},Wn.functions=function(e){return null==e?[]:xr(e,Fu(e))},Wn.functionsIn=function(e){return null==e?[]:xr(e,Du(e))},Wn.groupBy=Mi,Wn.initial=function(e){return null!=e&&e.length?ao(e,0,-1):[]},Wn.intersection=Ja,Wn.intersectionBy=Xa,Wn.intersectionWith=Za,Wn.invert=Eu,Wn.invertBy=Ru,Wn.invokeMap=wi,Wn.iteratee=il,Wn.keyBy=xi,Wn.keys=Fu,Wn.keysIn=Du,Wn.map=Ii,Wn.mapKeys=function(e,t){var n={};return t=fa(t,3),Mr(e,(function(e,r,o){ur(n,t(e,r,o),e)})),n},Wn.mapValues=function(e,t){var n={};return t=fa(t,3),Mr(e,(function(e,r,o){ur(n,r,t(e,r,o))})),n},Wn.matches=function(e){return Wr(sr(e,1))},Wn.matchesProperty=function(e,t){return Hr(e,sr(t,1))},Wn.memoize=Ui,Wn.merge=Bu,Wn.mergeWith=Uu,Wn.method=ul,Wn.methodOf=ll,Wn.mixin=cl,Wn.negate=Li,Wn.nthArg=function(e){return e=vu(e),Vr((function(t){return Qr(t,e)}))},Wn.omit=Lu,Wn.omitBy=function(e,t){return ku(e,Li(fa(t)))},Wn.once=function(e){return Ei(2,e)},Wn.orderBy=function(t,n,r,o){return null==t?[]:(zi(n)||(n=null==n?[]:[n]),zi(r=o?e:r)||(r=null==r?[]:[r]),zr(t,n,r))},Wn.over=fl,Wn.overArgs=Ni,Wn.overEvery=dl,Wn.overSome=pl,Wn.partial=ki,Wn.partialRight=Oi,Wn.partition=bi,Wn.pick=Nu,Wn.pickBy=ku,Wn.property=hl,Wn.propertyOf=function(t){return function(n){return null==t?e:Ir(t,n)}},Wn.pull=$a,Wn.pullAll=ei,Wn.pullAllBy=function(e,t,n){return e&&e.length&&t&&t.length?Kr(e,t,fa(n,2)):e},Wn.pullAllWith=function(t,n,r){return t&&t.length&&n&&n.length?Kr(t,n,e,r):t},Wn.pullAt=ti,Wn.range=gl,Wn.rangeRight=vl,Wn.rearg=ji,Wn.reject=function(e,t){return(zi(e)?Gt:Ar)(e,Li(fa(t,3)))},Wn.remove=function(e,t){var n=[];if(!e||!e.length)return n;var r=-1,o=[],a=e.length;for(t=fa(t,3);++r<a;){var i=e[r];t(i,r,e)&&(n.push(i),o.push(r))}return Jr(e,o),n},Wn.rest=function(n,r){if("function"!=typeof n)throw new Re(t);return Vr(n,r=r===e?r:vu(r))},Wn.reverse=ni,Wn.sampleSize=function(t,n,r){return n=(r?Ma(t,n,r):n===e)?1:vu(n),(zi(t)?er:eo)(t,n)},Wn.set=function(e,t,n){return null==e?e:to(e,t,n)},Wn.setWith=function(t,n,r,o){return o="function"==typeof o?o:e,null==t?t:to(t,n,r,o)},Wn.shuffle=function(e){return(zi(e)?tr:oo)(e)},Wn.slice=function(t,n,r){var o=null==t?0:t.length;return o?(r&&"number"!=typeof r&&Ma(t,n,r)?(n=0,r=o):(n=null==n?0:vu(n),r=r===e?o:vu(r)),ao(t,n,r)):[]},Wn.sortBy=Si,Wn.sortedUniq=function(e){return e&&e.length?co(e):[]},Wn.sortedUniqBy=function(e,t){return e&&e.length?co(e,fa(t,2)):[]},Wn.split=function(t,n,r){return r&&"number"!=typeof r&&Ma(t,n,r)&&(n=r=e),(r=r===e?p:r>>>0)?(t=yu(t))&&("string"==typeof n||null!=n&&!uu(n))&&!(n=fo(n))&&ln(t)?xo(gn(t),0,r):t.split(n,r):[]},Wn.spread=function(e,n){if("function"!=typeof e)throw new Re(t);return n=null==n?0:yn(vu(n),0),Vr((function(t){var r=t[n],o=xo(t,0,n);return r&&Ut(o,r),St(e,this,o)}))},Wn.tail=function(e){var t=null==e?0:e.length;return t?ao(e,1,t):[]},Wn.take=function(t,n,r){return t&&t.length?ao(t,0,(n=r||n===e?1:vu(n))<0?0:n):[]},Wn.takeRight=function(t,n,r){var o=null==t?0:t.length;return o?ao(t,(n=o-(n=r||n===e?1:vu(n)))<0?0:n,o):[]},Wn.takeRightWhile=function(e,t){return e&&e.length?vo(e,fa(t,3),!1,!0):[]},Wn.takeWhile=function(e,t){return e&&e.length?vo(e,fa(t,3)):[]},Wn.tap=function(e,t){return t(e),e},Wn.throttle=function(e,n,r){var o=!0,a=!0;if("function"!=typeof e)throw new Re(t);return nu(r)&&(o="leading"in r?!!r.leading:o,a="trailing"in r?!!r.trailing:a),Fi(e,n,{leading:o,maxWait:n,trailing:a})},Wn.thru=gi,Wn.toArray=hu,Wn.toPairs=Ou,Wn.toPairsIn=ju,Wn.toPath=function(e){return zi(e)?Bt(e,ka):su(e)?[e]:Go(Na(yu(e)))},Wn.toPlainObject=mu,Wn.transform=function(e,t,n){var r=zi(e),o=r||Xi(e)||fu(e);if(t=fa(t,4),null==n){var a=e&&e.constructor;n=o?r?new a:[]:nu(e)&&$i(a)?Hn(Ke(e)):{}}return(o?Tt:Mr)(e,(function(e,r,o){return t(n,e,r,o)})),n},Wn.unary=function(e){return Ti(e,1)},Wn.union=ri,Wn.unionBy=oi,Wn.unionWith=ai,Wn.uniq=function(e){return e&&e.length?po(e):[]},Wn.uniqBy=function(e,t){return e&&e.length?po(e,fa(t,2)):[]},Wn.uniqWith=function(t,n){return n="function"==typeof n?n:e,t&&t.length?po(t,e,n):[]},Wn.unset=function(e,t){return null==e||ho(e,t)},Wn.unzip=ii,Wn.unzipWith=ui,Wn.update=function(e,t,n){return null==e?e:go(e,t,Po(n))},Wn.updateWith=function(t,n,r,o){return o="function"==typeof o?o:e,null==t?t:go(t,n,Po(r),o)},Wn.values=Wu,Wn.valuesIn=function(e){return null==e?[]:en(e,Du(e))},Wn.without=li,Wn.words=$u,Wn.wrap=function(e,t){return ki(Po(t),e)},Wn.xor=ci,Wn.xorBy=si,Wn.xorWith=fi,Wn.zip=di,Wn.zipObject=function(e,t){return mo(e||[],t||[],rr)},Wn.zipObjectDeep=function(e,t){return mo(e||[],t||[],to)},Wn.zipWith=pi,Wn.entries=Ou,Wn.entriesIn=ju,Wn.extend=Mu,Wn.extendWith=wu,cl(Wn,Wn),Wn.add=yl,Wn.attempt=el,Wn.camelCase=Hu,Wn.capitalize=Yu,Wn.ceil=Pl,Wn.clamp=function(t,n,r){return r===e&&(r=n,n=e),r!==e&&(r=(r=Au(r))==r?r:0),n!==e&&(n=(n=Au(n))==n?n:0),cr(Au(t),n,r)},Wn.clone=function(e){return sr(e,4)},Wn.cloneDeep=function(e){return sr(e,5)},Wn.cloneDeepWith=function(t,n){return sr(t,5,n="function"==typeof n?n:e)},Wn.cloneWith=function(t,n){return sr(t,4,n="function"==typeof n?n:e)},Wn.conformsTo=function(e,t){return null==t||fr(e,t,Fu(t))},Wn.deburr=Qu,Wn.defaultTo=function(e,t){return null==e||e!=e?t:e},Wn.divide=Ml,Wn.endsWith=function(t,n,r){t=yu(t),n=fo(n);var o=t.length,a=r=r===e?o:cr(vu(r),0,o);return(r-=n.length)>=0&&t.slice(r,a)==n},Wn.eq=Wi,Wn.escape=function(e){return(e=yu(e))&&Z.test(e)?e.replace(J,an):e},Wn.escapeRegExp=function(e){return(e=yu(e))&&ae.test(e)?e.replace(oe,"\\$&"):e},Wn.every=function(t,n,r){var o=zi(t)?Rt:vr;return r&&Ma(t,n,r)&&(n=e),o(t,fa(n,3))},Wn.find=Ai,Wn.findIndex=Qa,Wn.findKey=function(e,t){return jt(e,fa(t,3),Mr)},Wn.findLast=mi,Wn.findLastIndex=za,Wn.findLastKey=function(e,t){return jt(e,fa(t,3),wr)},Wn.floor=wl,Wn.forEach=yi,Wn.forEachRight=Pi,Wn.forIn=function(e,t){return null==e?e:yr(e,fa(t,3),Du)},Wn.forInRight=function(e,t){return null==e?e:Pr(e,fa(t,3),Du)},Wn.forOwn=function(e,t){return e&&Mr(e,fa(t,3))},Wn.forOwnRight=function(e,t){return e&&wr(e,fa(t,3))},Wn.get=Cu,Wn.gt=Hi,Wn.gte=Yi,Wn.has=function(e,t){return null!=e&&Aa(e,t,Tr)},Wn.hasIn=Tu,Wn.head=Ka,Wn.identity=al,Wn.includes=function(e,t,n,r){e=Ki(e)?e:Wu(e),n=n&&!r?vu(n):0;var o=e.length;return n<0&&(n=yn(o+n,0)),cu(e)?n<=o&&e.indexOf(t,n)>-1:!!o&&Ht(e,t,n)>-1},Wn.indexOf=function(e,t,n){var r=null==e?0:e.length;if(!r)return-1;var o=null==n?0:vu(n);return o<0&&(o=yn(r+o,0)),Ht(e,t,o)},Wn.inRange=function(t,n,r){return n=gu(n),r===e?(r=n,n=0):r=gu(r),function(e,t,n){return e>=Pn(t,n)&&e<yn(t,n)}(t=Au(t),n,r)},Wn.invoke=Gu,Wn.isArguments=Qi,Wn.isArray=zi,Wn.isArrayBuffer=qi,Wn.isArrayLike=Ki,Wn.isArrayLikeObject=Ji,Wn.isBoolean=function(e){return!0===e||!1===e||ru(e)&&Sr(e)==_},Wn.isBuffer=Xi,Wn.isDate=Zi,Wn.isElement=function(e){return ru(e)&&1===e.nodeType&&!iu(e)},Wn.isEmpty=function(e){if(null==e)return!0;if(Ki(e)&&(zi(e)||"string"==typeof e||"function"==typeof e.splice||Xi(e)||fu(e)||Qi(e)))return!e.length;var t=_a(e);if(t==I||t==E)return!e.size;if(ba(e))return!Nr(e).length;for(var n in e)if(Le.call(e,n))return!1;return!0},Wn.isEqual=function(e,t){return Dr(e,t)},Wn.isEqualWith=function(t,n,r){var o=(r="function"==typeof r?r:e)?r(t,n):e;return o===e?Dr(t,n,e,r):!!o},Wn.isError=Vi,Wn.isFinite=function(e){return"number"==typeof e&&Ot(e)},Wn.isFunction=$i,Wn.isInteger=eu,Wn.isLength=tu,Wn.isMap=ou,Wn.isMatch=function(e,t){return e===t||Br(e,t,pa(t))},Wn.isMatchWith=function(t,n,r){return r="function"==typeof r?r:e,Br(t,n,pa(n),r)},Wn.isNaN=function(e){return au(e)&&e!=+e},Wn.isNative=function(e){if(Ia(e))throw new Ie("Unsupported core-js use. Try https://npms.io/search?q=ponyfill.");return Ur(e)},Wn.isNil=function(e){return null==e},Wn.isNull=function(e){return null===e},Wn.isNumber=au,Wn.isObject=nu,Wn.isObjectLike=ru,Wn.isPlainObject=iu,Wn.isRegExp=uu,Wn.isSafeInteger=function(e){return eu(e)&&e>=-9007199254740991&&e<=f},Wn.isSet=lu,Wn.isString=cu,Wn.isSymbol=su,Wn.isTypedArray=fu,Wn.isUndefined=function(t){return t===e},Wn.isWeakMap=function(e){return ru(e)&&_a(e)==F},Wn.isWeakSet=function(e){return ru(e)&&"[object WeakSet]"==Sr(e)},Wn.join=function(e,t){return null==e?"":Kt.call(e,t)},Wn.kebabCase=zu,Wn.last=Va,Wn.lastIndexOf=function(t,n,r){var o=null==t?0:t.length;if(!o)return-1;var a=o;return r!==e&&(a=(a=vu(r))<0?yn(o+a,0):Pn(a,o-1)),n==n?function(e,t,n){for(var r=n+1;r--;)if(e[r]===t)return r;return r}(t,n,a):Wt(t,Qt,a,!0)},Wn.lowerCase=qu,Wn.lowerFirst=Ku,Wn.lt=du,Wn.lte=pu,Wn.max=function(t){return t&&t.length?_r(t,al,Cr):e},Wn.maxBy=function(t,n){return t&&t.length?_r(t,fa(n,2),Cr):e},Wn.mean=function(e){return zt(e,al)},Wn.meanBy=function(e,t){return zt(e,fa(t,2))},Wn.min=function(t){return t&&t.length?_r(t,al,Or):e},Wn.minBy=function(t,n){return t&&t.length?_r(t,fa(n,2),Or):e},Wn.stubArray=_l,Wn.stubFalse=Al,Wn.stubObject=function(){return{}},Wn.stubString=function(){return""},Wn.stubTrue=function(){return!0},Wn.multiply=xl,Wn.nth=function(t,n){return t&&t.length?Qr(t,vu(n)):e},Wn.noConflict=function(){return gt._===this&&(gt._=We),this},Wn.noop=sl,Wn.now=Ci,Wn.pad=function(e,t,n){e=yu(e);var r=(t=vu(t))?hn(e):0;if(!t||r>=t)return e;var o=(t-r)/2;return qo(_t(o),n)+e+qo(vt(o),n)},Wn.padEnd=function(e,t,n){e=yu(e);var r=(t=vu(t))?hn(e):0;return t&&r<t?e+qo(t-r,n):e},Wn.padStart=function(e,t,n){e=yu(e);var r=(t=vu(t))?hn(e):0;return t&&r<t?qo(t-r,n)+e:e},Wn.parseInt=function(e,t,n){return n||null==t?t=0:t&&(t=+t),wn(yu(e).replace(ie,""),t||0)},Wn.random=function(t,n,r){if(r&&"boolean"!=typeof r&&Ma(t,n,r)&&(n=r=e),r===e&&("boolean"==typeof n?(r=n,n=e):"boolean"==typeof t&&(r=t,t=e)),t===e&&n===e?(t=0,n=1):(t=gu(t),n===e?(n=t,t=0):n=gu(n)),t>n){var o=t;t=n,n=o}if(r||t%1||n%1){var a=xn();return Pn(t+a*(n-t+ft("1e-"+((a+"").length-1))),n)}return Xr(t,n)},Wn.reduce=function(e,t,n){var r=zi(e)?Lt:Jt,o=arguments.length<3;return r(e,fa(t,4),n,o,hr)},Wn.reduceRight=function(e,t,n){var r=zi(e)?Nt:Jt,o=arguments.length<3;return r(e,fa(t,4),n,o,gr)},Wn.repeat=function(t,n,r){return n=(r?Ma(t,n,r):n===e)?1:vu(n),Zr(yu(t),n)},Wn.replace=function(){var e=arguments,t=yu(e[0]);return e.length<3?t:t.replace(e[1],e[2])},Wn.result=function(t,n,r){var o=-1,a=(n=Mo(n,t)).length;for(a||(a=1,t=e);++o<a;){var i=null==t?e:t[ka(n[o])];i===e&&(o=a,i=r),t=$i(i)?i.call(t):i}return t},Wn.round=Il,Wn.runInContext=M,Wn.sample=function(e){return(zi(e)?$n:$r)(e)},Wn.size=function(e){if(null==e)return 0;if(Ki(e))return cu(e)?hn(e):e.length;var t=_a(e);return t==I||t==E?e.size:Nr(e).length},Wn.snakeCase=Ju,Wn.some=function(t,n,r){var o=zi(t)?kt:io;return r&&Ma(t,n,r)&&(n=e),o(t,fa(n,3))},Wn.sortedIndex=function(e,t){return uo(e,t)},Wn.sortedIndexBy=function(e,t,n){return lo(e,t,fa(n,2))},Wn.sortedIndexOf=function(e,t){var n=null==e?0:e.length;if(n){var r=uo(e,t);if(r<n&&Wi(e[r],t))return r}return-1},Wn.sortedLastIndex=function(e,t){return uo(e,t,!0)},Wn.sortedLastIndexBy=function(e,t,n){return lo(e,t,fa(n,2),!0)},Wn.sortedLastIndexOf=function(e,t){if(null!=e&&e.length){var n=uo(e,t,!0)-1;if(Wi(e[n],t))return n}return-1},Wn.startCase=Xu,Wn.startsWith=function(e,t,n){return e=yu(e),n=null==n?0:cr(vu(n),0,e.length),t=fo(t),e.slice(n,n+t.length)==t},Wn.subtract=bl,Wn.sum=function(e){return e&&e.length?Xt(e,al):0},Wn.sumBy=function(e,t){return e&&e.length?Xt(e,fa(t,2)):0},Wn.template=function(t,n,r){var o=Wn.templateSettings;r&&Ma(t,n,r)&&(n=e),t=yu(t),n=wu({},n,o,ta);var a,i,u=wu({},n.imports,o.imports,ta),l=Fu(u),c=en(u,l),s=0,f=n.interpolate||Me,d="__p += '",p=Te((n.escape||Me).source+"|"+f.source+"|"+(f===ee?he:Me).source+"|"+(n.evaluate||Me).source+"|$","g"),h="//# sourceURL="+(Le.call(n,"sourceURL")?(n.sourceURL+"").replace(/\s/g," "):"lodash.templateSources["+ ++ut+"]")+"\n";t.replace(p,(function(e,n,r,o,u,l){return r||(r=o),d+=t.slice(s,l).replace(we,un),n&&(a=!0,d+="' +\n__e("+n+") +\n'"),u&&(i=!0,d+="';\n"+u+";\n__p += '"),r&&(d+="' +\n((__t = ("+r+")) == null ? '' : __t) +\n'"),s=l+e.length,e})),d+="';\n";var g=Le.call(n,"variable")&&n.variable;if(g){if(de.test(g))throw new Ie("Invalid `variable` option passed into `_.template`")}else d="with (obj) {\n"+d+"\n}\n";d=(i?d.replace(Q,""):d).replace(z,"$1").replace(q,"$1;"),d="function("+(g||"obj")+") {\n"+(g?"":"obj || (obj = {});\n")+"var __t, __p = ''"+(a?", __e = _.escape":"")+(i?", __j = Array.prototype.join;\nfunction print() { __p += __j.call(arguments, '') }\n":";\n")+d+"return __p\n}";var v=el((function(){return be(l,h+"return "+d).apply(e,c)}));if(v.source=d,Vi(v))throw v;return v},Wn.times=function(e,t){if((e=vu(e))<1||e>f)return[];var n=p,r=Pn(e,p);t=fa(t),e-=p;for(var o=Zt(r,t);++n<e;)t(n);return o},Wn.toFinite=gu,Wn.toInteger=vu,Wn.toLength=_u,Wn.toLower=function(e){return yu(e).toLowerCase()},Wn.toNumber=Au,Wn.toSafeInteger=function(e){return e?cr(vu(e),-9007199254740991,f):0===e?e:0},Wn.toString=yu,Wn.toUpper=function(e){return yu(e).toUpperCase()},Wn.trim=function(t,n,r){if((t=yu(t))&&(r||n===e))return Vt(t);if(!t||!(n=fo(n)))return t;var o=gn(t),a=gn(n);return xo(o,nn(o,a),rn(o,a)+1).join("")},Wn.trimEnd=function(t,n,r){if((t=yu(t))&&(r||n===e))return t.slice(0,vn(t)+1);if(!t||!(n=fo(n)))return t;var o=gn(t);return xo(o,0,rn(o,gn(n))+1).join("")},Wn.trimStart=function(t,n,r){if((t=yu(t))&&(r||n===e))return t.replace(ie,"");if(!t||!(n=fo(n)))return t;var o=gn(t);return xo(o,nn(o,gn(n))).join("")},Wn.truncate=function(t,n){var r=30,o="...";if(nu(n)){var a="separator"in n?n.separator:a;r="length"in n?vu(n.length):r,o="omission"in n?fo(n.omission):o}var i=(t=yu(t)).length;if(ln(t)){var u=gn(t);i=u.length}if(r>=i)return t;var l=r-hn(o);if(l<1)return o;var c=u?xo(u,0,l).join(""):t.slice(0,l);if(a===e)return c+o;if(u&&(l+=c.length-l),uu(a)){if(t.slice(l).search(a)){var s,f=c;for(a.global||(a=Te(a.source,yu(ge.exec(a))+"g")),a.lastIndex=0;s=a.exec(f);)var d=s.index;c=c.slice(0,d===e?l:d)}}else if(t.indexOf(fo(a),l)!=l){var p=c.lastIndexOf(a);p>-1&&(c=c.slice(0,p))}return c+o},Wn.unescape=function(e){return(e=yu(e))&&X.test(e)?e.replace(K,_n):e},Wn.uniqueId=function(e){var t=++Ne;return yu(e)+t},Wn.upperCase=Zu,Wn.upperFirst=Vu,Wn.each=yi,Wn.eachRight=Pi,Wn.first=Ka,cl(Wn,(ml={},Mr(Wn,(function(e,t){Le.call(Wn.prototype,t)||(ml[t]=e)})),ml),{chain:!1}),Wn.VERSION="4.17.21",Tt(["bind","bindKey","curry","curryRight","partial","partialRight"],(function(e){Wn[e].placeholder=Wn})),Tt(["drop","take"],(function(t,n){zn.prototype[t]=function(r){r=r===e?1:yn(vu(r),0);var o=this.__filtered__&&!n?new zn(this):this.clone();return o.__filtered__?o.__takeCount__=Pn(r,o.__takeCount__):o.__views__.push({size:Pn(r,p),type:t+(o.__dir__<0?"Right":"")}),o},zn.prototype[t+"Right"]=function(e){return this.reverse()[t](e).reverse()}})),Tt(["filter","map","takeWhile"],(function(e,t){var n=t+1,r=1==n||3==n;zn.prototype[e]=function(e){var t=this.clone();return t.__iteratees__.push({iteratee:fa(e,3),type:n}),t.__filtered__=t.__filtered__||r,t}})),Tt(["head","last"],(function(e,t){var n="take"+(t?"Right":"");zn.prototype[e]=function(){return this[n](1).value()[0]}})),Tt(["initial","tail"],(function(e,t){var n="drop"+(t?"":"Right");zn.prototype[e]=function(){return this.__filtered__?new zn(this):this[n](1)}})),zn.prototype.compact=function(){return this.filter(al)},zn.prototype.find=function(e){return this.filter(e).head()},zn.prototype.findLast=function(e){return this.reverse().find(e)},zn.prototype.invokeMap=Vr((function(e,t){return"function"==typeof e?new zn(this):this.map((function(n){return Gr(n,e,t)}))})),zn.prototype.reject=function(e){return this.filter(Li(fa(e)))},zn.prototype.slice=function(t,n){t=vu(t);var r=this;return r.__filtered__&&(t>0||n<0)?new zn(r):(t<0?r=r.takeRight(-t):t&&(r=r.drop(t)),n!==e&&(r=(n=vu(n))<0?r.dropRight(-n):r.take(n-t)),r)},zn.prototype.takeRightWhile=function(e){return this.reverse().takeWhile(e).reverse()},zn.prototype.toArray=function(){return this.take(p)},Mr(zn.prototype,(function(t,n){var r=/^(?:filter|find|map|reject)|While$/.test(n),o=/^(?:head|last)$/.test(n),a=Wn[o?"take"+("last"==n?"Right":""):n],i=o||/^find/.test(n);a&&(Wn.prototype[n]=function(){var n=this.__wrapped__,u=o?[1]:arguments,l=n instanceof zn,c=u[0],s=l||zi(n),f=function(e){var t=a.apply(Wn,Ut([e],u));return o&&d?t[0]:t};s&&r&&"function"==typeof c&&1!=c.length&&(l=s=!1);var d=this.__chain__,p=!!this.__actions__.length,h=i&&!d,g=l&&!p;if(!i&&s){n=g?n:new zn(this);var v=t.apply(n,u);return v.__actions__.push({func:gi,args:[f],thisArg:e}),new Qn(v,d)}return h&&g?t.apply(this,u):(v=this.thru(f),h?o?v.value()[0]:v.value():v)})})),Tt(["pop","push","shift","sort","splice","unshift"],(function(e){var t=Ge[e],n=/^(?:push|sort|unshift)$/.test(e)?"tap":"thru",r=/^(?:pop|shift)$/.test(e);Wn.prototype[e]=function(){var e=arguments;if(r&&!this.__chain__){var o=this.value();return t.apply(zi(o)?o:[],e)}return this[n]((function(n){return t.apply(zi(n)?n:[],e)}))}})),Mr(zn.prototype,(function(e,t){var n=Wn[t];if(n){var r=n.name+"";Le.call(Fn,r)||(Fn[r]=[]),Fn[r].push({name:t,func:n})}})),Fn[Ho(e,2).name]=[{name:"wrapper",func:e}],zn.prototype.clone=function(){var e=new zn(this.__wrapped__);return e.__actions__=Go(this.__actions__),e.__dir__=this.__dir__,e.__filtered__=this.__filtered__,e.__iteratees__=Go(this.__iteratees__),e.__takeCount__=this.__takeCount__,e.__views__=Go(this.__views__),e},zn.prototype.reverse=function(){if(this.__filtered__){var e=new zn(this);e.__dir__=-1,e.__filtered__=!0}else(e=this.clone()).__dir__*=-1;return e},zn.prototype.value=function(){var e=this.__wrapped__.value(),t=this.__dir__,n=zi(e),r=t<0,o=n?e.length:0,a=function(e,t,n){for(var r=-1,o=n.length;++r<o;){var a=n[r],i=a.size;switch(a.type){case"drop":e+=i;break;case"dropRight":t-=i;break;case"take":t=Pn(t,e+i);break;case"takeRight":e=yn(e,t-i)}}return{start:e,end:t}}(0,o,this.__views__),i=a.start,u=a.end,l=u-i,c=r?u:i-1,s=this.__iteratees__,f=s.length,d=0,p=Pn(l,this.__takeCount__);if(!n||!r&&o==l&&p==l)return _o(e,this.__actions__);var h=[];e:for(;l--&&d<p;){for(var g=-1,v=e[c+=t];++g<f;){var _=s[g],A=_.iteratee,m=_.type,y=A(v);if(2==m)v=y;else if(!y){if(1==m)continue e;break e}}h[d++]=v}return h},Wn.prototype.at=vi,Wn.prototype.chain=function(){return hi(this)},Wn.prototype.commit=function(){return new Qn(this.value(),this.__chain__)},Wn.prototype.next=function(){this.__values__===e&&(this.__values__=hu(this.value()));var t=this.__index__>=this.__values__.length;return{done:t,value:t?e:this.__values__[this.__index__++]}},Wn.prototype.plant=function(t){for(var n,r=this;r instanceof Yn;){var o=ja(r);o.__index__=0,o.__values__=e,n?a.__wrapped__=o:n=o;var a=o;r=r.__wrapped__}return a.__wrapped__=t,n},Wn.prototype.reverse=function(){var t=this.__wrapped__;if(t instanceof zn){var n=t;return this.__actions__.length&&(n=new zn(this)),(n=n.reverse()).__actions__.push({func:gi,args:[ni],thisArg:e}),new Qn(n,this.__chain__)}return this.thru(ni)},Wn.prototype.toJSON=Wn.prototype.valueOf=Wn.prototype.value=function(){return _o(this.__wrapped__,this.__actions__)},Wn.prototype.first=Wn.prototype.head,$e&&(Wn.prototype[$e]=function(){return this}),Wn}();_t?((_t.exports=An)._=An,vt._=An):gt._=An}.call(x);var T=c((function e(){var t=this;u(this,e),this.promise=new Promise((function(e,n){t.resolve=function(t){e(t)},t.reject=function(e){n(e)}}))}));function E(e,t){this.constructor===E&&function(e){throw new Error(e)}("Cannot initialize BaseException."),this.message=e,this.name=t}function R(e){return e.startsWith("#")?"hex":e.startsWith("rgb(")?"rgb":e.startsWith("hsl")?"hsl":"unknown"}function G(e){var t=R(e);if("hex"===t){var n=D(e),r=n.R,o=n.G,a=n.B;return{R:C(r/255),G:C(o/255),B:C(a/255)}}if("rgb"===t){var i=e.match(/(\d+),\s*(\d+),\s*(\d+)/);return{R:C(parseInt(i[1])/255),G:C(parseInt(i[2])/255),B:C(parseInt(i[3])/255)}}if("hsl"===t){var u=e.match(/hsl\((\d+),\s*(\d+)%,\s*(\d+)%\)/);if(!u)return{error:"Invalid HSL format"};var l=parseInt(u[1]),c=parseInt(u[2])/100,s=parseInt(u[3])/100,f=B(l,c,s),d=f.R,p=f.G,h=f.B;return{R:C(d/255),G:C(p/255),B:C(h/255)}}console.error({message:"Invalid color value"})}function F(e,t,n){return function(e,t,n){var r=(1<<24|e<<16|t<<8|n).toString(16).slice(1).toUpperCase();return"#".concat(r)}(Math.round(255*e),Math.round(255*t),Math.round(255*n))}function D(e){var t=e.slice(1),n=parseInt(t,16);return{R:n>>16&255,G:n>>8&255,B:255&n}}E.prototype=new Error,E.constructor=E,"undefined"!=typeof document&&document.documentElement.style;var B=function(e,t,n){var r,o,a;if(e/=360,0===t)r=o=a=Math.round(255*n);else{var i=function(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+6*(t-e)*n:n<.5?t:n<2/3?e+(t-e)*(2/3-n)*6:e},u=n<.5?n*(1+t):n+t-n*t,l=2*n-u;r=Math.round(255*i(l,u,e+1/3)),o=Math.round(255*i(l,u,e)),a=Math.round(255*i(l,u,e-1/3))}return{R:r,G:o,B:a}},U={0:"left",1:"center",2:"right"},L={LEFT:0,CENTER:1,CENTERED:1,RIGHT:2},N={TEXT:1,LINK:2,FREETEXT:3,LINE:4,SQUARE:5,CIRCLE:6,POLYGON:7,POLYLINE:8,HIGHLIGHT:9,UNDERLINE:10,SQUIGGLY:11,STRIKEOUT:12,STAMP:13,CARET:14,INK:15,POPUP:16,FILEATTACHMENT:17,SOUND:18,MOVIE:19,WIDGET:20,SCREEN:21,PRINTERMARK:22,TRAPNET:23,WATERMARK:24,THREED:25,RICHMEDIA:26,REDACT:27,INTERCHANGE:28,ARC:29},k={1:"text",2:"link",3:"freetext",4:"line",5:"square",6:"circle",7:"polygon",8:"polyline",9:"highlight",10:"underline",11:"squiggly",12:"strikeout",13:"stamp",14:"caret",15:"ink",16:"popup",17:"fileattachment",18:"sound",19:"movie",20:"widget",21:"screen",22:"printermark",23:"trapnet",24:"watermark",25:"threed",26:"richmedia",27:"redact",28:"interchange",29:"arc"},O={PUSHBUTTON:0,CHECKBOX:1,RADIOBUTTON:2,TEXTFIELD:3,COMBOBOX:4,LISTBOX:5,SIGNATUREFIELDS:6,UNKNOWN:255},j={0:"pushbutton",1:"checkbox",2:"radiobutton",3:"textfield",4:"combobox",5:"listbox",6:"signatureFields",255:"unknown"},W=1,H=2,Y=32,Q={UNKNOWN:-1,NONE:0,OPENARROW:1,CLOSEDARROW:2,SQUARE:3,CIRCLE:4,DIAMOND:5,BUTT:6,ROPENARROW:7,RCLOSEDARROW:8,SLASH:9},z={"-1":"unknown",0:"none",1:"openarrow",2:"closedarrow",3:"square",4:"circle",5:"diamond",6:"butt",7:"ropenarrow",8:"rclosedarrow",9:"slash"};s(s(s(s(s({1:"none",0:"check"},"1","circle"),2,"cross"),3,"diamond"),4,"square"),5,"star");var q={0:"unknown",1:"standard",2:"image",3:"text",4:"digital"},K={0:"unknown",1:"goto",2:"gotor",3:"gotoe",4:"launch",5:"thread",6:"uri",7:"sound",8:"movie",9:"hide",10:"named",11:"submitform",12:"resetform",13:"importdata",14:"javascript",15:"setocgstate",16:"rendition",17:"trans",18:"goto3dview",19:"uop",255:"error"},J={solid:0,dashed:1,beveled:2,inset:3,underline:4},X={0:"solid",1:"dashed",2:"beveled",3:"inset",4:"underline"},Z={0:"rect",1:"leftTriangle",2:"rightTriangle",3:"none"},V=c((function e(){u(this,e)}));s(V,"CSS",96),s(V,"PDF",72),s(V,"PDF_TO_CSS_UNITS",1);var $={MARKED:0,UNMARKED:1,ACCEPTED:2,REJECTED:3,CANCELLED:4,COMPLETED:5,NONE:6,ERROR:7},ee={0:"MARKED",1:"UNMARKED",2:"ACCEPTED",3:"REJECTED",4:"CANCELLED",5:"COMPLETED",6:"NONE",7:"ERROR"},te={DISTANCE:0,PERIMETER:1,AREA:2,ANGLE:3,COUNT:4,ARC:5},ne={0:"distance",1:"perimeter",2:"area",3:"angle",4:"count",5:"arc"},re={AREA:1,LENGTH:2,RADIUS:4,DIAMETER:8,ANGLE:16,COUNT:32,ARCLENGTH:64,GENERIC:128},oe={1:"area",2:"length",4:"radius",8:"diameter",16:"angle",32:"count",64:"arclength",128:"generic"},ae=function(){return c((function e(t,n,r){var o=this;u(this,e),this.sourceName=t,this.targetName=n,this.comObj=r,this.callbackId=1,this.callbackCapabilities=Object.create(null),this.actionHandler=Object.create(null),this._onComObjOnMessage=function(e){var t=e.data;if(t.targetName===o.sourceName)if(t.callback){var n=t.callbackId,a=o.callbackCapabilities[n];if(!a)throw new Error("Cannot resolve callback ".concat(n));if(delete o.callbackCapabilities[n],"resolve"!==t.callback)throw console.log({data:{action:t.data.action,data:t.data.data},message:t.reason&&t.reason.message}),new Error("Unexpected callback case");a.resolve(t.data)}else{var i=o.actionHandler[t.action];if(!i)throw new Error("Unknown action from worker: ".concat(t.action));if(t.callbackId){var u=o.sourceName,l=t.sourceName;new Promise((function(e){e(i(t.data))})).then((function(e){r.postMessage({sourceName:u,targetName:l,callback:"resolve",callbackId:t.callbackId,data:e})}),(function(e){r.postMessage({data:t,sourceName:u,targetName:l,callback:"reject",callbackId:t.callbackId,reason:e})}))}else i(t.data)}},r.addEventListener("message",this._onComObjOnMessage)}),[{key:"on",value:function(e,t){var n=this.actionHandler;if(n[e])throw new Error('There is already an actionName called "'.concat(e,'"'));n[e]=t}},{key:"send",value:function(e,t){this.comObj.postMessage({sourceName:this.sourceName,targetName:this.targetName,action:e,data:t})}},{key:"sendWithPromise",value:function(e,t){var n=this.callbackId++,r=new T;this.callbackCapabilities[n]=r;try{this.comObj.postMessage({sourceName:this.sourceName,targetName:this.targetName,action:e,callbackId:n,data:t})}catch(e){r.reject(e)}return r.promise}},{key:"destroy",value:function(){this.comObj.removeEventListener("message",this._onComObjOnMessage)}}])}();function ie(e){return new Promise((function(t,n){var r=new FileReader;r.onload=function(){var e=new Uint8Array(r.result);t(e)},r.onerror=function(){n(r.error)},r.readAsArrayBuffer(e)}))}function ue(e){for(var t=e.split(";base64,")[1],n=atob(t),r=new Uint8Array(n.length),o=0;o<n.length;o++)r[o]=n.charCodeAt(o);return r}importScripts("./ComPDFKit.js");var ComPDFKitJS={opened_Font:[],opened_DefFont:[],ComPDFKitDownloadFont:function(e){if(ComPDFKitJS.opened_Font[e])return ComPDFKitJS.opened_Font[e];return!1},ComPDFKitDownloadFontA:function(e){if(ComPDFKitJS.opened_Font[e])return ComPDFKitJS.opened_Font[e];var t=fontsJson[e-1];if(!t)return!1;var fontFile=fe(le+t.filePath);return pe.MessageHandler.send("LoadFont",{fontFamily:t.family}),ComPDFKitJS.opened_Font[e]=fontFile,fontFile},FontNameList:[],FontCount:1,opened_image:[],opened_files:[],opened_cert:[]},DataArray=[],LineType={},BorderStyle={},PageSize={},Rect={},RGBColor={},FontDa={},PDFDestination={},WidgetItem={},WidgetItemsArray={},TextFindItemArray=[],EditCharPlace={},EndEditCharPlace={},RectArray=[],CursorPoints={},EditTextStyle={},U8StringData="",PDFRange={},TextRectArray=[],ImageAreaInfo={},SignerArray=[],CertArray=[],AccessInfoArray=[],PolicyArray=[],CRLPointArray=[],OCSPUrlArray=[],DiffInfoArray=[],AnnotArray=[],fontsJson=null,le="https://www.compdf.com/fonts/",LayerPrintData={},LayerZoomData={},SepColorsArray=[],SelectPointColorArray=[],MeasureVerticesArray=[],PermissionsInfo={};function ce(e){return se.apply(this,arguments)}function se(){return(se=i(n().mark((function e(t){return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,new Promise((function(e){return setTimeout(e,t)}));case 2:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function fe(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=new XMLHttpRequest;n.open("GET",e,t),n.responseType="arraybuffer";var r=function(){return(200===n.status||206===n.status)&&new Uint8Array(n.response)};if(t)return new Promise((function(e,t){n.onload=function(){return e(r())},n.onerror=function(){return t(new Error("Network error"))},n.send()}));try{return n.send(),r()}catch(e){return!1}}function de(e){var t=parseInt(e.slice(1),16),n=t>>8&255,r=255&t;return{R:C((t>>16&255)/255),G:C(n/255),B:C(r/255)}}var pe=function(){function e(){u(this,e)}return c(e,null,[{key:"setUp",value:(t=i(n().mark((function t(){var r,o;return n().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:r=new ae("worker","main",self),this.MessageHandler=r,o=!1,r.on("test",(function(e){o||(o=!0,r.send("test",e instanceof Uint8Array))})),r.send("ready",null),r.on("Ready",(function(t){return e.createDocumentHandler(self)}));case 6:case"end":return t.stop()}}),t,this)}))),function(){return t.apply(this,arguments)})},{key:"createDocumentHandler",value:function(){var e=this.MessageHandler;e.on("InitConfig",(function(e){var t=e.license,n=e.appId,r=e.version,o=stringToNewUTF8(t),a=stringToNewUTF8(""),i=stringToNewUTF8(n),u=stringToNewUTF8(r);return Module._InitComPDFkit(o,!1,a,i,0,u),Re()})),e.on("LoadFile",function(){var e=i(n().mark((function e(t){var r,o,a,i,u,l,fontFile,c;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(ComPDFKitJS.opened_files=[],ComPDFKitJS.opened_files[0]=t.buffer,t.pagesPtr&&t.pagesPtr.length>0&&t.pagesPtr.forEach((function(e){Module._ClearPage(e.pagePtr)})),t.doc&&Module._ClearDocument(t.doc),t.fileList&&(ComPDFKitJS.opened_Font[0]=t.fileList),"https://www.compdf.com/fonts/"!==t.webFontURL&&(le=t.webFontURL),!t.fontsJson||fontsJson){e.next=26;break}if(fontsJson=t.fontsJson,r=!!ComPDFKitJS.opened_Font[0],ComPDFKitJS.FontCount=r?t.fontsJson.length+1:t.fontsJson.length,r){e.next=25;break}for(o=[],a=0;a<t.fontsJson.length;a++)o.push(fe(le+t.fontsJson[a].filePath,!0));if(!o.length){e.next=25;break}return i=null,e.prev=15,e.next=18,Promise.all(o);case 18:i=e.sent,e.next=24;break;case 21:e.prev=21,e.t0=e.catch(15),console.log(e.t0);case 24:if(i&&i.length)for(u=0;u<o.length;u++)l=ComPDFKitJS.opened_Font.length,i[u]&&(ComPDFKitJS.opened_Font[l]=i[u]);case 25:Module._InitComPDFkitFont(r);case 26:return t.enableDefaultFont&&!ComPDFKitJS.opened_DefFont[0]&&(fontFile=fe("./DroidSansFallbackFull.ttf"),ComPDFKitJS.opened_DefFont[0]=fontFile),c=Module._InitDocument(),e.abrupt("return",c);case 29:case"end":return e.stop()}}),e,null,[[15,21]])})));return function(t){return e.apply(this,arguments)}}()),e.on("GetWebFontNameList",(function(){var e=ComPDFKitJS.FontNameList;return e.length?e:null})),e.on("LoadDocumentByStream",ge),e.on("GetPageCount",(function(e){var t=e.doc;return Module._GetPageCount(t)})),e.on("GetPageSize",(function(e){var t=e.doc,n=e.pageIndex;PageSize.Width=0,PageSize.Height=0,Module._GetPageSizeForIndex(t,n);var r=Module._GetPage(t,n),o=Module._GetPageRotation(r);return{pagePtr:r,width:PageSize.Width,height:PageSize.Height,rotation:90*o}})),e.on("InitAnnot",(function(e){for(var t=e.doc,n=e.pagesPtr,r=Ie({doc:t}),o=r.signatures,a=r.signaturePtrList,i=[],u=0;u<n.length;u++)for(var l=n[u].pagePtr,c=Module._GetAnnotCount(l),s=0;s<c;s++){var f=Module._InitAnnot(l,s);if(f)if(!Module._IsReplyAnnot(f)){var d=Module._GetAnnotType(f),p=k[d],h=null;(h="widget"===p?ye({doc:t,pagePtr:l,annotPtr:f,signatures:o,pageIndex:u}):me({doc:t,pagePtr:l,annotPtr:f,type:p,pageIndex:u}))&&i.push(h)}}return{annotations:i,signatures:o,signaturePtrList:a}})),e.on("GetAnnotation",(function(e){return me({doc:e.doc,pagePtr:e.pagePtr,annotPtr:e.annotPtr,typeInt:e.typeInt,scale:e.scale})})),e.on("GetPermissionsInfo",(function(e){PermissionsInfo={};var t=e.doc;return Module._GetPermissionsInfo(t),PermissionsInfo})),e.on("CheckOwnerPassword",(function(e){var t=e.doc,n=e.pwd,r=stringToNewUTF8(n);return!!Module._CheckOwnerPassword(t,r)})),e.on("GetDynamicStampPreview",(function(e){var t=e.contents,n=e.time,r=e.stampShape,o=e.stampColor,a=Module._InitDocument();Module._CreateDocument(a),Module._InsertPage(a,0,595,842);var i=Module._GetPage(a,0),u=Module._CreateAnnot(a,i,13),l=stringToNewUTF8(t),c=stringToNewUTF8(n);Module._SetTextStamp(u,l,c,r,o,0),Module._UpdateAnnotAp(u,0),Rect={},Module._GetAnnotRect(i,u);for(var s=Rect,f=s.Left,d=s.Top,p=s.Right,h=s.Bottom,g=Math.ceil(p-f),v=Math.ceil(h-d),_=g*v,A=4*_,m=_malloc(A),y=0;y<_;y++)Module.HEAP32[m/4+y]=0;Module._RenderAnnot(u,g,v,m,0);var P=new Uint8Array(A);for(y=0;y<A;y+=4)P[y]=Module.HEAPU8[m+y+2],P[y+1]=Module.HEAPU8[m+y+1],P[y+2]=Module.HEAPU8[m+y],P[y+3]=Module.HEAPU8[m+y+3];return _free(m),{width:g,height:v,imageArray:P}})),e.on("GetRenderAnnot",(function(e){for(var t=e.pagePtr,n=e.annotPtr,r=e.width,o=e.height,a=r*o,i=_malloc(4*a),u=0;u<a;u++)Module.HEAP32[i/4+u]=0;var l=function(e,t,n,r,o){var a,i;1===o||3===o?(a=r,i=n):(a=n,i=r);Module._RenderAnnot(e,a,i,t,0);for(var u=new Uint8Array(a*i*4),l=0;l<i;l++)for(var c=0;c<a;c++){var s=4*(l*a+c),f=void 0,d=void 0;switch(o){case 1:f=i-1-l,d=c;break;case 2:f=a-1-c,d=i-1-l;break;case 3:f=l,d=a-1-c;break;default:f=c,d=l}var p=4*(d*n+f);u[p]=Module.HEAPU8[t+s+2],u[p+1]=Module.HEAPU8[t+s+1],u[p+2]=Module.HEAPU8[t+s],u[p+3]=Module.HEAPU8[t+s+3]}return{imageArray:u,width:a,height:i}}(n,i,r,o,Module._GetPageRotation(t)),c=l.imageArray;return _free(i),c})),e.on("GetWidgetAnnotation",(function(e){return ye({doc:e.doc,pagePtr:e.pagePtr,annotPtr:e.annotPtr,typeInt:e.typeInt,signaturePtrList:e.signaturePtrList})})),e.on("GetSignatures",(function(e){return Ie(e)})),e.on("UpdateDigitalSignAp",function(){var e=i(n().mark((function e(t){var r,o,a,i,u,l,c,s,f,d,p,h,g,v,_,A,m;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:for("data:image/png;base64,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",r=ue("data:image/png;base64,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"),ComPDFKitJS.opened_image=[],ComPDFKitJS.opened_image[0]=r,t.imageBase64&&"image"===t.appearance?st(t):dt(t),o=t.pkcs12Buffer,a=t.password,i=t.annotPtr,u=t.doc,ComPDFKitJS.opened_cert[0]=o,l=stringToNewUTF8(a),c=Module._GetPKCS12Cert(l,0,o.length),(DataArray=[]).push(ComPDFKitJS.opened_files[0]),s=Module._InitSignature2(i),Module._AddSignSaveDocument(u,s,c),f=[],d=0;d<DataArray.length;d++)p=new Uint8Array(DataArray[d]),f.push(p);return h=new Blob(f,{type:"application/pdf"}),e.next=18,ie(h);case 18:for(g=e.sent,v=ComPDFKitJS.opened_files.length,ComPDFKitJS.opened_files[v]=g,DataArray=[],Module._AddSignModifyDocument(s,c,v,g.length),f=[],_=0;_<DataArray.length;_++)A=new Uint8Array(DataArray[_]),f.push(A);return ComPDFKitJS.opened_files[0]=f,ComPDFKitJS.opened_files.splice(v,1),m=new Blob(f,{type:"application/pdf"}),e.abrupt("return",m);case 29:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()),e.on("CreateSignatureAP",function(){var e=i(n().mark((function e(t){var r,o,a,i,u,l,c,s,f,d,p;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:for(r=t.rect,o=Module._InitDocument(),Module._CreateDocument(o),Module._InsertPage(o,0,595,842),Module._GetPageCount(o),a=Module._GetPage(o,0),Fe({annotPtr:i=Module._CreateWidget(o,a,6),date:new Date}),De({pagePtr:a,annotPtr:i,rect:r}),Module._UpdateFormAp(i),t.annotPtr=i,dt(t),u=r.width,l=r.height,s=4*(c=u*l),f=_malloc(s),d=0;d<c;d++)Module.HEAP32[f/4+d]=0;for(Module._RenderAnnot(i,u,l,f,0),p=new Uint8Array(s),d=0;d<s;d+=4)p[d]=Module.HEAPU8[f+d+2],p[d+1]=Module.HEAPU8[f+d+1],p[d+2]=Module.HEAPU8[f+d],p[d+3]=Module.HEAPU8[f+d+3];return _free(f),e.abrupt("return",p);case 22:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()),e.on("UpdateSignatureAP",function(){var e=i(n().mark((function e(t){var r,o,a,i,u,l,c;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:r=t.doc,o=t.annotPtr,a=t.rect,i=t.pagePtr,u=t.imageBase64,l=t.imageArray,De({pagePtr:i,annotPtr:o,rect:a}),c=l||u&&ue(u),ComPDFKitJS.opened_image=[],ComPDFKitJS.opened_image[0]=c,Module._RemoveAP(o),Module._SetImageStampByStream(o,0,c.length,0),Module._SetStampSignature(o),Module._WidgetRemoveFromFields(r,o);case 9:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()),e.on("TestAddSignature",function(){var e=i(n().mark((function e(t){var r,o,a,i,u,l,c,s,f,d,p,h,g,v;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=t.doc,e.next=3,ie(t.certFile);case 3:for(o=e.sent,ComPDFKitJS.opened_cert[0]=o,a=stringToNewUTF8("ComPDFKit"),i=Module._GetX509ByPKCS12Cert(0,o.length,a),u=Module._InitSignature2(annotPtr),DataArray=[],l=!!fontsJson||!!ComPDFKitJS.opened_DefFont[0],Module._SaveDocumentByStream(r,2,l),Module._AddSignSaveDocument(r,u,i),c=[],s=0;s<DataArray.length;s++)f=new Uint8Array(DataArray[s]),c.push(f);return d=new Blob(c,{type:"application/pdf"}),e.next=17,ie(d);case 17:p=e.sent,h=ComPDFKitJS.opened_files.length,ComPDFKitJS.opened_files[h]=p,g=Module._InitDocument(),v=stringToNewUTF8(""),Module._LoadDocumentByStream(g,h,p.length,v);case 23:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()),e.on("LoadCertificates",function(){var e=i(n().mark((function e(t){var r,o,a,i,u;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=t.pkcs12Buffer,o=t.password,ComPDFKitJS.opened_cert[0]=r,a=stringToNewUTF8(o),0!==(i=Module._GetX509ByPKCS12Cert(0,r.length,a))){e.next=6;break}return e.abrupt("return",!1);case 6:return e.next=8,ct(i);case 8:return u=e.sent,e.abrupt("return",u);case 10:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()),e.on("AddSignature",function(){var e=i(n().mark((function e(t){var r,o,a,i,u,l,c,s,f,d,p,h,g,v,_,A,m;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=t.doc,o=t.pagePtr.pagePtr,e.next=4,ie(t.certFile);case 4:for(a=e.sent,ComPDFKitJS.opened_cert[0]=a,i=stringToNewUTF8("ComPDFKit"),u=Module._GetPKCS12Cert(i,0,a.length),l={left:50,top:442,right:100,bottom:642},(DataArray=[]).push(ComPDFKitJS.opened_files[0]),Fe({annotPtr:c=Module._CreateWidget(r,o,6),date:new Date}),tt({annotPtr:c,fieldName:"signatureField1"}),De({pagePtr:o,annotPtr:c,rect:l}),s=Module._InitSignature2(c),Module._AddSignSaveDocument(r,s,u),f=[],d=0;d<DataArray.length;d++)p=new Uint8Array(DataArray[d]),f.push(p);return h=new Blob(f,{type:"application/pdf"}),e.next=22,ie(h);case 22:for(g=e.sent,v=ComPDFKitJS.opened_files.length,ComPDFKitJS.opened_files[v]=g,DataArray=[],Module._AddSignModifyDocument(s,u,1,ComPDFKitJS.opened_files[1].length),f=[],_=0;_<DataArray.length;_++)A=new Uint8Array(DataArray[_]),f.push(A);return m=new Blob(f,{type:"application/pdf"}),e.abrupt("return",m);case 31:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()),e.on("PushRenderTask",(function(e){var t=e.pagePtr,n=e.scale,r=e.left,o=e.right,a=e.bottom,i=e.top,u=e.mode,l=void 0===u?-1:u,c=e.flag,s=void 0===c?0:c,f=e.form,d=function(e){for(var t=e.pagePtr,n=e.scale,r=e.left,o=e.right,a=e.bottom,i=e.top,u=e.mode,l=e.flag,c=e.form,s=parseInt(o-r)*parseInt(a-i),f=4*s,d=_malloc(f),p=0;p<s;p++)Module.HEAP32[d/4+p]=0;Module._RenderPageBitmapWithMatrix(t,n,r,o,a,i,u,d,l,c);var h=Re("PDF VIEWER RENDER");if(h.code)return _free(d),h;var g=new Uint8Array(f);for(p=0;p<f;p+=4)g[p]=Module.HEAPU8[d+p+2],g[p+1]=Module.HEAPU8[d+p+1],g[p+2]=Module.HEAPU8[d+p],g[p+3]=Module.HEAPU8[d+p+3];return _free(d),{imageArray:g}}({pagePtr:t,scale:n,left:r,right:o,bottom:a,top:i,mode:l,flag:s,form:void 0===f?0:f});return d})),e.on("RenderPageBitmap",(function(e){var t=function(e){for(var t=e.pagePtr,n=e.x,r=e.y,o=e.width,a=e.height,i=e.mode,u=e.flag,l=e.form,c=parseInt(o)*parseInt(a),s=4*c,f=_malloc(s),d=0;d<c;d++)Module.HEAP32[f/4+d]=0;Module._RenderPageBitmap(t,n,r,o,a,i,f,u,l);var p=Re("PDF VIEWER RENDER");if(p.code)return _free(f),p;var h=new Uint8Array(s);for(d=0;d<s;d+=4)h[d]=Module.HEAPU8[f+d+2],h[d+1]=Module.HEAPU8[f+d+1],h[d+2]=Module.HEAPU8[f+d],h[d+3]=Module.HEAPU8[f+d+3];return _free(f),{imageArray:h}}({pagePtr:e.pagePtr,x:e.x,y:e.y,width:e.width,height:e.height,mode:-1,flag:1,form:1});return t})),e.on("EditAnnotation",(function(e){var t=e.annotation;if(t.rect&&De(t),t.color&&(t.type&&"freetext"===t.type&&lt(t),t.type&&["highlight","underline","squiggly","strikeout"].includes(t.type)&&Ne({annotPtr:t.annotPtr,borderColor:t.color})),"customData"in t&&We(t),isFinite(t.opacity)&&He(t),"contents"in t&&"mod-form"===t.operate){var n=it(t);if(n)return n}if("state"in t){var r=$[t.state.toUpperCase()];Module._SetState(t.annotPtr,r)}if("contents"in t&&"mod-annot"===t.operate&&(t.type&&"freetext"===t.type?lt(t):Le(t)),"fillColor"in t&&"mod-annot"===t.operate&&(t.type&&"freetext"===t.type?Oe(t):je({annotPtr:t.annotPtr,fillColor:t.fillColor})),"borderWidth"in t&&Ye({annotPtr:t.annotPtr,borderWidth:t.borderWidth}),t.borderColor&&"mod-annot"===t.operate&&(t.type&&"freetext"===t.type?ke(t):Ne({annotPtr:t.annotPtr,borderColor:t.borderColor})),t.borderColor&&"mod-form"===t.operate&&function(e){var t=e.annotPtr,n=e.borderColor,r=G(n);Module._SetWidgetBorderRGBColor(t,r.R,r.G,r.B)}(t),"arcPoints"in t&&vt({pagePtr:t.pagePtr,annotPtr:t.annotPtr,vertices:t.arcPoints}),t.linePoints&&qe(t),t.inkPointes&&Ke(t),t.destPage&&"mod-annot"===t.operate&&Je(t),t.destPage&&"mod-form"===t.operate&&ot(t),t.url&&"mod-annot"===t.operate&&Xe(t),t.url&&"mod-form"===t.operate&&at(t),(t.isHidden||0===t.isHidden)&&function(e){var t=e.annotPtr,n=e.isHidden,r=0;r=1==n?2:4;var o=Module._SetAnnotFlag(t,r)}(t),"required"in t&&function(e){var t=e.annotPtr,n=e.required;Module._SetRequiredFlag(t,n)}(t),"isChecked"in t)if("checkbox"===t.type){var o=Ze(t);if(o)return o}else if("radiobutton"===t.type){var a=Ve(t);if(a)return a}if("checkStyle"in t&&$e(t),t.fillColor&&"mod-form"===t.operate&&et(t),t.title&&"mod-form"===t.operate){var i=t.annotPtr,u=t.title,l=stringToNewUTF8(u);Module._SetButtonTitle(i,l)}if(t.fieldName&&tt(t),t.fontName&&"freetext"!==t.type&&nt({annotPtr:t.annotPtr,fontName:t.fontName,fontSize:t.fontSize,fontStyle:t.fontStyle,fontWeight:t.fontWeight,color:t.color}),t.item&&ut(t),t.items)for(var c=t.items,s=0;s<c.length;s++)ut({annotPtr:t.annotPtr,item:{string:c[s].String,value:c[s].Value,index:s}});if(t.moveOption&&function(e){var t=e.annotPtr,n=e.moveOption,r=n.fromIndex,o=n.toIndex,a=n.item;(function(e){var t=e.annotPtr,n=e.index;Module._RemoveOptionItem(t,n)})({annotPtr:t,index:r}),ut({annotPtr:t,item:{string:a.String,value:a.Value,index:o}})}(t),t.hasOwnProperty("deleteItemIndex")&&function(e){var t=e.annotPtr,n=e.deleteItemIndex;if(Module._RemoveOptionItem(t,n),0===Module._GetItemsCount(t))return;rt({annotPtr:t,selected:0})}(t),t.textAlignment&&"mod-form"===t.operate){var f=t.annotPtr,d=t.textAlignment,p=L[d.toUpperCase()];Module._SetWidgetTextJustification(f,p)}if("selected"in t){var h=rt(t);if(h)return h}if(t.hasOwnProperty("isMultiLine")){var g=t.annotPtr,v=t.isMultiLine;Module._SetMultiLine(g,v)}t.author&&Be(t),t.fillTransparency&&Qe(t),t.quadPoints&&ze(t),(t.borderStyle||t.dashes)&&("mod-annot"===t.operate?_t(t):function(e){var t=e.annotPtr,n=e.borderStyle,r=J[n];Module._SetWidgetBorderStyle(t,r)}(t)),t.vertices&&vt(t),null==t.head&&null==t.tail||Module._SetLineType(t.annotPtr,Q[t.head.toUpperCase()],Q[t.tail.toUpperCase()]);var _=Module._GetPageRotation(t.pagePtr);"mod-annot"===t.operate?Module._UpdateAnnotAp(t.annotPtr,_):"mod-form"===t.operate&&"signatureFields"!==t.type&&Module._UpdateFormAp(t.annotPtr,_)})),e.on("FlattenPage",(function(e){var t=function(e){return Ge.apply(this,arguments)}({doc:e.doc,saveType:e.saveType,password:e.password,oldPassword:e.oldPassword});return t}));var t=["textfield","checkbox","radiobutton","listbox","combobox","pushbutton","signatureFields"];e.on("CreateAnnotation",(function(e){var n=e.doc,r=e.pagePtr,o=e.annotation;return o.name=o.name||P(),"add-form"===o.operate||o.type&&t.includes(o.type)?function(e,t,n){var r=n.type,o=n.date,a=n.fieldName,i=n.fillColor,u=n.checkStyle,l=n.rect,c=O[r.toUpperCase()],s=Module._CreateWidget(e,t,c),f=Re("PDF FORM");if(f.code)return f;n.annotPtr=s,"customData"in n&&We(n);n.name&&Module._SetAnnotDictName(s,stringToNewUTF8("NM"),stringToNewUTF8(n.name));"author"in n&&Be({annotPtr:s,author:n.author});o&&Fe({annotPtr:s,date:o});i&&et({annotPtr:s,fillColor:i});u&&$e({annotPtr:s,checkStyle:u});a&&tt({annotPtr:s,fieldName:a});var d=n.borderColor,p=n.borderWidth,h=n.borderStyle,g=n.isChecked,v=n.color,_=n.fontName,A=n.fontSize;(d||h||"borderWidth"in n)&&function(e){var t=e.annotPtr,n=e.borderColor,r=e.borderWidth,o=e.borderStyle;if(n){var a=G(n);Module._SetWidgetBorderRGBColor(t,a.R,a.G,a.B)}if(o){var i=J[o];Module._SetWidgetBorderStyle(t,i)}(0===r||r)&&Ye({annotPtr:t,borderWidth:r})}({annotPtr:s,borderColor:d,borderWidth:p,borderStyle:h});switch(r){case"checkbox":(g||0===g)&&Ze({annotPtr:s,isChecked:g});break;case"radiobutton":(g||0===g)&&Ve({annotPtr:s,isChecked:g});break;case"listbox":case"combobox":for(var m=n.items,y=n.selected,P=0;P<m.length;P++)ut({annotPtr:s,item:{string:m[P].String,value:m[P].Value,index:P}});nt({annotPtr:s,fontName:_,fontSize:A,color:v}),(y||0===y)&&rt({annotPtr:s,selected:y});break;case"pushbutton":nt({annotPtr:s,fontName:_,fontSize:A,color:v});var M=n.title,w=stringToNewUTF8(M);Module._SetButtonTitle(s,w),n.destPage&&ot(n),n.url&&at(n);break;case"textfield":var x=n.isMultiLine,I=n.textAlignment,b=L[I.toUpperCase()];Module._SetMultiLine(s,x),Module._SetWidgetTextJustification(s,b),Module._SetTextFieldSpe(s,0),"contents"in n&&it({annotPtr:s,contents:n.contents}),nt({annotPtr:s,fontName:_,fontSize:A,color:v})}if(De({pagePtr:t,annotPtr:s,rect:l}),Module._UpdateFormAp(s),"signatureFields"===r){var S=n.imageBase64;if(S){var C=ue(S);ComPDFKitJS.opened_image=[],ComPDFKitJS.opened_image[0]=C,Module._RemoveAP(s),Module._SetImageStampByStream(s,0,C.length,0),Module._SignWithName(s)}}return{annotPtr:s}}(n,r,o):function(e,t,n){var r=n.type,o=N[r.toUpperCase()],a=Module._CreateAnnot(e,t,o),i=Re("PDF Annotation");if(i.code)return i;n.annotPtr=a;var u=n.color,l=n.fontName,c=n.fontWeight,s=n.fontStyle,f=n.borderColor,d=n.borderWidth,p=n.contents,h=n.date,g=n.opacity,v=n.fillTransparency,_=n.rect,A=n.author,m=n.fillColor,y=n.quadPoints,P=n.vertices,M=n.borderStyle,w=n.dashes;u&&l&&c&&s&&nt(n);n.name&&Module._SetAnnotDictName(a,stringToNewUTF8("NM"),stringToNewUTF8(n.name));"customData"in n&&We(n);f&&"freetext"!==r&&Ne({annotPtr:a,borderColor:f});m&&"freetext"!==r&&je({annotPtr:a,fillColor:m});p&&"freetext"!==r&&Le({annotPtr:a,contents:p});Fe({annotPtr:a,date:h}),isFinite(g)&&He({annotPtr:a,opacity:g});"fillTransparency"in n?Qe({annotPtr:a,fillTransparency:v}):m&&Qe({annotPtr:a,fillTransparency:1});_&&De({pagePtr:t,annotPtr:a,rect:_});A&&Be({annotPtr:a,author:A});y&&ze({pagePtr:t,annotPtr:a,quadPoints:y});"borderWidth"in n&&Ye({annotPtr:a,borderWidth:d});(M||w)&&_t({annotPtr:a,borderWidth:d,borderStyle:M,dashes:w});switch(o){case N.TEXT:break;case N.LINK:n.destPage&&Je(n),n.url&&Xe(n);break;case N.FREETEXT:var x=n.color,I=void 0===x?"rgb(0, 0, 0":x,b=n.fontSize,S=void 0===b?16:b,C=n.fontName,T=void 0===C?"Helvetica":C,E=n.fontStyle,R=void 0===E?"normal":E,F=n.fontWeight,D=void 0===F?"normal":F,B=n.textAlignment,U=void 0===B?"left":B;n.borderWidth;var k=n.borderColor,O=void 0===k?"rgb(0, 0, 0)":k,j=n.fillColor,W=void 0===j?"":j,H=n.contents;!function(e){var t=e.annotPtr,n=e.fontName,r=e.fontWeight,o=e.fontStyle,a=e.fontSize,i=e.color,u=e.textAlignment,l=e.contents,c=we({fontName:n,fontWeight:r,fontStyle:o}),s=L[u.toUpperCase()],f=stringToNewUTF8(c),d=stringToNewUTF8(l),p=G(i);Module._SetFreeText(t,f,a,p.R,p.G,p.B,s,d),Module._SetFreeTextType(t,0),Le(e)}({annotPtr:a,color:I,fontName:T,fontSize:S,fontStyle:R,fontWeight:D,textAlignment:U,contents:void 0===H?"":H}),"fillColor"in n&&Oe({annotPtr:a,fillColor:W}),"borderColor"in n&&ke({annotPtr:a,borderColor:O});break;case N.POLYLINE:case N.POLYGON:P&&vt({pagePtr:t,annotPtr:a,vertices:P});break;case N.LINE:var Y=n.head,z=n.tail,q=n.linePoints;null==Y&&null==z||Module._SetLineType(a,Q[Y.toUpperCase()],Q[z.toUpperCase()]),qe({pagePtr:t,annotPtr:a,linePoints:q});break;case N.SQUARE:case N.CIRCLE:!function(e){var t=e.annotPtr,n=e.borderWidth;Ye({annotPtr:t,borderWidth:n})}({annotPtr:a,borderWidth:d});break;case N.HIGHLIGHT:case N.UNDERLINE:case N.SQUIGGLY:case N.STRIKEOUT:var K=n.quadPoints;!function(e){var t=e.pagePtr,n=e.annotPtr,r=e.color,o=e.quadPoints;Ne({annotPtr:n,borderColor:r}),ze({pagePtr:t,annotPtr:n,quadPoints:o})}({pagePtr:t,annotPtr:a,color:n.color,quadPoints:K});break;case N.STAMP:var J=n.stampType,X=Module._GetPageRotation(t);if("standard"===J){var Z=function(e,t){var n=e.annotPtr,r=e.contents,o=stringToNewUTF8(r.replace(/ /g,""));Module._SetStandardStamp(n,o,t);var a=Re("PDF Annotation STAMPS");if(a.code)return a}({annotPtr:a,contents:p},X);if(Z)return Z}else if("text"===J){n.annotPtr=a;var V=function(e,t){var n=e.annotPtr,r=e.contents,o=e.time,a=e.stampShape,i=e.stampColor,u=stringToNewUTF8(r),l=stringToNewUTF8(o);Module._SetTextStamp(n,u,l,a,i,t);var c=Re("PDF Annotation STAMPS");if(c.code)return c}(n,X);if(V)return V}else if("image"===J){n.annotPtr=a,n.doc=e;var $=function(e,t){var n=e.annotPtr,r=e.imageBase64;e.stampShape,e.rect,e.doc;var o=ue(r);Le({annotPtr:n,contents:"image"}),ComPDFKitJS.opened_image=[],ComPDFKitJS.opened_image[0]=o,Module._SetImageStampByStream(n,0,o.length,t);var a=Re("PDF Annotation STAMPS");if(a.code)return a}(n,X);if($)return $}break;case N.INK:var ee=n.inkPointes,te=n.borderColor;te&&Ne({annotPtr:a,borderColor:te}),Ke({pagePtr:t,annotPtr:a,inkPointes:ee});break;case N.ARC:vt({pagePtr:t,annotPtr:a,vertices:n.arcPoints})}_&&De({pagePtr:t,annotPtr:a,rect:_});var ne=Module._GetPageRotation(t);return Module._UpdateAnnotAp(n.annotPtr,ne),{annotPtr:a}}(n,r,o)})),e.on("GetAnnotRect",(function(e){var t=e.pagePtr,n=e.annotPtr;return Rect={},Module._GetAnnotRect(t,n),{left:Rect.Left,top:Rect.Top,right:Rect.Right,bottom:Rect.Bottom}})),e.on("SetAnnotRect",(function(e){var t=e.pagePtr,n=e.annotPtr,r=e.rect;Module._SetAnnotRect(t,n,r.left,r.right,r.bottom,r.top)})),e.on("RemoveAnnot",(function(e){var t=e.annotPtr;Module._RemoveAnnot(t);var n=Re("PDF Annotation");if(n.code)return n})),e.on("RemoveFromPageIncludeReplyAnnot",(function(e){var t=e.pagePtr,n=e.annotPtr;Module._RemoveFromPageIncludeReplyAnnot(t,n);var r=Re("PDF Annotation");if(r.code)return r})),e.on("GetCharIndexAtPos",(function(e){var t=e.pagePtr,n=e.textPtr,r=e.point,o=r.x,a=r.y;return-1!==Module._GetCharIndexAtPos(t,n,o,a,5,5)})),e.on("GetCharsRangeAtPos",(function(e){var t=e.pagePtr,n=e.textPtr,r=e.start,o=e.end;return PDFRange={},Module._GetCharsRangeAtPos(t,n,r.x,r.y,o.x,o.y,5,5),U8StringData="",Module._GetTextContent(n,PDFRange.Location,PDFRange.Length),TextRectArray=[],Module._GetRects(t,n,PDFRange.Location,PDFRange.Length),{textContent:U8StringData,textRects:TextRectArray}})),e.on("GetSelectionForWordAtPos",(function(e){var t=e.pagePtr,n=e.textPtr,r=e.start;return e.end,Rect={},PDFRange={},U8StringData="",Module._GetSelectionForWordAtPos(t,n,r.x,r.y,5,5),TextRectArray=[],Module._GetRects(t,n,PDFRange.Location,PDFRange.Length),{textContent:U8StringData,textRects:TextRectArray}})),e.on("XFDFExportAnnotations",(function(e){var t=function(e){XFDFData={},Module._XFDFExportAnnotations(e);var t=Re("PDF Annotation XFDF");if(t.code)return t;return XFDFData}(e.doc);return t})),e.on("XFDFImportAnnotations",(function(e){var t=e.doc,n=e.xfdfBuffer,r=_malloc(n.length);Module.HEAPU8.set(n,r),Module._XFDFImportAnnotaitons(t,r,n.length),_free(r);var o=Re("PDF Annotation XFDF");if(o.code)return o})),e.on("XFDFExportFormAnnotations",(function(e){var t=function(e){XFDFData={},Module._ExportCustomWidgets(e);var t=Re("PDF XFDF");if(t.code)return t;return XFDFData.Content}(e.doc);return t})),e.on("SaveDocumentByStream",(function(e){return Te(e)})),e.on("SetPassword",(function(e){var t=e.doc,n=e.password,r=stringToNewUTF8(n);Module._SetPassword(t,r,"","","","","","")})),e.on("CompareDocument",function(){var e=i(n().mark((function e(t){var r,o,a,i,u,l,c,s,f,d,p,h,g,v,_,A,m,y,P,M,w,x,I,b,S,C,T,E,R,G,F,D,B,U,L,N,k,O,j,W,H,Y,Q,z,q,K;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=t.data,o=r.leftFile,a=r.rightFile,i=r.leftFilePassword,u=r.rightFilePassword,e.next=4,ie(o);case 4:return l=e.sent,c=Module._InitDocument(),ComPDFKitJS.opened_files[1]=l,s=stringToNewUTF8(i),Module._LoadDocumentByStream(c,1,l.length,s),f=Module._GetPageCount(c),e.next=12,ie(a);case 12:if(d=e.sent,p=Module._InitDocument(),ComPDFKitJS.opened_files[2]=d,h=stringToNewUTF8(u),Module._LoadDocumentByStream(p,2,d.length,h),g=Module._GetPageCount(p),v=Math.min(f,g),1!==r.type){e.next=58;break}_=r.replaceColor,A=r.insertColor,m=r.deleteColor,y=r.textCompare,P=r.imgCompare,M=Module._InitCompare(c,p),w=0,y&&P?w=2:P&&(w=1),x=de(_),I=de(A),b=de(m),Module._SetReplaceResColor(M,255*x.R,255*x.G,255*x.B),Module._SetInsertResColor(M,255*I.R,255*I.G,255*I.B),Module._SetDeleteResColor(M,255*b.R,255*b.G,255*b.B),Module._SetReplaceResTransparency(M,127.5),Module._SetInsertResTransparency(M,127.5),Module._SetDeleteResTransparency(M,127.5),S=0;case 34:if(!(S<v)){e.next=47;break}if(C=Module._DoCompare(M,S,S,w,1),!(T=Re("PDF COMPARE"))||!T.code){e.next=39;break}return e.abrupt("return",T);case 39:Module._GetCompareResultsCount(C,1),CompareResultArray=[],Module._GetCompareResults(c,p,C,1),ResultsCountParams={},Module._GetResultsCountParams(C);case 44:S++,e.next=34;break;case 47:for(DataArray=[],Module._GenerateNewDoc(M),E=[],R=0;R<DataArray.length;R++)G=new Uint8Array(DataArray[R]),E.push(G);return DataArray=[],F=new Blob(E,{type:"application/pdf"}),D=Ce(c,2),B=Ce(p,2),e.abrupt("return",{oldDocument:D,newDocument:B,blobData:F});case 58:if(2!==r.type){e.next=82;break}if(U=r.inColor,L=r.newColor,N=r.inTransparency,k=r.newTransparency,O=r.coverType,j=Module._InitCompareDrawing(c,p),Module._SetBlendMode(j,O),Module._SetNoFill(j,1),W=N/100,H=k/100,Module._SetStrokeAlphaCompare(j,W),Module._SetStrokeAlphaCompared(j,H),Module._SetFillAlphaCompare(j,W),Module._SetFillAlphaCompared(j,H),Y=de(U),Q=de(L),Module._SetColorCompare(j,255*Y.R,255*Y.G,255*Y.B),Module._SetColorCompared(j,255*Q.R,255*Q.G,255*Q.B),Module._CompareContent(j),!(z=Re("PDF COMPARE"))||!z.code){e.next=77;break}return e.abrupt("return",z);case 77:return q=Module._GetDocGenerate(j),e.next=80,Te({doc:q});case 80:return K=e.sent,e.abrupt("return",{blobData:K});case 82:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()),e.on("GetOutlines",(function(e){var t=e.doc,n=[];return _e(t,Module._GetOutlineRoot(t),n,-1),n})),e.on("InitText",(function(e){var t=e.pagePtr;return Module._InitText(t)})),e.on("ClearText",(function(e){var t=e.textPtr;Module._ClearText(t)})),e.on("ClearPage",(function(e){var t=e.pagePtr;Module._ClearPage(t)})),e.on("Search",(function(e){for(var t=e.pagesPtr,n=e.value,r=stringToNewUTF8(n),o=null,a=[],i=0;i<t.length;i++){var u=t[i],l=u.pageNumber,c=u.pagePtr,s=u.textPtr,f=Module._InitTextFind();if(Module._FindStart(s,f,r,0,0),(o=Re("PDF VIEWER SEARCH")).code)return o;var d=1,p=0;do{if(TextFindItemArray=[],d=Module._FindNext(c,s,f)){for(var h=TextFindItemArray.length,g=[],v=0;v<h;v++){var _=TextFindItemArray[v],A=_.Left,m=_.Top,y=_.Right,P=_.Bottom;g.push({left:A,top:m,right:y,bottom:P})}a.push({pageNum:l,quads:g,content:TextFindItemArray[0].Content.replace(/\r?\n/gm," "),pageSearchIndex:p,searchValue:n}),p++}}while(d);Module._ClearTextFind(f)}return{searchResults:a}})),e.on("InitEditPage",(function(e){var t=e.pagePtr;return{editPagePtr:Module._InitEditPage(t),pagePtr:t}})),e.on("ClearEditPage",(function(e){Module._ClearEditPage(e)})),e.on("BeginEdit",(function(e){var t=e.editPagePtr,n=e.type;return Module._BeginEdit(t,n),Re("PDF CONTENT EDIT")})),e.on("EndEdit",(function(e){Module._EndEdit(e)})),e.on("GetEditAreaCount",(function(e){return Module._GetEditAreaCount(e)})),e.on("GetEditArea",(function(e){var t=e.editPagePtr,n=e.index;return Module._GetEditArea(t,n)})),e.on("IsTextArea",(function(e){return Module._IsTextArea(e)})),e.on("IsImageArea",(function(e){return Module._IsImageArea(e)})),e.on("SetFrame",(function(e){var t=e.pagePtr,n=e.editAreaPtr,r=e.rect;Module._SetFrame(t,n,r.left,r.right,r.bottom,r.top,!0)})),e.on("GetFrame",(function(e){var t=e.pagePtr,n=e.editAreaPtr;return Rect={},Module._GetFrame(t,n),{left:Rect.Left,top:Rect.Top,right:Rect.Right,bottom:Rect.Bottom}})),e.on("GetBeginAndEndCharPlace",(function(e){return EditCharPlace={},EndEditCharPlace={},Module._GetBeginAndEndCharPlace(e),{start:EditCharPlace,end:EndEditCharPlace}})),e.on("GetTextByRange",(function(e){var t=e.editAreaPtr,n=e.start,r=e.end;return Module._GetTextByRange(t,n.SectionIndex,n.LineIndex,n.RunIndex,n.CharIndex,r.SectionIndex,r.LineIndex,r.RunIndex,r.CharIndex),U8StringData})),e.on("GetText",function(){var e=i(n().mark((function e(t){return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return EditCharPlace={},EndEditCharPlace={},e.next=4,Module._GetBeginAndEndCharPlace(t);case 4:return e.next=6,Module._GetTextByRange(t,EditCharPlace.SectionIndex,EditCharPlace.LineIndex,EditCharPlace.RunIndex,EditCharPlace.CharIndex,EndEditCharPlace.SectionIndex,EndEditCharPlace.LineIndex,EndEditCharPlace.RunIndex,EndEditCharPlace.CharIndex);case 6:return e.abrupt("return",U8StringData);case 7:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()),e.on("SelectCharsRangeAtPos",(function(e){var t=e.pagePtr,n=e.editAreaPtr,r=e.startPoint,o=e.endPoint;return EditCharPlace={},EndEditCharPlace={},Module._SelectCharsRangeAtPos(t,n,r.x,r.y,o.x,o.y),{start:EditCharPlace,end:EndEditCharPlace}})),e.on("GetSectionAtPos",(function(e){var t=e.pagePtr,n=e.editAreaPtr,r=e.pointX,o=e.pointY;return EditCharPlace={},EndEditCharPlace={},Module._GetSectionAtPos(t,n,r,o),{start:EditCharPlace,end:EndEditCharPlace}})),e.on("GetTextCursorPoints",(function(e){var t=e.pagePtr,n=e.editAreaPtr,r=e.char;return CursorPoints={},Module._GetTextCursorPoints(t,n,r.SectionIndex,r.LineIndex,r.RunIndex,r.CharIndex),CursorPoints})),e.on("GetPrevCharPlace",(function(e){var t=e.editAreaPtr,n=e.char;return EditCharPlace={},EndEditCharPlace={},Module._GetPrevCharPlace(t,n.SectionIndex,n.LineIndex,n.RunIndex,n.CharIndex),{start:EditCharPlace,end:EndEditCharPlace}})),e.on("GetNextCharPlace",(function(e){var t=e.editAreaPtr,n=e.char;return EditCharPlace={},EndEditCharPlace={},Module._GetNextCharPlace(t,n.SectionIndex,n.LineIndex,n.RunIndex,n.CharIndex),{start:EditCharPlace,end:EndEditCharPlace}})),e.on("GetUpCharPlace",(function(e){var t=e.editAreaPtr,n=e.char;return EditCharPlace={},EndEditCharPlace={},Module._GetUpCharPlace(t,n.SectionIndex,n.LineIndex,n.RunIndex,n.CharIndex),{start:EditCharPlace,end:EndEditCharPlace}})),e.on("GetDownCharPlace",(function(e){var t=e.editAreaPtr,n=e.char;return EditCharPlace={},EndEditCharPlace={},Module._GetDownCharPlace(t,n.SectionIndex,n.LineIndex,n.RunIndex,n.CharIndex),{start:EditCharPlace,end:EndEditCharPlace}})),e.on("InsertText",(function(e){var t=e.editAreaPtr,n=e.char,r=e.text;EditCharPlace={};var o=stringToNewUTF8(r);return Module._InsertText(t,n.SectionIndex||0,n.LineIndex||0,n.RunIndex||0,n.CharIndex||0,o,!0),EditCharPlace})),e.on("BackSpaceChar",(function(e){var t=e.editAreaPtr,n=e.char;return EditCharPlace={},Module._BackSpaceChar(t,n.SectionIndex,n.LineIndex,n.RunIndex,n.CharIndex,!0),EditCharPlace})),e.on("DeleteChar",(function(e){var t=e.editAreaPtr,n=e.char;return EditCharPlace={},Module._DeleteChar(t,n.SectionIndex,n.LineIndex,n.RunIndex,n.CharIndex,!0),EditCharPlace})),e.on("ReplaceText",(function(e){var t=e.editAreaPtr,n=e.start,r=e.end,o=e.text;EditCharPlace={},Module._DeleteChars(t,n.SectionIndex||0,n.LineIndex||0,n.RunIndex||0,n.CharIndex||0,r.SectionIndex||0,r.LineIndex||0,r.RunIndex||0,r.CharIndex||0,!0);var a=stringToNewUTF8(o);Module._InsertText(t,EditCharPlace.SectionIndex||0,EditCharPlace.LineIndex||0,EditCharPlace.RunIndex||0,EditCharPlace.CharIndex||0,a,!0)})),e.on("ReplaceAll",(function(e){var t=e.editAreaPtr,n=e.text;EditCharPlace={},EndEditCharPlace={},Module._GetBeginAndEndCharPlace(t),Module._DeleteChars(t,EditCharPlace.SectionIndex||0,EditCharPlace.LineIndex||0,EditCharPlace.RunIndex||0,EditCharPlace.CharIndex||0,EndEditCharPlace.SectionIndex||0,EndEditCharPlace.LineIndex||0,EndEditCharPlace.RunIndex||0,EndEditCharPlace.CharIndex||0,!0);var r=stringToNewUTF8(n);Module._InsertText(t,EditCharPlace.SectionIndex||0,EditCharPlace.LineIndex||0,EditCharPlace.RunIndex||0,EditCharPlace.CharIndex||0,r,!0)})),e.on("DeleteChars",(function(e){var t=e.editAreaPtr,n=e.start,r=e.end;return EditCharPlace={},Module._DeleteChars(t,n.SectionIndex||0,n.LineIndex||0,n.RunIndex||0,n.CharIndex||0,r.SectionIndex||0,r.LineIndex||0,r.RunIndex||0,r.CharIndex||0,!0),EditCharPlace})),e.on("GetSelectLineRectsCounts",(function(e){var t=e.editAreaPtr,n=e.start,r=e.end;return Module._GetSelectLineRectsCounts(t,n.SectionIndex,n.LineIndex,n.RunIndex,n.CharIndex,r.SectionIndex,r.LineIndex,r.RunIndex,r.CharIndex)})),e.on("GetSelectLineRects",(function(e){var t=e.pagePtr,n=e.editAreaPtr,r=e.start,o=e.end;return RectArray=[],Module._GetSelectLineRects(t,n,r.SectionIndex,r.LineIndex,r.RunIndex,r.CharIndex,o.SectionIndex,o.LineIndex,o.RunIndex,o.CharIndex),RectArray})),e.on("SetCharsFontColor",(function(e){var t=e.editAreaPtr,n=e.start,r=e.end,o=function(e){var t=R(e);if("hex"===t)return D(e);if("rgb"===t){var n=e.match(/(\d+),\s*(\d+),\s*(\d+)/);return n?{R:parseInt(n[1]),G:parseInt(n[2]),B:parseInt(n[3])}:{error:"Invalid RGB format"}}if("hsl"===t){var r=e.match(/hsl\((\d+),\s*(\d+)%,\s*(\d+)%\)/);if(!r)return{error:"Invalid HSL format"};var o=parseInt(r[1]),a=parseInt(r[2])/100,i=parseInt(r[3])/100;return B(o,a,i)}console.error({message:"Invalid color value"})}(e.color);Module._SetCharsFontColor(t,n.SectionIndex||0,n.LineIndex||0,n.RunIndex||0,n.CharIndex||0,r.SectionIndex||0,r.LineIndex||0,r.RunIndex||0,r.CharIndex||0,o.R,o.G,o.B,!0)})),e.on("SetCharsFontTransparency",(function(e){var t=e.editAreaPtr,n=e.start,r=e.end,o=e.opacity;Module._SetCharsFontTransparency(t,n.SectionIndex,n.LineIndex,n.RunIndex,n.CharIndex,r.SectionIndex,r.LineIndex,r.RunIndex,r.CharIndex,o,!0)})),e.on("SetTextAligningSection",(function(e){var t=e.editAreaPtr,n=e.char,r=e.alignType;Module._SetTextAligningSection(t,r,n.SectionIndex,n.LineIndex,n.RunIndex,n.CharIndex,!0)})),e.on("SetTextAligningRange",(function(e){var t=e.editAreaPtr,n=e.start,r=e.end,o=e.alignType;Module._SetTextAligningRange(t,o,n.SectionIndex,n.LineIndex,n.RunIndex,n.CharIndex,r.SectionIndex,r.LineIndex,r.RunIndex,r.CharIndex,!0)})),e.on("SetCharsFontSize",(function(e){var t=e.editAreaPtr,n=e.start,r=e.end,o=e.fontSize;Module._SetCharsFontSize(t,n.SectionIndex,n.LineIndex,n.RunIndex,n.CharIndex,r.SectionIndex,r.LineIndex,r.RunIndex,r.CharIndex,o,!0,!0)})),e.on("SetFontFromNativeTrueTypeFont",(function(e){var t=e.editAreaPtr,n=e.start,r=e.end,o=e.fontFamily,a=stringToNewUTF8(o);Module._SetFontFromNativeTrueTypeFont(t,n.SectionIndex,n.LineIndex,n.RunIndex,n.CharIndex,r.SectionIndex,r.LineIndex,r.RunIndex,r.CharIndex,a,!0)})),e.on("RefreshRange",(function(e){var t=e.editAreaPtr,n=e.start,r=e.end;return EditCharPlace={},EndEditCharPlace={},Module._RefreshRange(t,n.SectionIndex,n.LineIndex,n.RunIndex,n.CharIndex,r.SectionIndex,r.LineIndex,r.RunIndex,r.CharIndex),{start:EditCharPlace,end:EndEditCharPlace}})),e.on("SetCharsFontStyle",function(){var e=i(n().mark((function e(t){var r,o,a,i;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:r=t.editAreaPtr,o=t.start,a=t.end,i=t.fontStyle,Module["_"+i](r,o.SectionIndex,o.LineIndex,o.RunIndex,o.CharIndex,a.SectionIndex,a.LineIndex,a.RunIndex,a.CharIndex,!0);case 3:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()),e.on("CreateNewTextArea",(function(e){var t=e.pagePtr,n=e.editPagePtr,r=e.rect,o=e.fontData,a=e.alignType;return Module._CreateNewTextArea(t,n,r.left,r.right,r.bottom,r.top,stringToNewUTF8(o.fontName),o.fontSize,o.r,o.g,o.b,o.opacity,o.isBold,o.italic,a,!0)})),e.on("RemoveEditAreaByIndex",(function(e){var t=e.editPagePtr,n=e.editAreaIndex;Module._RemoveEditAreaByIndex(t,n,!0)})),e.on("RemoveEditArea",(function(e){var t=e.editPagePtr,n=e.editAreaPtr;Module._RemoveEditArea(t,n,!0)})),e.on("CopyArea",(function(e){var t=e.editPagePtr,n=e.editAreaPtr;return Module._CopyArea(t,n)})),e.on("PasteArea",(function(e){var t=e.pagePtr,n=e.editPagePtr,r=e.editAreaInfoPtr,o=e.rect,a=n;n||(a=Module._InitEditPage(t),Module._BeginEdit(a,3));var i=Module._PasteArea(t,a,r,o.left+5,o.top+5,!0);return n||(Module._EndEdit(a),Module._ClearEditPage(a)),i})),e.on("GetCopyAreaContent",(function(e){var t=e.editPagePtr,n=e.editAreaInfoPtr;return Module._GetCopyAreaContent(t,n),U8StringData})),e.on("GetTextStyle",(function(e){var t=e.editAreaPtr,n=e.char;return EditTextStyle={},Module._GetTextStyle(t,n.SectionIndex,n.LineIndex,n.RunIndex,n.CharIndex),EditTextStyle})),e.on("GetBaseFontName",(function(e){var t=e.editAreaPtr,n=e.char;return Module._GetBaseFontName(t,n.SectionIndex,n.LineIndex,n.RunIndex,n.CharIndex),U8StringData})),e.on("createEditorDoc",function(){var e=i(n().mark((function e(t){var r,o;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=t.doc,o=t.password,e.next=3,be(r,o);case 3:return e.abrupt("return",e.sent);case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()),e.on("GetTextSectionAlignType",function(){var e=i(n().mark((function e(t){var r,o;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=t.editAreaPtr,o=t.char,e.abrupt("return",Module._GetTextSectionAlignType(r,o.SectionIndex,o.LineIndex,o.RunIndex,o.CharIndex));case 2:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()),e.on("InsertPage",(function(e){var t=e.doc,n=e.index,r=e.width,o=e.height,a=Module._InsertPage(t,n,r,o),i=Re("PDF EDITOR PAGE");return i.code?i:{status:a}})),e.on("ImportPagesAtIndex",function(){var e=i(n().mark((function e(t){var r,o,a,i,u,l,c,s,d,p,h,g,v,_,A,m,y,P;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=t.doc,o=t.file,a=t.range,i=t.index,u=t.password,e.next=3,ie(o);case 3:if(l=e.sent,c=Module._InitDocument(),s=ComPDFKitJS.opened_files.length||0,ComPDFKitJS.opened_files[s]=l,d=stringToNewUTF8(u),Module._LoadDocumentByStream(c,s,l.length,d),p=Module._GetPageCount(c),g=!1,"all"===a?h="1-"+p:"odd"===a?h=Array.from({length:p},(function(e,t){return t+1})).filter((function(e){return e%2!=0})).join(","):"even"===a?h=Array.from({length:p},(function(e,t){return t+1})).filter((function(e){return e%2==0})).join(","):(v=p,_=a.replace(/\s/g,""),A=_.split(","),m=[],A.forEach((function(e){if(e.includes("-")){var t=e.split("-").map((function(e){return parseInt(e)})),n=f(t,2),r=n[0],o=n[1];if(r<=0&&(g=!0),r>v)return;o>v&&(o=v),m.push(r===v?r.toString():"".concat(r,"-").concat(o))}else{var a=parseInt(e);a<=0&&(g=!0),a<=v&&m.push(a.toString())}})),h=m.join(",")),h&&!g){e.next=14;break}return e.abrupt("return",0);case 14:if(h=stringToNewUTF8(h),y=Module._ImportPagesAtIndex(r,c,h,i),!(P=Re("PDF EDITOR PAGE")).code){e.next=19;break}return e.abrupt("return",P);case 19:return ComPDFKitJS.opened_files.pop(),e.abrupt("return",{status:y});case 21:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()),e.on("RemovePage",(function(e){var t=e.doc,n=e.index,r=Module._RemovePage(t,n),o=Re("PDF EDITOR PAGE");return o.code?o:{status:r}})),e.on("RotatePage",(function(e){var t=e.pagePtr,n=e.rotation;Module._RotatePage(t,n);var r=Re("PDF EDITOR PAGE");if(r.code)return r})),e.on("GetPageWithoutParse",(function(e){var t=e.doc,n=e.index;return Module._GetPageWithoutParse(t,n)})),e.on("CopyPage",(function(e){var t=e.doc,n=e.index,r=stringToNewUTF8(n+1+""),o=Module._ImportPagesAtIndex(t,t,r,n+1),a=Re("PDF EDITOR PAGE");return a.code?a:{status:o}})),e.on("GetPageRotation",(function(e){return Module._GetPageRotation(e)})),e.on("ClearDocument",(function(e){Module._ClearDocument(e),ComPDFKitJS.opened_files=[]})),e.on("ExtractPage",function(){var e=i(n().mark((function e(t){var r,o,a,i,u,l,c,s,f,d,p,h,g,v;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=t.doc,o=t.range,a=t.separateFile,i=[],!a){e.next=20;break}u=0;case 4:if(!(u<o.length)){e.next=18;break}if(l=o[u]+1+"",c=stringToNewUTF8(l),s=Module._InitDocument(),Module._CreateDocument(s),Module._ImportPagesAtIndex(s,r,c,0),!(f=Re("PDF EDITOR PAGE")).code){e.next=13;break}return e.abrupt("return",f);case 13:d=Ce(s,2),i.push({name:l,blobData:d});case 15:u++,e.next=4;break;case 18:e.next=29;break;case 20:if(p=stringToNewUTF8(o),h=Module._InitDocument(),Module._CreateDocument(h),Module._ImportPagesAtIndex(h,r,p,0),!(g=Re("PDF EDITOR PAGE")).code){e.next=27;break}return e.abrupt("return",g);case 27:v=Ce(h,2),i.push(v);case 29:return e.abrupt("return",{blobDataList:i});case 30:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()),e.on("MovePage",(function(e){var t=e.doc,n=e.index,r=e.targetIndex,o=Module._MovePage(t,n,r),a=Re("PDF EDITOR PAGE");return a.code?a:{status:o}})),e.on("CreateNewImageAreaByStream",function(){var e=i(n().mark((function e(t){var r,o,a,i,u,l,c,s;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=t.pagePtr,o=t.editPagePtr,a=t.rect,i=t.imageBase64,u=ue(i),ComPDFKitJS.opened_image=[],ComPDFKitJS.opened_image[0]=u,l=Module._CreateNewImageAreaByStream(r,o,a.left,a.right,a.bottom,a.top,0,u.length,!0),(c=Module._GetPageRotation(r))%4!=0&&(s=90*c,Module._RotateImage(l,s,!1)),e.abrupt("return",l);case 8:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()),e.on("ReplaceImageAreaByStream",function(){var e=i(n().mark((function e(t){var r,o,a,i,u,l;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=t.pagePtr,o=t.editPagePtr,a=t.editAreaPtr,i=t.rect,u=t.imageBase64,l=ue(u),ComPDFKitJS.opened_image=[],ComPDFKitJS.opened_image[0]=l,e.abrupt("return",Module._ReplaceImageAreaByStream(r,o,a,i.left,i.right,i.bottom,i.top,0,l.length,!0));case 5:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()),e.on("RotateImage",(function(e){var t=e.editAreaPtr,n=e.angle;return Module._RotateImage(t,n,!0)})),e.on("HorizontalMirrorImage",(function(e){return Module._HorizontalMirrorImage(e,!0)})),e.on("VerticalMirrorImage",(function(e){return Module._VerticalMirrorImage(e,!0)})),e.on("SetImageTransparency",(function(e){var t=e.editAreaPtr,n=e.opacity;return Module._SetImageTransparency(t,n,!0)})),e.on("GetImageTransparency",(function(e){return Module._GetImageTransparency(e)})),e.on("GetImageAreaSize",(function(e){return ImageAreaInfo={},Module._GetImageAreaSize(e),{width:ImageAreaInfo.Width,height:ImageAreaInfo.Height,stride:ImageAreaInfo.Stride}})),e.on("ExtractOriginalImage",(function(e){var t=function(e){for(var t=e.editAreaPtr,n=e.width,r=e.height,o=e.stride,a=parseInt(n)*parseInt(r),i=_malloc(o*r),u=0;u<a;u++)Module.HEAP32[i/4+u]=0;Module._GetImageAreaData(t,i);for(var l=new Uint8Array(o*r),c=0;c<r;c++)for(var s=0;s<n;s++){var f=c*o+4*s,d=c*n*4+4*s;l[d]=Module.HEAPU8[i+f+2],l[d+1]=Module.HEAPU8[i+f+1],l[d+2]=Module.HEAPU8[i+f],l[d+3]=Module.HEAPU8[i+f+3]}return _free(i),l}(e);return{imageArray:t}})),e.on("CutWithRect",(function(e){var t=e.pagePtr,n=e.editAreaPtr,r=e.rect;Module._CutWithRect(t,n,r.left,r.right,r.bottom,r.top,!0)})),e.on("GetClipRect",(function(e){var t=e.pagePtr,n=e.editAreaPtr;return Rect={},Module._GetClipRect(t,n),{left:Rect.Left,top:Rect.Top,right:Rect.Right,bottom:Rect.Bottom}})),e.on("SetClipRect",(function(e){var t=e.pagePtr,n=e.editAreaPtr,r=e.rect;Module._SetClipRect(t,n,r.left,r.right,r.bottom,r.top,!0)})),e.on("RemoveSignature",(function(e){var t=e.doc,n=e.signaturePtr;return Module._RemoveSignature(t,n,!0)})),e.on("AddToTrustedCertificates",(function(e){return function(e){var t=e.signerPtr,n=Module._GetSignCert(t);if(n){return CertArray=[],!!Module._ExportToFilePath(n,0)&&CertArray[0]}return!1}(e)})),e.on("CreateReplyAnnotation",(function(e){return function(e,t,n){var r=n.author,o=n.date,a=n.dateCreated,i=n.contents,u=n.title,l=Module._CreateReplyAnnotation(e,t);r&&Be({annotPtr:l,author:r});n.name&&Module._SetAnnotDictName(l,stringToNewUTF8("NM"),stringToNewUTF8(n.name));i&&Le({annotPtr:l,contents:i});(o||a)&&Fe({annotPtr:l,date:o||a});u&&Ue({annotPtr:l,title:u});if(n.state){var c=$[n.state.toUpperCase()];Module._SetState(l,c)}return l}(e.pagePtr,e.annotPtr,e.replyAnnotation)})),e.on("CreateReplyStateAnnotation",(function(e){var t=e.pagePtr,n=e.annotPtr,r=e.state,o=$[r],a=Module._CreateReplyStateAnnotation(t,n,o);return newStateInt=Module._GetState(a),{annotPtr:a,state:ee[newStateInt]}})),e.on("GetReplyAnnotation",(function(e){return pt(e.pagePtr,e.annotPtr)})),e.on("EditReplyAnnotation",(function(e){var t=e.reply;t.author&&Be(t),t.contents&&Le(t),t.date&&Fe(t),t.title&&Ue(t)})),e.on("SetState",(function(e){var t=e.reviewAnnotState,n=e.markedAnnotState;if(n){var r=$[n.state.toUpperCase()];Module._SetState(n.annotPtr,r);var o=Module._GetState(n.annotPtr);return{annotPtr:n.annotPtr,state:ee[o]}}if(t){var a=$[t.state.toUpperCase()];Module._SetState(t.annotPtr,a);var i=Module._GetState(t.annotPtr);return{annotPtr:t.annotPtr,state:ee[i]}}})),e.on("CanRedo",(function(e){return Module._CanRedo(e)})),e.on("CanUndo",(function(e){return Module._CanUndo(e)})),e.on("Redo",(function(e){return EditCharPlace={},{editAreaPtr:Module._Redo(e),editCharPlace:EditCharPlace}})),e.on("Undo",(function(e){return EditCharPlace={},{editAreaPtr:Module._Undo(e),editCharPlace:EditCharPlace}})),e.on("GetLineLeading",(function(e){var t=e.editAreaPtr,n=e.char;return Module._GetLineLeading(t,n.SectionIndex,n.LineIndex,n.RunIndex,n.CharIndex)})),e.on("SetLineLeading",(function(e){var t=e.editAreaPtr,n=e.char,r=e.spacing;Module._SetLineLeading(t,n.SectionIndex,n.LineIndex,n.RunIndex,n.CharIndex,r)})),e.on("GetSectionAfterLeading",(function(e){var t=e.editAreaPtr,n=e.char;return Module._GetSectionAfterLeading(t,n.SectionIndex,n.LineIndex,n.RunIndex,n.CharIndex)})),e.on("SetSectionAfterLeading",(function(e){var t=e.editAreaPtr,n=e.char,r=e.spacing;Module._SetSectionAfterLeading(t,n.SectionIndex,n.LineIndex,n.RunIndex,n.CharIndex,r)})),e.on("GetCharacterSpacing",(function(e){var t=e.editAreaPtr,n=e.start,r=e.end;return Module._GetCharacterSpacing(t,n.SectionIndex,n.LineIndex,n.RunIndex,n.CharIndex,r.SectionIndex,r.LineIndex,r.RunIndex,r.CharIndex)})),e.on("SetCharacterSpacing",(function(e){var t=e.editAreaPtr,n=e.start,r=e.end,o=e.spacing;Module._SetCharacterSpacing(t,n.SectionIndex,n.LineIndex,n.RunIndex,n.CharIndex,r.SectionIndex,r.LineIndex,r.RunIndex,r.CharIndex,o)})),e.on("ApplyRedaction",(function(e){var t=e.annotPtr;return Module._ApplyRedaction(t)})),e.on("ApplyRedactionPage",(function(e){var t=e.pagePtr;return Module._ApplyRedactionPage(t)})),e.on("ErasureRedactionFromRect",(function(e){var t=e.pagePtr,n=e.rect;return Module._ErasureRedactionFromRect(t,n.left,n.right,n.bottom,n.top)})),e.on("GetStampRect",(function(e){var t=e.doc,n=e.pagePtr,r=e.annotation,o=r.contents,a=r.time,i=r.stampShape,u=r.stampColor,l=N[r.type.toUpperCase()],c=Module._CreateAnnot(t,n,l),s=stringToNewUTF8(o),f=stringToNewUTF8(a);Module._SetTextStamp(c,s,f,i,u,0);var d=Re("PDF Annotation STAMPS");return d.code?d:(Module._UpdateAnnotAp(c,0),Module._GetAnnotRect(n,c),Module._RemoveAnnot(c),{left:Rect.Left,top:Rect.Top,right:Rect.Right,bottom:Rect.Bottom})})),e.on("GetLayers",(function(e){var t=Module._getLayerTree(e),n=Module._getRootNode(t),r=[],o=[],a=Module._getChildrenCount(n);return r.push.apply(r,d(function e(t,n){for(var r=[],a=0;a<n;a++){var i=Module._getChild(t,a);Module._getName(i);var u=Module._isLocked(i),l=Module._getLayerNodeDict(i),c=Module._getViewUsage(i),s=1!==c?1:0;s&&l&&o.push(l);var f=Module._getExportUsage(i);LayerPrintData={},Module._getPrintUsage(i),LayerZoomData={},Module._getZoomUsage(i);var p=Module._getChildrenCount(i),h=[];p>0&&h.push.apply(h,d(e(i,p))),r.push({node:i,ocg:l,name:U8StringData,isLocked:u,visible:s,defaultVisible:s,children:h,viewState:c,exportState:0===f?0:1===f?1:2,printState:0===LayerPrintData.print_state?0:1===LayerPrintData.print_state?1:2,zoom:LayerZoomData})}return r}(n,a))),{layers:r,ocgsPtr:o}})),e.on("SetLayer",(function(e){var t=e.node,n=e.name,r=e.defaultVisible,o=e.isLocked,a=e.printState,i=e.exportState;n&&Module._setName(t,stringToNewUTF8(n)),e.hasOwnProperty("defaultVisible")&&Module._setViewUsage(t,!r),e.hasOwnProperty("isLocked")&&Module._setLocked(t,o),e.hasOwnProperty("printState")&&Module._setPrintUsage(t,stringToNewUTF8(""),0===a?0:1===a?1:3),e.hasOwnProperty("exportState")&&Module._setExportUsage(t,0===i?0:1===i?1:3)})),e.on("RenderLayerBitmap",(function(e){var t=function(e){for(var t=e.pagePtr,n=e.x,r=e.y,o=e.width,a=e.height,i=e.mode,u=e.flag,l=e.ocgsPtr,c=e.form,s=parseInt(o)*parseInt(a),f=4*s,d=_malloc(f),p=0;p<s;p++)Module.HEAP32[d/4+p]=0;var h=l.length,g=_malloc(4*h);for(p=0;p<h;p++)Module.HEAP32[g/4+p]=l[p];Module._RenderLayerBitmap(t,n,r,o,a,i,d,u,g,h,c);var v=Re("PDF VIEWER RENDER");if(v.code)return _free(d),v;var _=new Uint8Array(f);for(p=0;p<f;p+=4)_[p]=Module.HEAPU8[d+p+2],_[p+1]=Module.HEAPU8[d+p+1],_[p+2]=Module.HEAPU8[d+p],_[p+3]=Module.HEAPU8[d+p+3];return _free(d),{imageArray:_}}({pagePtr:e.pagePtr,x:e.x,y:e.y,width:e.width,height:e.height,ocgsPtr:e.ocgsPtr,mode:-1,flag:1,form:1});return t})),e.on("RenderLayerBitmapWithMatrix",(function(e){var t=e.pagePtr,n=e.scale,r=e.left,o=e.right,a=e.bottom,i=e.top,u=e.mode,l=void 0===u?-1:u,c=e.flag,s=void 0===c?0:c,f=e.ocgsPtr,d=e.form,p=function(e){for(var t=e.pagePtr,n=e.scale,r=e.left,o=e.right,a=e.bottom,i=e.top,u=e.mode,l=e.flag,c=e.ocgsPtr,s=e.form,f=parseInt(o-r)*parseInt(a-i),d=4*f,p=_malloc(d),h=0;h<f;h++)Module.HEAP32[p/4+h]=0;var g=c.length,v=_malloc(4*g);for(h=0;h<g;h++)Module.HEAP32[v/4+h]=c[h];Module._RenderPageBitmapWithMatrix2(t,n,r,o,a,i,u,p,l,v,g,s);var _=Re("PDF VIEWER RENDER");if(_.code)return _free(p),_;var A=new Uint8Array(d);for(h=0;h<d;h+=4)A[h]=Module.HEAPU8[p+h+2],A[h+1]=Module.HEAPU8[p+h+1],A[h+2]=Module.HEAPU8[p+h],A[h+3]=Module.HEAPU8[p+h+3];return _free(p),{imageArray:A}}({pagePtr:t,scale:n,left:r,right:o,bottom:a,top:i,mode:l,flag:s,ocgsPtr:f,form:void 0===d?0:d});return p})),e.on("ExportLayers",(function(e){for(var t=e.doc,n=e.ocgsPtr,r=n.length,o=4*n.length,a=_malloc(o),i=0;i<n.length;i++)Module.HEAP32[a/4+i]=n[i];return Ce(Module._exportDocumentLayer(t,a,r),2)})),e.on("InitColorList",(function(e){return Module._initColorList(e)})),e.on("GetPDFSeparationColor",function(){var e=i(n().mark((function e(t){var r,o,a,i,u;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=t.pagePtr,o=t.fileData,a=o instanceof ArrayBuffer?new Uint8Array(o):o,i=_malloc(a.length),Module.HEAPU8.set(a,i),u=Module._getPDFSeparationColor(r,i,a.length),_free(i),e.abrupt("return",u);case 7:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()),e.on("GetColorsList",(function(e){SepColorsArray=[],Module._getColorsList(e);for(var t=[],n=0;n<SepColorsArray.length;n++){var r=SepColorsArray[n];t.push({index:n,name:r.ColorName,color:"rgb(".concat(r.R,", ").concat(r.G,", ").concat(r.B,")")})}return t})),e.on("GetSelectPointColor",(function(e){var t=e.pagePtr,n=e.sepColorPtr,r=e.point;return SelectPointColorArray=[],Module._getSelectPointColor(t,n,r.x,r.y),SelectPointColorArray})),e.on("RenderPageAndIgnoreColorByIndex",(function(e){var t=e.sepColorPtr,n=e.x,r=e.y,o=e.width,a=e.height,i=e.indexs,u=e.flag,l=void 0===u?0:u,c=e.rotate,s=function(e){for(var t=e.sepColorPtr,n=e.x,r=e.y,o=e.width,a=e.height,i=e.rotate,u=e.flag,l=e.indexs,c=parseInt(o)*parseInt(a),s=4*c,f=_malloc(s),d=0;d<c;d++)Module.HEAP32[f/4+d]=0;var p=l.length,h=_malloc(4*p);for(d=0;d<p;d++)Module.HEAP32[h/4+d]=l[d];Module._renderPageAndIgnoreColorByIndex(t,f,n,r,o,a,i,u,h,p);var g=Re("PDF VIEWER COLOR RENDER");if(g.code)return _free(f),g;var v=new Uint8Array(s);for(d=0;d<s;d+=4)v[d]=Module.HEAPU8[f+d+2],v[d+1]=Module.HEAPU8[f+d+1],v[d+2]=Module.HEAPU8[f+d],v[d+3]=Module.HEAPU8[f+d+3];return _free(f),{imageArray:v}}({sepColorPtr:t,x:n,y:r,width:o,height:a,rotate:void 0===c?0:c,flag:l,indexs:i});return s})),e.on("CreateMeasurementAnnotation",(function(e){return function(e,t,n){var r="RECTANGLE"===n.type.toUpperCase()?"POLYGON":n.type.toUpperCase(),o=N[r],a=te[n.measureType.toUpperCase()],i=Module._CreateAnnot(e,t,o),u=Module._initRuler(e,i,a);n.captionType&&(MeasureInfo={},Module._getMeasureInfo(u),Module._SetMeasureInfo(u,MeasureInfo.factor,stringToNewUTF8(MeasureInfo.unit),stringToNewUTF8(MeasureInfo.decimalSymbol),stringToNewUTF8(MeasureInfo.thousandSymbol),stringToNewUTF8(MeasureInfo.display),MeasureInfo.precision,stringToNewUTF8(MeasureInfo.unitPrefix),stringToNewUTF8(MeasureInfo.unitSuffix),stringToNewUTF8(MeasureInfo.unitPosition),MeasureInfo.RulerBase,stringToNewUTF8(MeasureInfo.RulerBaseUnit),MeasureInfo.RulerTranslate,stringToNewUTF8(MeasureInfo.RulerTranslateUnit),re[n.captionType.toUpperCase()]));var l=Re("PDF Annotation");if(l.code)return l;return n.pagePtr=t,n.annotPtr=i,n.rulerPtr=u,ht(e,n),{annotPtr:i,rulerPtr:u}}(e.doc,e.pagePtr,e.annotation)})),e.on("EditMeasurementAnnotation",(function(e){return ht(e.doc,e.annotation)})),e.on("GetMeasurementData",(function(e){return gt(e.annotPtr,e.rulerPtr,e.type)})),e.on("GetMeasurementResult",(function(e){var t=e.annotPtr,n=e.rulerPtr,r=e.captionType,o=r?re[r.toUpperCase()]:Module._getCaptionTypes(t);return Module._getMeasurementResults(n,o)})),e.on("GetFactor",(function(e){return Module._getFactor(e)}))}}]);var t}();function he(){return(he=i(n().mark((function e(){return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(runtimeInitialized){e.next=5;break}return e.next=3,ce(100);case 3:e.next=0;break;case 5:return e.abrupt("return",new Promise((function(e,t){e()})));case 6:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function ge(e){var t=e.doc,n=e.fileId,r=e.length,o=e.password,a=stringToNewUTF8(o);return Module._LoadDocumentByStream(t,n,r,a),PermissionsInfo={},Module._GetPermissionsInfo(t),PermissionsInfo}function ve(e){var t=e.doc,n=e.password,r=e.permissionPassword,o=void 0===r?"":r,a=e.permissions,i=void 0===a?"":a,u=stringToNewUTF8(n),l=o?stringToNewUTF8(o):"",c=i.allowsPrinting,s=i.allowsCopying,f=i.allowsDocumentChanges;return c=void 0===c||c,s=void 0===s||s,f=void 0===f||f,Module._SetPassword(t,u,l,c,1,s,f,1,1,1),Re("PDF SECURITY ENCRYPT")}function _e(e,t,n,r){++r;var o=[];if(!Module._IsValid(t))return n;var a=Module._GetFirst(t);if(!Module._IsValid(a))return n;for(var i=Ae(e,o,a,n,r);Module._IsValid(i);)i=Ae(e,o,i,n,r);for(var u=0;u<o.length;u++)_e(e,o[u],n[u].children,r)}function Ae(e,t,n,r,o){t.push(n),Module._GetTitle(n);var a={title:U8StringData,children:[],level:o};return function(e,t,n,r){1===n?(PDFDestination={},Module._GetOutlineDestination(e,t),r.pageNumber=PDFDestination.PageIndex+1,r.posX=PDFDestination.PosX<0?0:PDFDestination.PosX,r.posY=PDFDestination.PosY<0?0:PDFDestination.PosY):6===n&&(Module._GetUri(t),r.url=U8StringData)}(e,n,Module._GetOutlineActionType(e,n),a),r.push(a),Module._GetNext(n)}function me(e){var t=e.doc,n=e.pagePtr,r=e.annotPtr,o=e.type,a=e.pageIndex,i=null;Rect={},Module._GetAnnotRect(n,r);var u={left:C(Rect.Left),top:C(Rect.Top),right:C(Rect.Right),bottom:C(Rect.Bottom)};Module._GetAnnotCreationDate(r);var l=S(U8StringData);Module._GetAnnotContent(r);var c=U8StringData;Module._GetAnnotTitle(r);var s=U8StringData;Module._GetAnnotAuthor(r);var d=U8StringData,p=C(Module._GetAnnotTransparency(r));U8StringData="",Module._GetAnnotDictName(r,stringToNewUTF8("NM"));var h=U8StringData;h||(h=P(),Module._SetAnnotDictName(r,stringToNewUTF8("NM"),stringToNewUTF8(h))),U8StringData="",Module._GetAnnotDictName(r,stringToNewUTF8("trn-custom-data"));var g=U8StringData;try{g=JSON.parse(g)}catch(e){g={}}var v={type:o,date:l,contents:c,rect:u,opacity:p,name:h,pagePtr:n,annotPtr:r,title:s,author:d,color:null,borderColor:null,borderStyle:"solid",borderWidth:Module._GetAnnotBorderWidth(r),fillColor:null,fillTransparency:1,pageIndex:a,customData:g},_=pt(n,r,h);switch(_&&(v.replies=_),RGBColor={},Module._GetAnnotRGBColor(r)&&(v.borderColor=F(RGBColor.R,RGBColor.G,RGBColor.B)),RGBColor={},Module._GetAnnotFilledRGBColor(r)&&(v.fillColor=F(RGBColor.R,RGBColor.G,RGBColor.B)),(BorderStyle={}).DashArray=[],Module._GetAnnotBorderStyle(r)&&(v.borderStyle=X[BorderStyle.Style],v.dashes=BorderStyle.DashArray),o){case"text":return v.textColor=v.borderColor,delete v.borderColor,v;case"link":var A=Module._GetLinkActionType(r);return v.actionType=K[A],1===A?(PDFDestination={},Module._GetLinkDestination(t,r),v.destPage=PDFDestination.PageIndex+1,v.url=""):6===A&&(Module._GetLinkUri(r),v.url=U8StringData,v.destPage=""),v;case"freetext":FontDa={},Module._GetFreeTextFont(r);var m=U8StringData,y=/bold/i.test(m)?"bold":"normal",M=/(oblique|italic)/i.test(m)?"oblique":"normal";v.fontName=m.split("-")[0],v.fontStyle=M,v.fontWeight=y,v.fontSize=FontDa.fontSize,v.color=F(FontDa.R,FontDa.G,FontDa.B),RGBColor={},Module._GetFreeTextBorderColor(r)&&(v.borderColor=F(RGBColor.R,RGBColor.G,RGBColor.B)),v.fillTransparency=Module._GetAnnotFilledTransparency(r);var w=Module._GetTextAlignment(r);return v.textAlignment=U[w],v.contents||(Module._GetFreeTextContent(r),v.contents=U8StringData),RGBColor={},Module._GetFreeTextBgColor(r)&&(v.fillColor=F(RGBColor.R,RGBColor.G,RGBColor.B)),v;case"line":LinePoint={},Module._GetLinePoint(n,r);var x=[LinePoint.StartPointX,LinePoint.StartPointY,LinePoint.EndPointX,LinePoint.EndPointY];if(v.linePoints=x,LineType={},Module._GetLineType(r),v.tail=z[LineType.Tail],v.head=z[LineType.Head],Module._isMeasured(r)){v.measure=1,v.rulerPtr=Module._initRuler(t,r,te.DISTANCE);var I=gt(r,v.rulerPtr,o);Object.assign(v,I)}return v;case"arc":case"square":case"polygon":if(v.vertices=At({pagePtr:n,annotPtr:r}),4===v.vertices.length){var b=f(v.vertices,4),T=b[0],E=b[1],R=b[2],G=b[3];T.y===E.y&&E.x===R.x&&R.y===G.y&&G.x===T.x&&(v.type="rectangle")}if(v.fillTransparency=Module._GetAnnotFilledTransparency(r),Module._isMeasured(r)){v.measure=1,v.rulerPtr=Module._initRuler(t,r,te.AREA);var D=gt(r,v.rulerPtr,o);Object.assign(v,D)}return v;case"polyline":if(v.vertices=At({pagePtr:n,annotPtr:r}),Module._isMeasured(r)){v.measure=1,v.rulerPtr=Module._initRuler(t,r,te.PERIMETER);var B=gt(r,v.rulerPtr,o);Object.assign(v,B)}return v;case"circle":if(v.fillTransparency=Module._GetAnnotFilledTransparency(r),Module._isMeasured(r)){v.measure=1,v.rulerPtr=Module._initRuler(t,r,te.DISTANCE);var L=gt(r,v.rulerPtr,o);Object.assign(v,L)}return v;case"highlight":case"underline":case"squiggly":case"strikeout":return i=function(e){var t=e.pagePtr,n=e.annotPtr;RGBColor={};var r={};return Module._GetAnnotRGBColor(n)&&(r.color=F(RGBColor.R,RGBColor.G,RGBColor.B)),InkPointArray=[],Module._GetQuadPoints(t,n),r.quadPoints=InkPointArray,r}({pagePtr:n,annotPtr:r}),Object.assign(v,i),v;case"stamp":var N=Module._GetStampType(r);return v.stampType=q[N],0===N?v.stampType="image":(TextStamp={},Module._GetTextStamp(r),v.stampColor=TextStamp.StampColor,v.stampShape=Z[TextStamp.StampType],TextStamp.Date&&(v.time=TextStamp.Date)),v;case"ink":if(i=function(e){for(var t=e.pagePtr,n=e.annotPtr,r=Module._GetInkPathCount(n),o=[],a=0;a<r;a++)InkPointArray=[],Module._GetInkPoints(t,n,a),o.push(InkPointArray);return{borderWidth:Module._GetAnnotBorderWidth(n),inkPointes:o}}({pagePtr:n,annotPtr:r}),Object.assign(v,i),3===i.inkPointes[0].length&&(v.arcPoints=i.inkPointes[0].map((function(e){return{x:e.PointX,y:e.PointY}}))),Module._isMeasured(r)){v.measure=1,v.rulerPtr=Module._initRuler(t,r,te.ARC);var k=gt(r,v.rulerPtr,o);Object.assign(v,k)}return v}}function ye(e){var n=e.doc,r=e.pagePtr,o=e.annotPtr,a=e.signatures,i=e.pageIndex;Rect={},Module._GetAnnotRect(r,o);var u={left:C(Rect.Left),top:C(Rect.Top),right:C(Rect.Right),bottom:C(Rect.Bottom)};Module._GetAnnotCreationDate(o);var l=S(U8StringData);Module._GetAnnotContent(o);var c=U8StringData;Module._GetAnnotAuthor(o);var s=U8StringData,f=C(Module._GetAnnotTransparency(o)),d=Module._GetWidgetType(o),p=Module._GetRequiredFlag(o);U8StringData="",Module._GetAnnotDictName(o,stringToNewUTF8("NM"));var h=U8StringData;h||(h=P(),Module._SetAnnotDictName(o,stringToNewUTF8("NM"),stringToNewUTF8(h))),U8StringData="",Module._GetAnnotDictName(o,stringToNewUTF8("trn-custom-data"));var g=U8StringData;try{g=JSON.parse(g)}catch(e){g={}}var v=Module._GetAnnotBorderWidth(o),_={type:j[d],name:h,pagePtr:r,annotPtr:o,date:l,contents:c,rect:u,opacity:f,required:p,author:s,borderColor:null,borderWidth:v,borderStyle:"solid",fillColor:null,fillTransparency:1,pageIndex:i,customData:g};RGBColor={},Module._GetAnnotRGBColor(o)&&(_.borderColor=F(RGBColor.R,RGBColor.G,RGBColor.B)),RGBColor={},Module._GetWidgetBackgroundRGBColor(o)&&(_.fillColor=F(RGBColor.R,RGBColor.G,RGBColor.B));var A=Module._GetWidgetBorderStyle(o);_.borderStyle=X[A],Module._GetWidgetFieldName(o),_.fieldName=U8StringData;var m=Module._GetAnnotFlag(o);if(_.flag=m,0===m)_.isHidden=0;else{var y=!(m&W),M=!(m&Y),w=!(m&H);_.isHidden=y&&M&&w?0:1}switch(d){case O.PUSHBUTTON:Module._GetButtonTitle(o),_.title=U8StringData,Module._GetBtnActionNamedAction(o),_.actionName=U8StringData;var x=Module._GetBtnActionType(o);if(1===x){var I=Module._GetBtnActionDestPage(n,o);_.destPage=I+1}else 6===x&&(Module._GetBtnActionURI(o),_.url=U8StringData);_.actionType=x,FontDa={},Pe(_,o,Module._GetAnnotFontDa(o));var b=Me(U8StringData),T=t({fontSize:FontDa.fontSize||12},b);Object.assign(_,T);break;case O.CHECKBOX:var E=Module._GetWidgetCheckStyle(o);_.checkStyle=E;var R=Module._GetCheckBoxIsChecked(o);_.isChecked=R;break;case O.RADIOBUTTON:var G=Module._GetWidgetCheckStyle(o);_.checkStyle=G;var D=Module._GetRadioButtonIsChecked(o);_.isChecked=D;break;case O.TEXTFIELD:if(1===Module._GetTextFieldSpe(o))return null;Module._GetTextFieldContent(o),_.contents=U8StringData;var B=Module._IsMultiLine(o);_.isMultiLine=B;var L=Module._GetWidgetTextJustification(o);_.textAlignment=U[L],FontDa={},Pe(_,o,Module._GetAnnotFontDa(o));var N=Me(U8StringData),k=t({fontSize:FontDa.fontSize||12},N);Object.assign(_,k);break;case O.COMBOBOX:case O.LISTBOX:WidgetItem={},Module._GetItemsSelectedItem(o),_.selectedItem={values:WidgetItem.Values,strings:WidgetItem.Strings};var Q=Module._GetItemsCount(o);_.itemsCount=Q,WidgetItemsArray=[],Module._GetWidgetItems(o),_.items=WidgetItemsArray,Module._GetItemsSelectedValue(o);var z=U8StringData;if(z){for(var q=0;q<WidgetItemsArray.length;q++)if(WidgetItemsArray[q].Value===z){_.selected=q;break}}else _.selected=null;FontDa={},Pe(_,o,Module._GetAnnotFontDa(o));var K=Me(U8StringData),J=t({fontSize:FontDa.fontSize||12},K);Object.assign(_,J);break;case O.SIGNATUREFIELDS:for(var Z=0;Z<a.length;Z++){var V=a[Z].signaturePtr;Module._IsSameAnnotDict(V,o)&&(SignerArray=[],Module._GetSigner(V),SignerArray.length&&(_.isSignedDigitally=!0),_.signature=a[Z])}_.isSigned=Module._IsStampSignature(o)}return _}function Pe(e,t,n){if(RGBColor={},n){var r=FontDa,o=F(r.R,r.G,r.B);e.color=o}else if(Module._GetAnnotRGBColor(t)){var a=RGBColor,i=F(a.R,a.G,a.B);e.color=i}else e.color="#000000"}function Me(e){var t=e.split("-")[0],n=/bold/i.test(e)?"bold":"normal";return{fontName:t,fontStyle:/(oblique|italic)/i.test(e)?"oblique":"normal",fontWeight:n}}function we(e){var t=e.fontName,n=e.fontWeight,r=void 0===n?"normal":n,o=e.fontStyle,a=void 0===o?"normal":o;return"bold"!==r||"italic"!==a&&"oblique"!==a?"bold"===r?t+"-"+xe(r):"italic"===a||"oblique"===a?t+"-"+xe("Times"===t?"italic":a):t:t+"-"+xe(r)+xe("Times"===t?"italic":a)}function xe(e){return e.charAt(0).toUpperCase()+e.slice(1)}function Ie(e){for(var n=e.doc,r=[],o=[],a=Module._GetSignatureCount(n),i=0;i<a;i++){var u=Module._InitSignature(n,i);if(u){o.push(u);var l=[],c={signaturePtr:u,signerList:l};SignerArray=[],Module._GetSigner(u);for(var s=[],f=0;f<SignerArray.length;f++){var d=SignerArray[f];if(d){var p={signerPtr:d,isSignVerified:!1,isCertTrusted:!1,certificateList:s},h=Module._GetSignCert(d);if(h){CertArray=[],Module._ExportToFilePath(h,1),ComPDFKitJS.opened_cert[0]=CertArray[0],CertArray=[],Module._GetSignCertChain(h),CertArray.push(h);for(var g=0;g<CertArray.length;g++){var v=CertArray[g],_=t({certPtr:v},ct(v));s.push(_)}var A=ComPDFKitJS.opened_cert[0].length,m=Module._VerifyGetChain(u,h,0,A),y=void 0;if(y=m?null:Module._bEqualCert(h,0,A),m||y){p.isCertTrusted=!0;for(var P=0;P<p.certificateList.length;P++)p.certificateList[P].isTrusted=!0}var M=ComPDFKitJS.opened_files[0].length;if(1===Module._VerifySigner(u,d,0,M)){var w=Module._VerifyDocument(n,u,0,M);p.isSignVerified=1===w}DiffInfoArray=[],Module._GetSignatureDiffInfo(u),p.diffInfo=DiffInfoArray}Module._GetTimeStampSignature(d),l.push(p)}}if(SignerArray.length){Rect={},Module._GetSignatureRect(n,u),c.rect={left:C(Rect.Left),top:C(Rect.Top),right:C(Rect.Right),bottom:C(Rect.Bottom)},Module._GetSignatureFieldName(u),c.fieldName=U8StringData,Module._GetSignatureName(u),c.name=U8StringData||s[0].subject[5]||"",Module._GetSignatureTime(u);var x=U8StringData;c.date=S(x),Module._GetSignatureReason(u),c.reason=U8StringData,Module._GetSignatureLocation(u),c.location=U8StringData,c.pageIndex=Module._GetSignaturePageIndex(n,u),c.isDigital=1}else c.isDigital=0;r.push(c)}}return{signatures:r,signaturePtrList:o}}function be(e,t){return Se.apply(this,arguments)}function Se(){return Se=i(n().mark((function e(t,r){var o,a,i,u,l,c,s,f,d;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:for(DataArray=[],o=!!fontsJson||!!ComPDFKitJS.opened_DefFont[0],Module._SaveDocumentByStream(t,2,o),a=[],i=0;i<DataArray.length;i++)u=new Uint8Array(DataArray[i]),a.push(u);return DataArray=[],l=new Blob(a,{type:"application/pdf"}),e.next=9,ie(l);case 9:return c=e.sent,s=Module._InitDocument(),f=ComPDFKitJS.opened_files.length,ComPDFKitJS.opened_files[f]=c,d=stringToNewUTF8(r),Module._LoadDocumentByStream(s,f,c.length,d),e.abrupt("return",s);case 16:case"end":return e.stop()}}),e)}))),Se.apply(this,arguments)}function Ce(e,t){DataArray=[],1===t&&DataArray.push(ComPDFKitJS.opened_files[0]);var n=!!fontsJson||!!ComPDFKitJS.opened_DefFont[0];Module._SaveDocumentByStream(e,t,n);for(var r=[],o=0;o<DataArray.length;o++){var a=new Uint8Array(DataArray[o]);r.push(a)}return DataArray=[],new Blob(r,{type:"application/pdf"})}function Te(e){return Ee.apply(this,arguments)}function Ee(){return(Ee=i(n().mark((function e(t){var r,o,a,i,u,l,c,s,f,d,p,h;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=t.doc,o=t.saveType,a=void 0===o?2:o,i=t.password,u=t.oldPassword,l=t.permissionPassword,c=void 0===l?"":l,s=t.permissions,f=void 0===s?"":s,d=null,2!==a){e.next=21;break}if(u!==i&&i||f){e.next=7;break}d=Ce(r,a),e.next=19;break;case 7:if(!i&&!f){e.next=18;break}return e.next=10,be(r,u);case 10:if(p=e.sent,!(h=ve({doc:p,password:i,permissionPassword:c,permissions:f}))||!h.code){e.next=14;break}return e.abrupt("return",h);case 14:d=Ce(p,a),ComPDFKitJS.opened_files.pop(),e.next=19;break;case 18:d=Ce(r,a);case 19:e.next=22;break;case 21:d=Ce(r,a);case 22:return e.abrupt("return",d);case 23:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function Re(e){var t=Module._GetComPDFkitLicenseErrorcode(),n={code:t};if(t)switch(t){case-100:n.message="The license is invalid";break;case-101:n.message="The license is expired";break;case-102:n.message="The license does not support the current platform";break;case-103:case-104:n.message="The license does not support your domain";break;case-105:n.message="The license does not support the "+e;break;case-106:n.message="License has not been initialized";break;case-107:n.message="The license does not support the current version";break;case-1e3:n.message="Illegal access to the API interface";break;case-1001:n.message="Failed to read license file"}return n}function Ge(){return Ge=i(n().mark((function e(t){var r,o,a,i,u,l,c,s,f,d,p,h,g,v,_,A;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=t.doc,o=t.saveType,a=void 0===o?2:o,i=t.password,u=void 0===i?"":i,l=t.oldPassword,c=void 0===l?"":l,e.next=3,be(r,c);case 3:for(s=e.sent,f=Module._GetPageCount(s),d=[],p=0;p<f;p++)h=Module._GetPage(s,p),d.push(h);g=0;case 8:if(!(g<d.length)){e.next=17;break}if(Module._FlattenPage(d[g]),!(v=Re("PDF Annotation FLATTEN")).code){e.next=14;break}return ComPDFKitJS.opened_files.pop(),e.abrupt("return",v);case 14:g++,e.next=8;break;case 17:if(_=null,!u||u===c){e.next=26;break}if(!(A=ve({doc:s,password:u}))||!A.code){e.next=23;break}return ComPDFKitJS.opened_files.pop(),e.abrupt("return",A);case 23:_=Ce(s,a),e.next=27;break;case 26:_=Ce(s,a);case 27:return ComPDFKitJS.opened_files.pop(),e.abrupt("return",{blobData:_});case 29:case"end":return e.stop()}}),e)}))),Ge.apply(this,arguments)}function Fe(e){var t=e.annotPtr,n=e.date;n=n||new Date;var r=stringToNewUTF8(b(n));Module._SetAnnotCreationDate(t,r),function(e){var t=e.annotPtr,n=e.date;n=n||new Date;var r=stringToNewUTF8(b(n));Module._SetAnnotRecentlyModifyDate(t,r)}({annotPtr:t,date:n})}function De(e){var t=e.pagePtr,n=e.annotPtr,r=e.rect,o=r.left,a=r.right,i=r.bottom,u=r.top;return Module._SetAnnotRect(t,n,o,a,i,u)}function Be(e){var t=e.annotPtr,n=e.author,r=stringToNewUTF8(n);return Module._SetAnnotAuthor(t,r)}function Ue(e){var t=e.annotPtr,n=e.title,r=stringToNewUTF8(n);return Module._SetAnnotTitle(t,r)}function Le(e){var t=e.annotPtr,n=e.contents,r=stringToNewUTF8(n),o=Module._SetAnnotContent(t,r);return _free(r),o}function Ne(e){var t=e.annotPtr,n=G(e.borderColor);return!!Module._SetAnnotRGBColor(t,n.R,n.G,n.B)}function ke(e){var t=e.annotPtr,n=G(e.borderColor);return!!Module._SetFreeTextBorderColor(t,n.R,n.G,n.B)}function Oe(e){var t=e.annotPtr,n=e.fillColor;if(n){var r=G(n);return!!Module._SetFreeTextBgColor(t,r.R,r.G,r.B)}Module._ClearFreeTextBgColor(t)}function je(e){var t=e.annotPtr,n=e.fillColor;if(!n)return Module._ClearAnnotFilledRGBColor(t);var r=G(n);return!!Module._SetAnnotFilledRGBColor(t,r.R,r.G,r.B)}function We(e){Module._SetAnnotDictName(e.annotPtr,stringToNewUTF8("trn-custom-data"),stringToNewUTF8(e.customData))}function He(e){var t=e.annotPtr,n=e.opacity,r=void 0===n?1:n;return Module._SetAnnotTransparency(t,r)}function Ye(e){var t=e.annotPtr,n=e.borderWidth;Module._SetAnnotBorderWidth(t,n)}function Qe(e){var t=e.annotPtr,n=e.fillTransparency,r=void 0===n?1:n;return Module._SetAnnotFilledTransparency(t,r)}function ze(e){for(var t=e.pagePtr,n=e.annotPtr,r=e.quadPoints,o=[],a=0;a<r.length;a++){var i=r[a];o.push(i.PointX,i.PointY)}for(var u=o.length,l=_malloc(4*u),c=0;c<u;c++)Module.HEAPF32[l/4+c]=o[c];var s=Module._SetQuadPoints(t,n,l,u);return _free(l),s}function qe(e){var t=e.pagePtr,n=e.annotPtr,r=e.linePoints;return Module._SetLinePoint(t,n,r[0],r[1],r[2],r[3])}function Ke(e){for(var t=e.pagePtr,n=e.annotPtr,r=e.inkPointes,o=[],a=[],i=r.length,u=0;u<i;u++){for(var l=r[u].length,c=0;c<l;c++)o.push(r[u][c].PointX,r[u][c].PointY);a.push(l)}for(var s=a.length,f=_malloc(4*s),d=0;d<s;d++)Module.HEAP32[f/4+d]=a[d];var p=o.length,h=_malloc(4*p);for(d=0;d<p;d++)Module.HEAPF32[h/4+d]=o[d];Module._SetInkPath(t,n,f,i,h),_free(h)}function Je(e){var t=e.doc,n=e.annotPtr,r=e.destPage,o=e.pageHeight;Module._SetLinkDestination(t,n,r-1,0,o,0)}function Xe(e){var t=e.annotPtr,n=e.url,r=stringToNewUTF8(n);Module._SetLinkUri(t,r)}function Ze(e){var t=e.annotPtr,n=e.isChecked;Module._SetCheckBoxIsChecked(t,n);var r=Re("PDF FORM FILL");if(r.code)return r}function Ve(e){var t=e.annotPtr,n=e.isChecked;Module._SetRadioButtonIsChecked(t,n);var r=Re("PDF FORM FILL");if(r.code)return r}function $e(e){var t=e.annotPtr,n=e.checkStyle;Module._SetWidgetCheckStyle(t,n)}function et(e){var t=e.annotPtr,n=G(e.fillColor);Module._SetWidgetBackgroundRGBColor(t,n.R,n.G,n.B)}function tt(e){var t=e.annotPtr,n=e.fieldName,r=stringToNewUTF8(n);Module._SetWidgetFieldName(t,r)}function nt(e){var t=e.annotPtr,n=e.fontName,r=e.fontWeight,o=e.fontStyle,a=e.fontSize,i=G(e.color||e.textColor||"#000000"),u=we({fontName:n,fontWeight:r,fontStyle:o}),l=stringToNewUTF8(u);return Module._SetAnnotFontDa(t,l,a,i.R,i.G,i.B)}function rt(e){var t=e.annotPtr,n=e.selected;Module._SetItemsSelectedIndex(t,n);var r=Re("PDF FORM FILL");if(r.code)return r}function ot(e){var t=e.doc,n=e.annotPtr,r=e.destPage,o=e.pageHeight,a=e.actionType,i=Module._InitAction();Module._SetActionType(i,a),r&&1==a&&(Module._SetDestination(i,t,r-1,0,o,0),Module._SetButtonAction(n,i),Module._ClearAction(i))}function at(e){var t=e.annotPtr,n=e.url,r=e.actionType,o=Module._InitAction();if(Module._SetActionType(o,r),n&&6==r){var a=stringToNewUTF8(n);Module._SetUriPath(o,a),Module._SetButtonAction(t,o),Module._ClearAction(o)}}function it(e){var t=e.annotPtr,n=e.contents,r=stringToNewUTF8(n);Module._SetTextFieldContent(t,r);var o=Re("PDF FORM FILL");if(o.code)return o}function ut(e){var t=e.annotPtr,n=e.item,r=n.index,o=n.value,a=n.string,i=stringToNewUTF8(o),u=stringToNewUTF8(a);Module._AddOptionItem(t,r,i,u)}function lt(e){var t=e.annotPtr,n=e.fontName,r=e.fontWeight,o=e.fontStyle,a=e.fontSize,i=e.color,u=e.textAlignment,l=e.contents,c=we({fontName:n,fontWeight:r,fontStyle:o}),s=L[u.toUpperCase()],f=stringToNewUTF8(c),d=stringToNewUTF8(l),p=G(i);Module._SetFreeText(t,f,a,p.R,p.G,p.B,s,d),Module._SetAnnotContent(t,d),_free(d)}function ct(e){var t=["C","ST","L","O","OU","CN","email"],n=Module._GetSignVersion(e);Module._GetSignAlgOid(e);for(var r=U8StringData,o=function(e){switch(e){case"1.2.840.113549.1.1.1":default:return"RSA_RSA";case"1.2.840.113549.1.1.2":return"MD2RSA";case"1.2.840.113549.1.1.3":return"MD4RSA";case"1.2.840.113549.1.1.4":return"MD5RSA";case"1.2.840.113549.1.1.5":return"SHA1RSA";case"1.2.840.113549.1.1.11":return"SHA256RSA";case"1.2.156.10197.1.501":return"SM3SM2"}}(r),a={},i=0;i<7;i++)Module._GetSignSubject(e,i),a[t[i]]=U8StringData;for(var u=[],l=0;l<7;l++)Module._GetSignIssuer(e,l),u[t[l]]=U8StringData;Module._GetSignSerialNumber(e);var c=U8StringData;Module._GetSignValidDate(e,0);var s=U8StringData;Module._GetSignValidDate(e,1);var f=U8StringData;AccessInfoArray=[],Module._GetAuthorityInfoAccess(e);var d=AccessInfoArray;Module._GetSubjectKeyIdentifier(e);var p=U8StringData,h=Module._GetKeyUsage(e);PolicyArray=[],Module._GetCertificatePolicies(e);var g=PolicyArray;Module._GetAuthorityKeyIdentifier(e);var v=U8StringData;CRLPointArray=[],Module._GetCRLDistributionPoints(e);var _=CRLPointArray;Module._GetBasicConstraints(e);var A=U8StringData;Module._GetPublicKey(e);var m=U8StringData;Module._GetX509Data(e);var y=U8StringData;Module._GetSHA1Digest(e);var P=U8StringData;Module._GetMD5Digest(e);var M=U8StringData;return OCSPUrlArray=[],Module._GetOCSPUrl(e),{version:n,signAlgOid:r,signAlgOidType:o,subject:a,issuer:u,serialNumber:c,validDateStart:s,validDateEnd:f,sccessInfo:d,subjectKeyIdentifier:p,keyUsage:h,certificatePolicies:g,authorityKeyIdentifier:v,CRLDistributionPoints:_,basicConstraints:A,publicKey:m,X509Data:y,SHA1Digest:P,MD5Digest:M,ocspURL:OCSPUrlArray}}function st(e){return ft.apply(this,arguments)}function ft(){return(ft=i(n().mark((function e(t){var r,o;return n().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:r=ue(t.imageBase64),o=ComPDFKitJS.opened_image.length,ComPDFKitJS.opened_image[o]=r,Module._UpdateAPWithImage(t.annotPtr,o,r.length,1,0);case 4:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function dt(e){var t=e.annotPtr,n=e.content,r=void 0===n?"":n,o=e.isDrawOnlyContent,a=void 0!==o&&o,i=e.isDrawLogo,u=void 0!==i&&i,l=e.isContentAlginLeft,c=void 0!==l&&l,s=e.text,f=void 0===s?"":s,d=e.logoBase64,p=void 0===d?"":d,h=e.imageBase64,g=void 0===h?"":h,v=stringToNewUTF8(r),_=stringToNewUTF8(f);ComPDFKitJS.opened_image=[];var A=ComPDFKitJS.opened_image.length,m=0;if(p){var y=ue(p);ComPDFKitJS.opened_image[A]=y,m=y.length}var P=ComPDFKitJS.opened_image.length,M=0;if(g){var w=ue(g);ComPDFKitJS.opened_image[P]=w,M=w.length,""!==r||""!==f||p||(ComPDFKitJS.opened_image[A]=w,m=w.length,M=0,u=!0)}Module._UpdateApWithDigitalSigConfig(t,v,a,u,c,_,A,m,P,M,0,0,0,0,0,0)}function pt(e,t,n){AnnotArray=[];var r=[];Module._GetReplies(e,t);for(var o=0;o<AnnotArray.length;o++){var a=AnnotArray[o];Rect={},Module._GetAnnotRect(e,a);var i={left:C(Rect.Left),top:C(Rect.Top),right:C(Rect.Right),bottom:C(Rect.Bottom)};Module._GetAnnotCreationDate(a);var u=S(U8StringData);Module._GetAnnotContent(a);var l=U8StringData;Module._GetAnnotTitle(a);var c=U8StringData;Module._GetAnnotAuthor(a);var f=U8StringData,d=C(Module._GetAnnotTransparency(a));U8StringData="",Module._GetAnnotDictName(a,stringToNewUTF8("NM"));var p=U8StringData;p||(p=P(),Module._SetAnnotDictName(a,stringToNewUTF8("NM"),stringToNewUTF8(p)));var h=Module._GetAnnotBorderWidth(a),g=s(s(s(s({inReplyTo:n,date:u,contents:l,rect:i,opacity:d,name:p,pagePtr:e,annotPtr:a,title:c,author:f},"date",u),"opacity",d),"borderWidth",h),"fillTransparency",1);if(Module._IsReviewStateAnnot(a)){var v=Module._GetState(a);g.state=ee[v],g.stateModel="Review"}if(Module._IsMarkedStateAnnot(a)){var _=Module._GetState(a);g.state=ee[_],g.stateModel="Marked"}r.push(g)}return r}function ht(e,t){var n=t.pagePtr,r=t.annotPtr,o=t.rulerPtr,a=t.color,i=t.borderColor,u=t.borderWidth,l=t.contents,c=t.date,s=t.opacity,f=t.fillTransparency,d=t.rect,p=t.author,h=t.fillColor,g=t.quadPoints,v=t.fontName,_=t.fontSize,A=t.vertices,m=t.borderStyle,y=t.dashes,P=t.head,M=t.tail,w=t.linePoints,x=t.inkPointes,I=t.arcPoints;i&&Ne({annotPtr:r,borderColor:i}),i&&function(e){var t=e.annotPtr,n=G(e.borderColor);Module._SetAnnotBorderRGBColor(t,n.R,n.G,n.B)}({annotPtr:r,borderColor:i}),Object.hasOwn(t,"fillColor")&&je({annotPtr:r,fillColor:h}),c&&Fe({annotPtr:r,date:c}),isFinite(s)&&(!function(e){var t=e.annotPtr,n=e.opacity,r=void 0===n?1:n;Module._setMeasureTextTransparency(t,r)}({annotPtr:r,opacity:s}),He({annotPtr:r,opacity:s})),f&&Qe({annotPtr:r,fillTransparency:f}),d&&De({pagePtr:n,annotPtr:r,rect:d}),p&&Be({annotPtr:r,author:p}),g&&ze({pagePtr:n,annotPtr:r,quadPoints:g}),u&&Ye({annotPtr:r,borderWidth:u}),(m||y)&&_t({annotPtr:r,borderWidth:u,borderStyle:m,dashes:y}),v&&_&&a&&nt(t),A&&vt({pagePtr:n,annotPtr:r,vertices:A}),null==P&&null==M||Module._SetLineType(r,Q[P.toUpperCase()],Q[M.toUpperCase()]),w&&qe({pagePtr:n,annotPtr:r,linePoints:w}),x&&Ke({pagePtr:n,annotPtr:r,inkPointes:x});var b=t.precision,S=t.scale,T=t.labelPosition,E=t.leadLength,R=t.leadOffset,F=t.leadExtension;(b&&function(e){var t=e.rulerPtr,n=e.precision;Module._setPrecision(t,Math.pow(10,-Math.floor(Math.log10(n))))}({rulerPtr:o,precision:b}),S&&function(e){var t=e.rulerPtr,n=e.scale,r=n.base,o=n.baseUnit,a=n.translate,i=n.translateUnit;Module._setMeasureScale(t,r,stringToNewUTF8(o),a,stringToNewUTF8(i))}({rulerPtr:o,scale:S}),T&&function(e){var t=e.rulerPtr,n=e.labelPosition;if("top"===n)return Module._setLabelTop(t);if("inside"===n)Module._setLabelInside(t)}({rulerPtr:o,labelPosition:T}),E&&function(e){var t=e.rulerPtr,n=e.leadLength;Module._setLeadLength(t,n)}({rulerPtr:o,leadLength:E}),R&&function(e){var t=e.rulerPtr,n=e.leadOffset;Module._setLeadOffset(t,n)}({rulerPtr:o,leadOffset:R}),F&&function(e){var t=e.rulerPtr,n=e.leadExtension;Module._setLeadExtension(t,n)}({rulerPtr:o,leadExtension:F}),I)&&(PageSize.Width=0,PageSize.Height=0,Module._GetPageSizeForIndex(e,t.pageIndex),function(e){for(var t=e.rulerPtr,n=e.arcPoints,r=e.pageHeight,o=function(e,t){var n=e.map((function(e){return{x:e.x,y:t-e.y}}));return n}(n,r),a=[],i=0;i<o.length;i++){var u=o[i];a.push(u.x,u.y)}for(var l=a.length,c=_malloc(4*l),s=0;s<l;s++)Module.HEAPF32[c/4+s]=a[s];var f=Module._SetArcPoints(t,c);_free(c)}({rulerPtr:o,arcPoints:I,pageHeight:PageSize.Height}),Rect={},Module._GetArcRect(t.rulerPtr),De({pagePtr:n,annotPtr:r,rect:{left:C(Rect.Left),top:C(PageSize.Height-Rect.Top),right:C(Rect.Right),bottom:C(PageSize.Height-Rect.Bottom)}}));Module._UpdateAnnotMeasure(o),t.hasOwnProperty("contents")&&Le({annotPtr:r,contents:l}),Module._UpdateAnnotAp(r,0),x&&u&&i&&s&&function(e){for(var t=e.pagePtr,n=e.annotPtr,r=e.inkPointes,o=e.borderWidth,a=e.borderColor,i=e.opacity,u=[],l=[],c=r.length,s=0;s<c;s++){for(var f=r[s].length,d=0;d<f;d++)u.push(r[s][d].PointX,r[s][d].PointY);l.push(f)}for(var p=l.length,h=_malloc(4*p),g=0;g<p;g++)Module.HEAP32[h/4+g]=l[g];var v=u.length,_=_malloc(4*v);for(g=0;g<v;g++)Module.HEAPF32[_/4+g]=u[g];var A=G(a);Module._UpdateAnnotApWithInk(t,n,h,c,_,o,A.R,A.G,A.B,i),_free(_)}({pagePtr:n,annotPtr:r,inkPointes:x,borderWidth:u,borderColor:i,opacity:s})}function gt(e,t,n){MeasureInfo={},Module._getMeasureInfo(t);var r=Module._getCaptionTypes(e),o=Module._GetMeasureType(t);Module._setLabelInside(t);var a={captionType:oe[r],measureType:ne[o],scale:{base:MeasureInfo.RulerBase,baseUnit:MeasureInfo.RulerBaseUnit,translate:MeasureInfo.RulerTranslate,translateUnit:MeasureInfo.RulerTranslateUnit},precision:MeasureInfo.precision,factor:MeasureInfo.factor};FontDa={},Module._GetAnnotFontDa(e);var i=U8StringData;switch(a.fontName=i,a.fontSize=FontDa.fontSize,a.color=F(FontDa.R,FontDa.G,FontDa.B),RGBColor={},hasColor=Module._GetAnnotRGBColor(e),hasColor&&(a.borderColor=F(RGBColor.R,RGBColor.G,RGBColor.B)),n){case"line":a.length=Module._getMeasurementResults(t,re.LENGTH),a.leadOffset=Module._getLeadOffset(t);var u=Module._getLeadLength(t),l=Module._getLeadExtension(t);u<0&&(l-=u,u=0),l<0&&(u-=l,l=0),a.leadLength=u,a.leadExtension=l,a.labelPosition="inside";break;case"rectangle":case"circle":case"polygon":a.area=Module._getMeasurementResults(t,re.AREA),a.perimeter=Module._getMeasurementResults(t,re.LENGTH),a.labelPosition="inside";break;case"polyline":a.perimeter=Module._getMeasurementResults(t,re.LENGTH);break;case"ink":"generic"===a.captionType&&(a.perimeter=Module._getMeasurementResults(t,re.GENERIC)),"arclength"===a.captionType&&(a.arcLength=Module._getMeasurementResults(t,re.ARCLENGTH))}return a}function vt(e){for(var t=e.pagePtr,n=e.annotPtr,r=e.vertices,o=[],a=0;a<r.length;a++){var i=r[a];o.push(i.x,i.y)}for(var u=o.length,l=_malloc(4*u),c=0;c<u;c++)Module.HEAPF32[l/4+c]=o[c];var s=Module._setVertices(t,n,l,u);return _free(l),s}function _t(e){for(var t=e.annotPtr,n=e.dashes,r=void 0===n?[]:n,o=e.borderStyle,a=e.borderWidth,i=J[o],u=r.length,l=_malloc(4*u),c=0;c<u;c++)Module.HEAPF32[l/4+c]=r[c];var s=Module._SetAnnotBorderStyle(t,i,a,l,u);return _free(l),s}function At(e){var t=e.pagePtr,n=e.annotPtr;MeasureVerticesArray=[],Module._getVertices(t,n);for(var r=[],o=0;o<MeasureVerticesArray.length;o++){var a=MeasureVerticesArray[o];a&&r.push({x:a.PointX,y:a.PointY})}return r}(function(){return he.apply(this,arguments)})().then((function(){pe.setUp()}));
