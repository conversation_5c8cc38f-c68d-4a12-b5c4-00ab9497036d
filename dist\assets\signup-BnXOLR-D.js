import{r as l,u as f,j as e,L as x}from"./index-BNo7PP7q.js";import{A as b}from"./auth-TFDPTwFC.js";import{u as j,F as m,B as n,c as o,a as w}from"./rules-B0Vq_sHo.js";import{P as C}from"./index-BNdc7YlR.js";const P=()=>{const[c,t]=l.useState(),p=f(),{loading:d,postData:u}=j("create_company",{type:"submit"}),r=window.helper.generateRanges(50,1e3),h=a=>{if(!a){t("");return}const s=a.replace(/\D/g,"").slice(1);s.length===10?t(`+1 ${s.slice(0,3)} ${s.slice(3,6)} ${s.slice(6)}`):s.length<10&&t(a)},g=a=>{const s=new FormData;s.append("role","company");for(const i in a)s.append(i,a[i]);u(s,y)},y=a=>{a.statusCode==200&&p("/login/company")};return e.jsxs(m,{name:"login",layout:"vertical",onFinish:g,initialValues:{remember:!0},autoComplete:"off",children:[e.jsx(n,{name:"email",placeholder:"Email Address",label:"Email Address",rules:o.email}),e.jsx(n,{name:"company_name",placeholder:"Company Name",label:"Company Name",rules:o.company_name}),e.jsx(n,{type:"textarea",name:"company_details",placeholder:"Company Details",label:"Company Details",rules:o.company_details}),e.jsx(n,{type:"select",name:"company_size",placeholder:"Company Size",label:"Company Size",options:r==null?void 0:r.map(a=>({value:a.key,label:a.value})),rules:o.company_size}),e.jsx("div",{className:"phone-input-container",children:e.jsx(m.Item,{label:"Phone Number",name:"mobile_no",validateTrigger:"onBlur",rules:[{required:!0,message:"Phone Number is required!"},{validator:(a,s)=>s&&s.replace(/\D/g,"").slice(1).length!==10?Promise.reject("Enter a valid Phone number"):Promise.resolve()}],children:e.jsx(C,{id:"mobile_no",placeholder:"Enter phone number",value:c,onChange:h,className:"base-input",international:!1,defaultCountry:"US"})})}),e.jsx(n,{type:"password",name:"password",id:"password",placeholder:"Password",label:"Password",rules:o.password}),e.jsx(n,{type:"password",name:"confirmPassword",id:"confirmPassword",placeholder:"Confirm Password",label:"Confirm Password",rules:o.confirmpassword}),e.jsx("div",{children:e.jsx(w,{title:"Sign up",className:"mx-auto mt-4 signin-btn mt-5",htmlType:"submit",loading:d})}),e.jsx("div",{children:e.jsxs("p",{className:"signup-text",children:["Don’t have an account?",e.jsx(x,{to:"/login/company",className:"color-blue font-600 font-16 ms-1",children:"Sign in"})]})})]})},v=l.memo(P),_=()=>e.jsx(b,{src:"/admin/assets/img/signup-img.png",logoClass:"d-none",title:"Create an account",detail:"Let’s get started with your basic information",children:e.jsx(v,{})}),E=l.memo(_);export{E as default};
