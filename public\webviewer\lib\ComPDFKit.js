var Module=typeof Module!="undefined"?Module:{};var moduleOverrides=Object.assign({},Module);var arguments_=[];var thisProgram="./this.program";var quit_=(status,toThrow)=>{throw toThrow};var ENVIRONMENT_IS_WEB=typeof window=="object";var ENVIRONMENT_IS_WORKER=typeof importScripts=="function";var ENVIRONMENT_IS_NODE=typeof process=="object"&&typeof process.versions=="object"&&typeof process.versions.node=="string";var ENVIRONMENT_IS_SHELL=!ENVIRONMENT_IS_WEB&&!ENVIRONMENT_IS_NODE&&!ENVIRONMENT_IS_WORKER;if(Module["ENVIRONMENT"]){throw new Error("Module.ENVIRONMENT has been deprecated. To force the environment, use the ENVIRONMENT compile-time option (for example, -sENVIRONMENT=web or -sENVIRONMENT=node)")}var scriptDirectory="";function locateFile(path){if(Module["locateFile"]){return Module["locateFile"](path,scriptDirectory)}return scriptDirectory+path}var read_,readAsync,readBinary,setWindowTitle;if(ENVIRONMENT_IS_NODE){if(typeof process=="undefined"||!process.release||process.release.name!=="node")throw new Error("not compiled for this environment (did you build to HTML and try to run it not on the web, or set ENVIRONMENT to something - like node - and run it someplace else - like on the web?)");var nodeVersion=process.versions.node;var numericVersion=nodeVersion.split(".").slice(0,3);numericVersion=numericVersion[0]*1e4+numericVersion[1]*100+numericVersion[2].split("-")[0]*1;if(numericVersion<16e4){throw new Error("This emscripten-generated code requires node v16.0.0 (detected v"+nodeVersion+")")}var fs=require("fs");var nodePath=require("path");if(ENVIRONMENT_IS_WORKER){scriptDirectory=nodePath.dirname(scriptDirectory)+"/"}else{scriptDirectory=__dirname+"/"}read_=(filename,binary)=>{filename=isFileURI(filename)?new URL(filename):nodePath.normalize(filename);return fs.readFileSync(filename,binary?undefined:"utf8")};readBinary=filename=>{var ret=read_(filename,true);if(!ret.buffer){ret=new Uint8Array(ret)}assert(ret.buffer);return ret};readAsync=(filename,onload,onerror,binary=true)=>{filename=isFileURI(filename)?new URL(filename):nodePath.normalize(filename);fs.readFile(filename,binary?undefined:"utf8",(err,data)=>{if(err)onerror(err);else onload(binary?data.buffer:data)})};if(!Module["thisProgram"]&&process.argv.length>1){thisProgram=process.argv[1].replace(/\\/g,"/")}arguments_=process.argv.slice(2);if(typeof module!="undefined"){module["exports"]=Module}process.on("uncaughtException",ex=>{if(ex!=="unwind"&&!(ex instanceof ExitStatus)&&!(ex.context instanceof ExitStatus)){throw ex}});quit_=(status,toThrow)=>{process.exitCode=status;throw toThrow};Module["inspect"]=()=>"[Emscripten Module object]"}else if(ENVIRONMENT_IS_SHELL){if(typeof process=="object"&&typeof require==="function"||typeof window=="object"||typeof importScripts=="function")throw new Error("not compiled for this environment (did you build to HTML and try to run it not on the web, or set ENVIRONMENT to something - like node - and run it someplace else - like on the web?)");if(typeof read!="undefined"){read_=f=>{return read(f)}}readBinary=f=>{let data;if(typeof readbuffer=="function"){return new Uint8Array(readbuffer(f))}data=read(f,"binary");assert(typeof data=="object");return data};readAsync=(f,onload,onerror)=>{setTimeout(()=>onload(readBinary(f)))};if(typeof clearTimeout=="undefined"){globalThis.clearTimeout=id=>{}}if(typeof setTimeout=="undefined"){globalThis.setTimeout=f=>typeof f=="function"?f():abort()}if(typeof scriptArgs!="undefined"){arguments_=scriptArgs}else if(typeof arguments!="undefined"){arguments_=arguments}if(typeof quit=="function"){quit_=(status,toThrow)=>{setTimeout(()=>{if(!(toThrow instanceof ExitStatus)){let toLog=toThrow;if(toThrow&&typeof toThrow=="object"&&toThrow.stack){toLog=[toThrow,toThrow.stack]}err(`exiting due to exception: ${toLog}`)}quit(status)});throw toThrow}}if(typeof print!="undefined"){if(typeof console=="undefined")console={};console.log=print;console.warn=console.error=typeof printErr!="undefined"?printErr:print}}else if(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER){if(ENVIRONMENT_IS_WORKER){scriptDirectory=self.location.href}else if(typeof document!="undefined"&&document.currentScript){scriptDirectory=document.currentScript.src}if(scriptDirectory.indexOf("blob:")!==0){scriptDirectory=scriptDirectory.substr(0,scriptDirectory.replace(/[?#].*/,"").lastIndexOf("/")+1)}else{scriptDirectory=""}if(!(typeof window=="object"||typeof importScripts=="function"))throw new Error("not compiled for this environment (did you build to HTML and try to run it not on the web, or set ENVIRONMENT to something - like node - and run it someplace else - like on the web?)");{read_=url=>{var xhr=new XMLHttpRequest;xhr.open("GET",url,false);xhr.send(null);return xhr.responseText};if(ENVIRONMENT_IS_WORKER){readBinary=url=>{var xhr=new XMLHttpRequest;xhr.open("GET",url,false);xhr.responseType="arraybuffer";xhr.send(null);return new Uint8Array(xhr.response)}}readAsync=(url,onload,onerror)=>{var xhr=new XMLHttpRequest;xhr.open("GET",url,true);xhr.responseType="arraybuffer";xhr.onload=()=>{if(xhr.status==200||xhr.status==0&&xhr.response){onload(xhr.response);return}onerror()};xhr.onerror=onerror;xhr.send(null)}}setWindowTitle=title=>document.title=title}else{throw new Error("environment detection error")}var out=Module["print"]||console.log.bind(console);var err=Module["printErr"]||console.error.bind(console);Object.assign(Module,moduleOverrides);moduleOverrides=null;checkIncomingModuleAPI();if(Module["arguments"])arguments_=Module["arguments"];legacyModuleProp("arguments","arguments_");if(Module["thisProgram"])thisProgram=Module["thisProgram"];legacyModuleProp("thisProgram","thisProgram");if(Module["quit"])quit_=Module["quit"];legacyModuleProp("quit","quit_");assert(typeof Module["memoryInitializerPrefixURL"]=="undefined","Module.memoryInitializerPrefixURL option was removed, use Module.locateFile instead");assert(typeof Module["pthreadMainPrefixURL"]=="undefined","Module.pthreadMainPrefixURL option was removed, use Module.locateFile instead");assert(typeof Module["cdInitializerPrefixURL"]=="undefined","Module.cdInitializerPrefixURL option was removed, use Module.locateFile instead");assert(typeof Module["filePackagePrefixURL"]=="undefined","Module.filePackagePrefixURL option was removed, use Module.locateFile instead");assert(typeof Module["read"]=="undefined","Module.read option was removed (modify read_ in JS)");assert(typeof Module["readAsync"]=="undefined","Module.readAsync option was removed (modify readAsync in JS)");assert(typeof Module["readBinary"]=="undefined","Module.readBinary option was removed (modify readBinary in JS)");assert(typeof Module["setWindowTitle"]=="undefined","Module.setWindowTitle option was removed (modify setWindowTitle in JS)");assert(typeof Module["TOTAL_MEMORY"]=="undefined","Module.TOTAL_MEMORY has been renamed Module.INITIAL_MEMORY");legacyModuleProp("read","read_");legacyModuleProp("readAsync","readAsync");legacyModuleProp("readBinary","readBinary");legacyModuleProp("setWindowTitle","setWindowTitle");assert(!ENVIRONMENT_IS_SHELL,"shell environment detected but not enabled at build time.  Add 'shell' to `-sENVIRONMENT` to enable.");var wasmBinary;if(Module["wasmBinary"])wasmBinary=Module["wasmBinary"];legacyModuleProp("wasmBinary","wasmBinary");var noExitRuntime=Module["noExitRuntime"]||true;legacyModuleProp("noExitRuntime","noExitRuntime");if(typeof WebAssembly!="object"){abort("no native wasm support detected")}var wasmMemory;var ABORT=false;var EXITSTATUS;function assert(condition,text){if(!condition){abort("Assertion failed"+(text?": "+text:""))}}var HEAP8,HEAPU8,HEAP16,HEAPU16,HEAP32,HEAPU32,HEAPF32,HEAPF64;function updateMemoryViews(){var b=wasmMemory.buffer;Module["HEAP8"]=HEAP8=new Int8Array(b);Module["HEAP16"]=HEAP16=new Int16Array(b);Module["HEAP32"]=HEAP32=new Int32Array(b);Module["HEAPU8"]=HEAPU8=new Uint8Array(b);Module["HEAPU16"]=HEAPU16=new Uint16Array(b);Module["HEAPU32"]=HEAPU32=new Uint32Array(b);Module["HEAPF32"]=HEAPF32=new Float32Array(b);Module["HEAPF64"]=HEAPF64=new Float64Array(b)}assert(!Module["STACK_SIZE"],"STACK_SIZE can no longer be set at runtime.  Use -sSTACK_SIZE at link time");assert(typeof Int32Array!="undefined"&&typeof Float64Array!=="undefined"&&Int32Array.prototype.subarray!=undefined&&Int32Array.prototype.set!=undefined,"JS engine does not provide full typed array support");assert(!Module["wasmMemory"],"Use of `wasmMemory` detected.  Use -sIMPORTED_MEMORY to define wasmMemory externally");assert(!Module["INITIAL_MEMORY"],"Detected runtime INITIAL_MEMORY setting.  Use -sIMPORTED_MEMORY to define wasmMemory dynamically");var wasmTable;function writeStackCookie(){var max=_emscripten_stack_get_end();assert((max&3)==0);if(max==0){max+=4}HEAPU32[max>>2]=34821223;HEAPU32[max+4>>2]=2310721022;HEAPU32[0>>2]=1668509029}function checkStackCookie(){if(ABORT)return;var max=_emscripten_stack_get_end();if(max==0){max+=4}var cookie1=HEAPU32[max>>2];var cookie2=HEAPU32[max+4>>2];if(cookie1!=34821223||cookie2!=2310721022){abort(`Stack overflow! Stack cookie has been overwritten at ${ptrToString(max)}, expected hex dwords 0x89BACDFE and 0x2135467, but received ${ptrToString(cookie2)} ${ptrToString(cookie1)}`)}if(HEAPU32[0>>2]!=1668509029){abort("Runtime error: The application has corrupted its heap memory area (address zero)!")}}(function(){var h16=new Int16Array(1);var h8=new Int8Array(h16.buffer);h16[0]=25459;if(h8[0]!==115||h8[1]!==99)throw"Runtime error: expected the system to be little-endian! (Run with -sSUPPORT_BIG_ENDIAN to bypass)"})();var __ATPRERUN__=[];var __ATINIT__=[];var __ATPOSTRUN__=[];var runtimeInitialized=false;var runtimeKeepaliveCounter=0;function preRun(){if(Module["preRun"]){if(typeof Module["preRun"]=="function")Module["preRun"]=[Module["preRun"]];while(Module["preRun"].length){addOnPreRun(Module["preRun"].shift())}}callRuntimeCallbacks(__ATPRERUN__)}function initRuntime(){assert(!runtimeInitialized);runtimeInitialized=true;checkStackCookie();if(!Module["noFSInit"]&&!FS.init.initialized)FS.init();FS.ignorePermissions=false;TTY.init();callRuntimeCallbacks(__ATINIT__)}function postRun(){checkStackCookie();if(Module["postRun"]){if(typeof Module["postRun"]=="function")Module["postRun"]=[Module["postRun"]];while(Module["postRun"].length){addOnPostRun(Module["postRun"].shift())}}callRuntimeCallbacks(__ATPOSTRUN__)}function addOnPreRun(cb){__ATPRERUN__.unshift(cb)}function addOnInit(cb){__ATINIT__.unshift(cb)}function addOnPostRun(cb){__ATPOSTRUN__.unshift(cb)}assert(Math.imul,"This browser does not support Math.imul(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill");assert(Math.fround,"This browser does not support Math.fround(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill");assert(Math.clz32,"This browser does not support Math.clz32(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill");assert(Math.trunc,"This browser does not support Math.trunc(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill");var runDependencies=0;var runDependencyWatcher=null;var dependenciesFulfilled=null;var runDependencyTracking={};function getUniqueRunDependency(id){var orig=id;while(1){if(!runDependencyTracking[id])return id;id=orig+Math.random()}}function addRunDependency(id){runDependencies++;if(Module["monitorRunDependencies"]){Module["monitorRunDependencies"](runDependencies)}if(id){assert(!runDependencyTracking[id]);runDependencyTracking[id]=1;if(runDependencyWatcher===null&&typeof setInterval!="undefined"){runDependencyWatcher=setInterval(()=>{if(ABORT){clearInterval(runDependencyWatcher);runDependencyWatcher=null;return}var shown=false;for(var dep in runDependencyTracking){if(!shown){shown=true;err("still waiting on run dependencies:")}err("dependency: "+dep)}if(shown){err("(end of list)")}},1e4)}}else{err("warning: run dependency added without ID")}}function removeRunDependency(id){runDependencies--;if(Module["monitorRunDependencies"]){Module["monitorRunDependencies"](runDependencies)}if(id){assert(runDependencyTracking[id]);delete runDependencyTracking[id]}else{err("warning: run dependency removed without ID")}if(runDependencies==0){if(runDependencyWatcher!==null){clearInterval(runDependencyWatcher);runDependencyWatcher=null}if(dependenciesFulfilled){var callback=dependenciesFulfilled;dependenciesFulfilled=null;callback()}}}function abort(what){if(Module["onAbort"]){Module["onAbort"](what)}what="Aborted("+what+")";err(what);ABORT=true;EXITSTATUS=1;var e=new WebAssembly.RuntimeError(what);throw e}var dataURIPrefix="data:application/octet-stream;base64,";function isDataURI(filename){return filename.startsWith(dataURIPrefix)}function isFileURI(filename){return filename.startsWith("file://")}function createExportWrapper(name,fixedasm){return function(){var displayName=name;var asm=fixedasm;if(!fixedasm){asm=Module["asm"]}assert(runtimeInitialized,"native function `"+displayName+"` called before runtime initialization");if(!asm[name]){assert(asm[name],"exported native function `"+displayName+"` not found")}return asm[name].apply(null,arguments)}}class EmscriptenEH extends Error{}class EmscriptenSjLj extends EmscriptenEH{}class CppException extends EmscriptenEH{constructor(excPtr){super(excPtr);this.excPtr=excPtr;const excInfo=getExceptionMessage(excPtr);this.name=excInfo[0];this.message=excInfo[1]}}var wasmBinaryFile;wasmBinaryFile="ComPDFKit.wasm";if(!isDataURI(wasmBinaryFile)){wasmBinaryFile=locateFile(wasmBinaryFile)}function getBinary(file){try{if(file==wasmBinaryFile&&wasmBinary){return new Uint8Array(wasmBinary)}if(readBinary){return readBinary(file)}throw"both async and sync fetching of the wasm failed"}catch(err){abort(err)}}function getBinaryPromise(binaryFile){if(!wasmBinary&&(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER)){if(typeof fetch=="function"&&!isFileURI(binaryFile)){return fetch(binaryFile,{credentials:"same-origin"}).then(response=>{if(!response["ok"]){throw"failed to load wasm binary file at '"+binaryFile+"'"}return response["arrayBuffer"]()}).catch(()=>getBinary(binaryFile))}else{if(readAsync){return new Promise((resolve,reject)=>{readAsync(binaryFile,response=>resolve(new Uint8Array(response)),reject)})}}}return Promise.resolve().then(()=>getBinary(binaryFile))}function instantiateArrayBuffer(binaryFile,imports,receiver){return getBinaryPromise(binaryFile).then(binary=>{return WebAssembly.instantiate(binary,imports)}).then(instance=>{return instance}).then(receiver,reason=>{err("failed to asynchronously prepare wasm: "+reason);if(isFileURI(wasmBinaryFile)){err("warning: Loading from a file URI ("+wasmBinaryFile+") is not supported in most browsers. See https://emscripten.org/docs/getting_started/FAQ.html#how-do-i-run-a-local-webserver-for-testing-why-does-my-program-stall-in-downloading-or-preparing")}abort(reason)})}function instantiateAsync(binary,binaryFile,imports,callback){if(!binary&&typeof WebAssembly.instantiateStreaming=="function"&&!isDataURI(binaryFile)&&!isFileURI(binaryFile)&&!ENVIRONMENT_IS_NODE&&typeof fetch=="function"){return fetch(binaryFile,{credentials:"same-origin"}).then(response=>{var result=WebAssembly.instantiateStreaming(response,imports);return result.then(callback,function(reason){err("wasm streaming compile failed: "+reason);err("falling back to ArrayBuffer instantiation");return instantiateArrayBuffer(binaryFile,imports,callback)})})}else{return instantiateArrayBuffer(binaryFile,imports,callback)}}function createWasm(){var info={"env":wasmImports,"wasi_snapshot_preview1":wasmImports};function receiveInstance(instance,module){var exports=instance.exports;Module["asm"]=exports;wasmMemory=Module["asm"]["memory"];assert(wasmMemory,"memory not found in wasm exports");updateMemoryViews();wasmTable=Module["asm"]["__indirect_function_table"];assert(wasmTable,"table not found in wasm exports");addOnInit(Module["asm"]["__wasm_call_ctors"]);removeRunDependency("wasm-instantiate");return exports}addRunDependency("wasm-instantiate");var trueModule=Module;function receiveInstantiationResult(result){assert(Module===trueModule,"the Module object should not be replaced during async compilation - perhaps the order of HTML elements is wrong?");trueModule=null;receiveInstance(result["instance"])}if(Module["instantiateWasm"]){try{return Module["instantiateWasm"](info,receiveInstance)}catch(e){err("Module.instantiateWasm callback failed with error: "+e);return false}}instantiateAsync(wasmBinary,wasmBinaryFile,info,receiveInstantiationResult);return{}}var tempDouble;var tempI64;function legacyModuleProp(prop,newName){if(!Object.getOwnPropertyDescriptor(Module,prop)){Object.defineProperty(Module,prop,{configurable:true,get:function(){abort("Module."+prop+" has been replaced with plain "+newName+" (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)")}})}}function ignoredModuleProp(prop){if(Object.getOwnPropertyDescriptor(Module,prop)){abort("`Module."+prop+"` was supplied but `"+prop+"` not included in INCOMING_MODULE_JS_API")}}function isExportedByForceFilesystem(name){return name==="FS_createPath"||name==="FS_createDataFile"||name==="FS_createPreloadedFile"||name==="FS_unlink"||name==="addRunDependency"||name==="FS_createLazyFile"||name==="FS_createDevice"||name==="removeRunDependency"}function missingGlobal(sym,msg){if(typeof globalThis!=="undefined"){Object.defineProperty(globalThis,sym,{configurable:true,get:function(){warnOnce("`"+sym+"` is not longer defined by emscripten. "+msg);return undefined}})}}missingGlobal("buffer","Please use HEAP8.buffer or wasmMemory.buffer");function missingLibrarySymbol(sym){if(typeof globalThis!=="undefined"&&!Object.getOwnPropertyDescriptor(globalThis,sym)){Object.defineProperty(globalThis,sym,{configurable:true,get:function(){var msg="`"+sym+"` is a library symbol and not included by default; add it to your library.js __deps or to DEFAULT_LIBRARY_FUNCS_TO_INCLUDE on the command line";var librarySymbol=sym;if(!librarySymbol.startsWith("_")){librarySymbol="$"+sym}msg+=" (e.g. -sDEFAULT_LIBRARY_FUNCS_TO_INCLUDE='"+librarySymbol+"')";if(isExportedByForceFilesystem(sym)){msg+=". Alternatively, forcing filesystem support (-sFORCE_FILESYSTEM) can export this for you"}warnOnce(msg);return undefined}})}unexportedRuntimeSymbol(sym)}function unexportedRuntimeSymbol(sym){if(!Object.getOwnPropertyDescriptor(Module,sym)){Object.defineProperty(Module,sym,{configurable:true,get:function(){var msg="'"+sym+"' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)";if(isExportedByForceFilesystem(sym)){msg+=". Alternatively, forcing filesystem support (-sFORCE_FILESYSTEM) can export this for you"}abort(msg)}})}}function ExitStatus(status){this.name="ExitStatus";this.message=`Program terminated with exit(${status})`;this.status=status}var callRuntimeCallbacks=callbacks=>{while(callbacks.length>0){callbacks.shift()(Module)}};var withStackSave=f=>{var stack=stackSave();var ret=f();stackRestore(stack);return ret};var lengthBytesUTF8=str=>{var len=0;for(var i=0;i<str.length;++i){var c=str.charCodeAt(i);if(c<=127){len++}else if(c<=2047){len+=2}else if(c>=55296&&c<=57343){len+=4;++i}else{len+=3}}return len};var stringToUTF8Array=(str,heap,outIdx,maxBytesToWrite)=>{assert(typeof str==="string");if(!(maxBytesToWrite>0))return 0;var startIdx=outIdx;var endIdx=outIdx+maxBytesToWrite-1;for(var i=0;i<str.length;++i){var u=str.charCodeAt(i);if(u>=55296&&u<=57343){var u1=str.charCodeAt(++i);u=65536+((u&1023)<<10)|u1&1023}if(u<=127){if(outIdx>=endIdx)break;heap[outIdx++]=u}else if(u<=2047){if(outIdx+1>=endIdx)break;heap[outIdx++]=192|u>>6;heap[outIdx++]=128|u&63}else if(u<=65535){if(outIdx+2>=endIdx)break;heap[outIdx++]=224|u>>12;heap[outIdx++]=128|u>>6&63;heap[outIdx++]=128|u&63}else{if(outIdx+3>=endIdx)break;if(u>1114111)warnOnce("Invalid Unicode code point "+ptrToString(u)+" encountered when serializing a JS string to a UTF-8 string in wasm memory! (Valid unicode code points should be in range 0-0x10FFFF).");heap[outIdx++]=240|u>>18;heap[outIdx++]=128|u>>12&63;heap[outIdx++]=128|u>>6&63;heap[outIdx++]=128|u&63}}heap[outIdx]=0;return outIdx-startIdx};var stringToUTF8=(str,outPtr,maxBytesToWrite)=>{assert(typeof maxBytesToWrite=="number","stringToUTF8(str, outPtr, maxBytesToWrite) is missing the third parameter that specifies the length of the output buffer!");return stringToUTF8Array(str,HEAPU8,outPtr,maxBytesToWrite)};var stringToUTF8OnStack=str=>{var size=lengthBytesUTF8(str)+1;var ret=stackAlloc(size);stringToUTF8(str,ret,size);return ret};var UTF8Decoder=typeof TextDecoder!="undefined"?new TextDecoder("utf8"):undefined;var UTF8ArrayToString=(heapOrArray,idx,maxBytesToRead)=>{var endIdx=idx+maxBytesToRead;var endPtr=idx;while(heapOrArray[endPtr]&&!(endPtr>=endIdx))++endPtr;if(endPtr-idx>16&&heapOrArray.buffer&&UTF8Decoder){return UTF8Decoder.decode(heapOrArray.subarray(idx,endPtr))}var str="";while(idx<endPtr){var u0=heapOrArray[idx++];if(!(u0&128)){str+=String.fromCharCode(u0);continue}var u1=heapOrArray[idx++]&63;if((u0&224)==192){str+=String.fromCharCode((u0&31)<<6|u1);continue}var u2=heapOrArray[idx++]&63;if((u0&240)==224){u0=(u0&15)<<12|u1<<6|u2}else{if((u0&248)!=240)warnOnce("Invalid UTF-8 leading byte "+ptrToString(u0)+" encountered when deserializing a UTF-8 string in wasm memory to a JS string!");u0=(u0&7)<<18|u1<<12|u2<<6|heapOrArray[idx++]&63}if(u0<65536){str+=String.fromCharCode(u0)}else{var ch=u0-65536;str+=String.fromCharCode(55296|ch>>10,56320|ch&1023)}}return str};var UTF8ToString=(ptr,maxBytesToRead)=>{assert(typeof ptr=="number");return ptr?UTF8ArrayToString(HEAPU8,ptr,maxBytesToRead):""};function demangle(func){demangle.recursionGuard=(demangle.recursionGuard|0)+1;if(demangle.recursionGuard>1)return func;return withStackSave(function(){try{var s=func;if(s.startsWith("__Z"))s=s.substr(1);var buf=stringToUTF8OnStack(s);var status=stackAlloc(4);var ret=___cxa_demangle(buf,0,0,status);if(HEAP32[status>>2]===0&&ret){return UTF8ToString(ret)}}catch(e){}finally{_free(ret);if(demangle.recursionGuard<2)--demangle.recursionGuard}return func})}var getExceptionMessageCommon=ptr=>withStackSave(()=>{var type_addr_addr=stackAlloc(4);var message_addr_addr=stackAlloc(4);___get_exception_message(ptr,type_addr_addr,message_addr_addr);var type_addr=HEAPU32[type_addr_addr>>2];var message_addr=HEAPU32[message_addr_addr>>2];var type=UTF8ToString(type_addr);_free(type_addr);var message;if(message_addr){message=UTF8ToString(message_addr);_free(message_addr)}return[type,message]});function getExceptionMessage(ptr){return getExceptionMessageCommon(ptr)}Module["getExceptionMessage"]=getExceptionMessage;var ptrToString=ptr=>{assert(typeof ptr==="number");return"0x"+ptr.toString(16).padStart(8,"0")};function jsStackTrace(){var error=new Error;if(!error.stack){try{throw new Error}catch(e){error=e}if(!error.stack){return"(no stack trace available)"}}return error.stack.toString()}function demangleAll(text){var regex=/\b_Z[\w\d_]+/g;return text.replace(regex,function(x){var y=demangle(x);return x===y?x:y+" ["+x+"]"})}var warnOnce=text=>{if(!warnOnce.shown)warnOnce.shown={};if(!warnOnce.shown[text]){warnOnce.shown[text]=1;if(ENVIRONMENT_IS_NODE)text="warning: "+text;err(text)}};function _ComPDFKitJS_DefFontListNum(pThis){return ComPDFKitJS.opened_DefFont.length}function _ComPDFKitJS_FontCount(pThis){return ComPDFKitJS.FontCount}function _ComPDFKitJS_GetFontName(str){let Content=UTF8ToString(str);ComPDFKitJS.FontNameList.push(Content);return 1}function _ComPDFKitJS_Get_AccessInfoArray(key,accessInfo){AccessInfoArray.push({Key:UTF8ToString(key),AccessInfo:UTF8ToString(accessInfo)});return 1}function _ComPDFKitJS_Get_AnnotArray(Annot){AnnotArray.push(Annot);return 1}function _ComPDFKitJS_Get_AnnotDictName(name){AnnotDictName.push(UTF8ToString(name));return 1}function _ComPDFKitJS_Get_BorderStyle(borderStyle,borderWidth,data){BorderStyle.Style=borderStyle;BorderStyle.Width=borderWidth;BorderStyle.DashArray.push(data);return 1}function _ComPDFKitJS_Get_CRLPointArray(crlPoint){CRLPointArray.push(UTF8ToString(crlPoint));return 1}function _ComPDFKitJS_Get_CertArray(cert){CertArray.push(cert);return 1}function _ComPDFKitJS_Get_Cmyk(c,m,y,k){Cmyk.c=c;Cmyk.m=m;Cmyk.y=y;Cmyk.k=k;return 1}function _ComPDFKitJS_Get_CompareResultArray(resultType,oldPageIndex,newPageIndex,oldLeft,oldTop,oldRight,oldBottom,newLeft,newTop,newRight,newBottom){CompareResultArray.push({resultType:resultType,oldPageIndex:oldPageIndex,newPageIndex:newPageIndex,oldLeft:oldLeft,oldTop:oldTop,oldRight:oldRight,oldBottom:oldBottom,newLeft:newLeft,newTop:newTop,newRight:newRight,newBottom:newBottom});return 1}function _ComPDFKitJS_Get_CursorPoints(lowPointX,lowPointY,highPointX,highPointY){CursorPoints.LowPointX=lowPointX;CursorPoints.LowPointY=lowPointY;CursorPoints.HighPointX=highPointX;CursorPoints.HighPointY=highPointY;return 1}function _ComPDFKitJS_Get_DiffInfoArray(type,pageindex,annot_subtype,annot_formtype,info){DiffInfoArray.push({Type:type,PageIndex:pageindex,Annot_Subtype:annot_subtype,Annot_Formtype:annot_formtype,Info:UTF8ToString(info)});return 1}function _ComPDFKitJS_Get_EditTextStyle(fontSize,transparency,r,g,b,isBold,isItalic){EditTextStyle.FontSize=fontSize;EditTextStyle.Transparency=transparency;EditTextStyle.R=r;EditTextStyle.G=g;EditTextStyle.B=b;EditTextStyle.IsBold=isBold;EditTextStyle.IsItalic=isItalic;return 1}function _ComPDFKitJS_Get_EndPSOPDFEditCharPlace(section_index,line_index,run_index,char_index){EndEditCharPlace.SectionIndex=section_index;EndEditCharPlace.LineIndex=line_index;EndEditCharPlace.RunIndex=run_index;EndEditCharPlace.CharIndex=char_index;return 1}function _ComPDFKitJS_Get_FontAlignment(alignment){FontAlignment=alignment;return 1}function _ComPDFKitJS_Get_FontDa(r,g,b,a,fontSize){FontDa.fontSize=fontSize;FontDa.R=r;FontDa.G=g;FontDa.B=b;FontDa.A=a;return 1}function _ComPDFKitJS_Get_ImageAreaInfo(width,height,stride){ImageAreaInfo.Width=width;ImageAreaInfo.Height=height;ImageAreaInfo.Stride=stride;return 1}function _ComPDFKitJS_Get_InkPoints(x,y){InkPointArray.push({PointX:x,PointY:y});return 1}function _ComPDFKitJS_Get_LinePoint(startPointX,startPointY,endPointX,endPointY){LinePoint.StartPointX=startPointX;LinePoint.StartPointY=startPointY;LinePoint.EndPointX=endPointX;LinePoint.EndPointY=endPointY;return 1}function _ComPDFKitJS_Get_LineType(head,tail){LineType.Head=head;LineType.Tail=tail;return 1}function _ComPDFKitJS_Get_MeasureInfo(factor,unit,decimalSymbol,thousandSymbol,display,precision,unitPrefix,unitSuffix,unitPosition,RulerBase,RulerBaseUnit,RulerTranslate,RulerTranslateUnit,captiontypes){MeasureInfo.factor=factor;MeasureInfo.unit=UTF8ToString(unit);MeasureInfo.decimalSymbol=UTF8ToString(decimalSymbol);MeasureInfo.thousandSymbol=UTF8ToString(thousandSymbol);MeasureInfo.display=UTF8ToString(display);MeasureInfo.precision=precision;MeasureInfo.unitPrefix=UTF8ToString(unitPrefix);MeasureInfo.unitSuffix=UTF8ToString(unitSuffix);MeasureInfo.unitPosition=UTF8ToString(unitPosition);MeasureInfo.RulerBase=RulerBase;MeasureInfo.RulerBaseUnit=UTF8ToString(RulerBaseUnit);MeasureInfo.RulerTranslate=RulerTranslate;MeasureInfo.RulerTranslateUnit=UTF8ToString(RulerTranslateUnit);MeasureInfo.captiontypes=captiontypes;return 1}function _ComPDFKitJS_Get_Measure_Vertices(x,y){MeasureVerticesArray.push({PointX:x,PointY:y});return 1}function _ComPDFKitJS_Get_OCGS(ocgPtr){OCGSArr.push(ocgPtr);return 1}function _ComPDFKitJS_Get_OCSPUrlArray(ocspUrl){OCSPUrlArray.push(UTF8ToString(ocspUrl));return 1}function _ComPDFKitJS_Get_PDFDestination(pageIndex,posX,posY,zoom){PDFDestination.PageIndex=pageIndex;PDFDestination.PosX=posX;PDFDestination.PosY=posY;PDFDestination.Zoom=zoom;return 1}function _ComPDFKitJS_Get_PDFRange(length,location){PDFRange.Length=length;PDFRange.Location=location;return 1}function _ComPDFKitJS_Get_PSOPDFEditCharPlace(section_index,line_index,run_index,char_index){EditCharPlace.SectionIndex=section_index;EditCharPlace.LineIndex=line_index;EditCharPlace.RunIndex=run_index;EditCharPlace.CharIndex=char_index;return 1}function _ComPDFKitJS_Get_PageSize(width,height){PageSize.Width=width;PageSize.Height=height;return 1}function _ComPDFKitJS_Get_PermissionsInfo(allowsPrinting,allowsDocumentChanges,allowsCopying,allowsCommenting,allowsFormFieldEntry,allowsDocumentAssembly,allowsHighQualityPrinting){PermissionsInfo.allowsPrinting=allowsPrinting;PermissionsInfo.allowsDocumentChanges=allowsDocumentChanges;PermissionsInfo.allowsCopying=allowsCopying;PermissionsInfo.allowsCommenting=allowsCommenting;PermissionsInfo.allowsFormFieldEntry=allowsFormFieldEntry;PermissionsInfo.allowsDocumentAssembly=allowsDocumentAssembly;PermissionsInfo.allowsHighQualityPrinting=allowsHighQualityPrinting;return 1}function _ComPDFKitJS_Get_PolicyArray(policy){PolicyArray.push(UTF8ToString(policy));return 1}function _ComPDFKitJS_Get_PrintUsage(subtype,print_state){LayerPrintData.subtype=UTF8ToString(subtype);LayerPrintData.print_state=print_state;return 1}function _ComPDFKitJS_Get_RGBAColor(r,g,b,a){RGBAColor.R=r;RGBAColor.G=g;RGBAColor.B=b;RGBAColor.A=a;return 1}function _ComPDFKitJS_Get_RGBColor(r,g,b){RGBColor.R=r;RGBColor.G=g;RGBColor.B=b;return 1}function _ComPDFKitJS_Get_Rect(left,top,right,bottom){Rect.Left=left;Rect.Top=top;Rect.Right=right;Rect.Bottom=bottom;return 1}function _ComPDFKitJS_Get_RectArray(left,top,right,bottom){RectArray.push({Left:left,Top:top,Right:right,Bottom:bottom});return 1}function _ComPDFKitJS_Get_ResultsCountParams(deleteCount,insertCount,replaceCount){ResultsCountParams.DeleteCount=deleteCount;ResultsCountParams.InsertCount=insertCount;ResultsCountParams.ReplaceCount=replaceCount;return 1}function _ComPDFKitJS_Get_SelectPointColor(index,colorIntensity){SelectPointColorArray.push({Index:index,ColorIntensity:colorIntensity});return 1}function _ComPDFKitJS_Get_SepColors(colorName,r,g,b){SepColorsArray.push({ColorName:UTF8ToString(colorName),R:r,G:g,B:b});return 1}function _ComPDFKitJS_Get_SignerArray(signer){SignerArray.push(signer);return 1}function _ComPDFKitJS_Get_SnappingMode(mode){SnappingMode=mode;return 1}function _ComPDFKitJS_Get_TextFindItem(left,top,right,bottom,content){TextFindItemArray.push({Left:left,Top:top,Right:right,Bottom:bottom,Content:UTF8ToString(content)});return 1}function _ComPDFKitJS_Get_TextRectArray(location,length,left,top,right,bottom){TextRectArray.push({Location:location,Length:length,Left:left,Top:top,Right:right,Bottom:bottom});return 1}function _ComPDFKitJS_Get_TextStamp(content,date,stampType,stampColor){TextStamp.Content=UTF8ToString(content);TextStamp.Date=UTF8ToString(date);TextStamp.StampType=stampType;TextStamp.StampColor=stampColor;return 1}function _ComPDFKitJS_Get_Transparency(transparency){Transparency=transparency;return 1}function _ComPDFKitJS_Get_UTF8String(char){U8StringData=UTF8ToString(char);return U8StringData}function _ComPDFKitJS_Get_WidgetItem(values,strings){WidgetItem.Values=UTF8ToString(values);WidgetItem.Strings=UTF8ToString(strings);return 1}function _ComPDFKitJS_Get_WidgetItemArray(values,strings){WidgetItemsArray.push({Value:UTF8ToString(values),String:UTF8ToString(strings)});return 1}function _ComPDFKitJS_Get_XFDFData(content,size){XFDFData.Content=UTF8ToString(content);XFDFData.Size=size;return 1}function _ComPDFKitJS_Get_ZoomUsage(min_factor,max_factor){LayerZoomData.min_factor=min_factor;LayerZoomData.max_factor=max_factor;return 1}function _ComPDFKitJS_LogString(str){let Content=UTF8ToString(str);console.log(Content);return 1}function _ComPDFKitJS_read_Cert(cert_id,pos,pBuf,size){var input=ComPDFKitJS.opened_cert[0];if(!input)return 0;if(pos+size<pos||pos+size>input.length)return 0;HEAPU8.set(input.subarray(pos,pos+size),pBuf);return 1}function _ComPDFKitJS_read_DefFont(pThis,pBuf,index){var input=ComPDFKitJS.opened_DefFont[index];if(!input)return 0;HEAPU8.set(input.subarray(0,input.length),pBuf);return 1}function _ComPDFKitJS_read_DefFontSize(pThis,index){var input=ComPDFKitJS.opened_DefFont[index];if(!input)return 0;return input.length}function _ComPDFKitJS_read_Font(pThis,index,pBuf){var input=ComPDFKitJS.ComPDFKitDownloadFont(index);if(!input)return 0;HEAPU8.set(input.subarray(0,input.length),pBuf);return 1}function _ComPDFKitJS_read_FontSize(pThis,index){var input=ComPDFKitJS.ComPDFKitDownloadFontA(index);if(!input)return 0;return input.length}function _ComPDFKitJS_read_Image(image_id,pos,pBuf,size){var input=ComPDFKitJS.opened_image[image_id];if(!input){return 0}if(pos+size<pos||pos+size>input.length)return 0;HEAPU8.set(input.subarray(pos,pos+size),pBuf);return 1}function _ComPDFKitJS_read_file(file_id,pos,pBuf,size){var input=ComPDFKitJS.opened_files[file_id];if(!input)return 0;if(pos+size<pos||pos+size>input.length)return 0;HEAPU8.set(input.subarray(pos,pos+size),pBuf);return 1}function _ComPDFKitJS_write_Cert(cert_id,pData,size){let ptr=HEAPU8.subarray(pData,pData+size);let data=new Uint8Array(size);data.set(ptr);CertArray.push(data);return 1}function _ComPDFKitJS_write_file(file_id,pData,size){let ptr=HEAPU8.subarray(pData,pData+size);let data=new Uint8Array(size);data.set(ptr);DataArray.push(data);return 1}var ___assert_fail=(condition,filename,line,func)=>{abort(`Assertion failed: ${UTF8ToString(condition)}, at: `+[filename?UTF8ToString(filename):"unknown filename",line,func?UTF8ToString(func):"unknown function"])};var exceptionCaught=[];var uncaughtExceptionCount=0;function ___cxa_begin_catch(ptr){var info=new ExceptionInfo(ptr);if(!info.get_caught()){info.set_caught(true);uncaughtExceptionCount--}info.set_rethrown(false);exceptionCaught.push(info);___cxa_increment_exception_refcount(info.excPtr);return info.get_exception_ptr()}var exceptionLast=0;function ___cxa_end_catch(){_setThrew(0);assert(exceptionCaught.length>0);var info=exceptionCaught.pop();___cxa_decrement_exception_refcount(info.excPtr);exceptionLast=0}function ExceptionInfo(excPtr){this.excPtr=excPtr;this.ptr=excPtr-24;this.set_type=function(type){HEAPU32[this.ptr+4>>2]=type};this.get_type=function(){return HEAPU32[this.ptr+4>>2]};this.set_destructor=function(destructor){HEAPU32[this.ptr+8>>2]=destructor};this.get_destructor=function(){return HEAPU32[this.ptr+8>>2]};this.set_caught=function(caught){caught=caught?1:0;HEAP8[this.ptr+12>>0]=caught};this.get_caught=function(){return HEAP8[this.ptr+12>>0]!=0};this.set_rethrown=function(rethrown){rethrown=rethrown?1:0;HEAP8[this.ptr+13>>0]=rethrown};this.get_rethrown=function(){return HEAP8[this.ptr+13>>0]!=0};this.init=function(type,destructor){this.set_adjusted_ptr(0);this.set_type(type);this.set_destructor(destructor)};this.set_adjusted_ptr=function(adjustedPtr){HEAPU32[this.ptr+16>>2]=adjustedPtr};this.get_adjusted_ptr=function(){return HEAPU32[this.ptr+16>>2]};this.get_exception_ptr=function(){var isPointer=___cxa_is_pointer_type(this.get_type());if(isPointer){return HEAPU32[this.excPtr>>2]}var adjusted=this.get_adjusted_ptr();if(adjusted!==0)return adjusted;return this.excPtr}}function ___resumeException(ptr){if(!exceptionLast){exceptionLast=new CppException(ptr)}throw exceptionLast}function ___cxa_find_matching_catch(){var thrown=exceptionLast&&exceptionLast.excPtr;if(!thrown){setTempRet0(0);return 0}var info=new ExceptionInfo(thrown);info.set_adjusted_ptr(thrown);var thrownType=info.get_type();if(!thrownType){setTempRet0(0);return thrown}for(var i=0;i<arguments.length;i++){var caughtType=arguments[i];if(caughtType===0||caughtType===thrownType){break}var adjusted_ptr_addr=info.ptr+16;if(___cxa_can_catch(caughtType,thrownType,adjusted_ptr_addr)){setTempRet0(caughtType);return thrown}}setTempRet0(thrownType);return thrown}var ___cxa_find_matching_catch_2=___cxa_find_matching_catch;var ___cxa_find_matching_catch_3=___cxa_find_matching_catch;var ___cxa_find_matching_catch_4=___cxa_find_matching_catch;function ___cxa_rethrow(){var info=exceptionCaught.pop();if(!info){abort("no exception to throw")}var ptr=info.excPtr;if(!info.get_rethrown()){exceptionCaught.push(info);info.set_rethrown(true);info.set_caught(false);uncaughtExceptionCount++}exceptionLast=new CppException(ptr);throw exceptionLast}function ___cxa_throw(ptr,type,destructor){var info=new ExceptionInfo(ptr);info.init(type,destructor);exceptionLast=new CppException(ptr);uncaughtExceptionCount++;throw exceptionLast}function ___cxa_uncaught_exceptions(){return uncaughtExceptionCount}var setErrNo=value=>{HEAP32[___errno_location()>>2]=value;return value};var PATH={isAbs:path=>path.charAt(0)==="/",splitPath:filename=>{var splitPathRe=/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/;return splitPathRe.exec(filename).slice(1)},normalizeArray:(parts,allowAboveRoot)=>{var up=0;for(var i=parts.length-1;i>=0;i--){var last=parts[i];if(last==="."){parts.splice(i,1)}else if(last===".."){parts.splice(i,1);up++}else if(up){parts.splice(i,1);up--}}if(allowAboveRoot){for(;up;up--){parts.unshift("..")}}return parts},normalize:path=>{var isAbsolute=PATH.isAbs(path),trailingSlash=path.substr(-1)==="/";path=PATH.normalizeArray(path.split("/").filter(p=>!!p),!isAbsolute).join("/");if(!path&&!isAbsolute){path="."}if(path&&trailingSlash){path+="/"}return(isAbsolute?"/":"")+path},dirname:path=>{var result=PATH.splitPath(path),root=result[0],dir=result[1];if(!root&&!dir){return"."}if(dir){dir=dir.substr(0,dir.length-1)}return root+dir},basename:path=>{if(path==="/")return"/";path=PATH.normalize(path);path=path.replace(/\/$/,"");var lastSlash=path.lastIndexOf("/");if(lastSlash===-1)return path;return path.substr(lastSlash+1)},join:function(){var paths=Array.prototype.slice.call(arguments);return PATH.normalize(paths.join("/"))},join2:(l,r)=>{return PATH.normalize(l+"/"+r)}};var initRandomFill=()=>{if(typeof crypto=="object"&&typeof crypto["getRandomValues"]=="function"){return view=>crypto.getRandomValues(view)}else if(ENVIRONMENT_IS_NODE){try{var crypto_module=require("crypto");var randomFillSync=crypto_module["randomFillSync"];if(randomFillSync){return view=>crypto_module["randomFillSync"](view)}var randomBytes=crypto_module["randomBytes"];return view=>(view.set(randomBytes(view.byteLength)),view)}catch(e){}}abort("no cryptographic support found for randomDevice. consider polyfilling it if you want to use something insecure like Math.random(), e.g. put this in a --pre-js: var crypto = { getRandomValues: (array) => { for (var i = 0; i < array.length; i++) array[i] = (Math.random()*256)|0 } };")};var randomFill=view=>{return(randomFill=initRandomFill())(view)};var PATH_FS={resolve:function(){var resolvedPath="",resolvedAbsolute=false;for(var i=arguments.length-1;i>=-1&&!resolvedAbsolute;i--){var path=i>=0?arguments[i]:FS.cwd();if(typeof path!="string"){throw new TypeError("Arguments to path.resolve must be strings")}else if(!path){return""}resolvedPath=path+"/"+resolvedPath;resolvedAbsolute=PATH.isAbs(path)}resolvedPath=PATH.normalizeArray(resolvedPath.split("/").filter(p=>!!p),!resolvedAbsolute).join("/");return(resolvedAbsolute?"/":"")+resolvedPath||"."},relative:(from,to)=>{from=PATH_FS.resolve(from).substr(1);to=PATH_FS.resolve(to).substr(1);function trim(arr){var start=0;for(;start<arr.length;start++){if(arr[start]!=="")break}var end=arr.length-1;for(;end>=0;end--){if(arr[end]!=="")break}if(start>end)return[];return arr.slice(start,end-start+1)}var fromParts=trim(from.split("/"));var toParts=trim(to.split("/"));var length=Math.min(fromParts.length,toParts.length);var samePartsLength=length;for(var i=0;i<length;i++){if(fromParts[i]!==toParts[i]){samePartsLength=i;break}}var outputParts=[];for(var i=samePartsLength;i<fromParts.length;i++){outputParts.push("..")}outputParts=outputParts.concat(toParts.slice(samePartsLength));return outputParts.join("/")}};function intArrayFromString(stringy,dontAddNull,length){var len=length>0?length:lengthBytesUTF8(stringy)+1;var u8array=new Array(len);var numBytesWritten=stringToUTF8Array(stringy,u8array,0,u8array.length);if(dontAddNull)u8array.length=numBytesWritten;return u8array}var TTY={ttys:[],init:function(){},shutdown:function(){},register:function(dev,ops){TTY.ttys[dev]={input:[],output:[],ops:ops};FS.registerDevice(dev,TTY.stream_ops)},stream_ops:{open:function(stream){var tty=TTY.ttys[stream.node.rdev];if(!tty){throw new FS.ErrnoError(43)}stream.tty=tty;stream.seekable=false},close:function(stream){stream.tty.ops.fsync(stream.tty)},fsync:function(stream){stream.tty.ops.fsync(stream.tty)},read:function(stream,buffer,offset,length,pos){if(!stream.tty||!stream.tty.ops.get_char){throw new FS.ErrnoError(60)}var bytesRead=0;for(var i=0;i<length;i++){var result;try{result=stream.tty.ops.get_char(stream.tty)}catch(e){throw new FS.ErrnoError(29)}if(result===undefined&&bytesRead===0){throw new FS.ErrnoError(6)}if(result===null||result===undefined)break;bytesRead++;buffer[offset+i]=result}if(bytesRead){stream.node.timestamp=Date.now()}return bytesRead},write:function(stream,buffer,offset,length,pos){if(!stream.tty||!stream.tty.ops.put_char){throw new FS.ErrnoError(60)}try{for(var i=0;i<length;i++){stream.tty.ops.put_char(stream.tty,buffer[offset+i])}}catch(e){throw new FS.ErrnoError(29)}if(length){stream.node.timestamp=Date.now()}return i}},default_tty_ops:{get_char:function(tty){if(!tty.input.length){var result=null;if(ENVIRONMENT_IS_NODE){var BUFSIZE=256;var buf=Buffer.alloc(BUFSIZE);var bytesRead=0;try{bytesRead=fs.readSync(process.stdin.fd,buf,0,BUFSIZE,-1)}catch(e){if(e.toString().includes("EOF"))bytesRead=0;else throw e}if(bytesRead>0){result=buf.slice(0,bytesRead).toString("utf-8")}else{result=null}}else if(typeof window!="undefined"&&typeof window.prompt=="function"){result=window.prompt("Input: ");if(result!==null){result+="\n"}}else if(typeof readline=="function"){result=readline();if(result!==null){result+="\n"}}if(!result){return null}tty.input=intArrayFromString(result,true)}return tty.input.shift()},put_char:function(tty,val){if(val===null||val===10){out(UTF8ArrayToString(tty.output,0));tty.output=[]}else{if(val!=0)tty.output.push(val)}},fsync:function(tty){if(tty.output&&tty.output.length>0){out(UTF8ArrayToString(tty.output,0));tty.output=[]}},ioctl_tcgets:function(tty){return{c_iflag:25856,c_oflag:5,c_cflag:191,c_lflag:35387,c_cc:[3,28,127,21,4,0,1,0,17,19,26,0,18,15,23,22,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]}},ioctl_tcsets:function(tty,optional_actions,data){return 0},ioctl_tiocgwinsz:function(tty){return[24,80]}},default_tty1_ops:{put_char:function(tty,val){if(val===null||val===10){err(UTF8ArrayToString(tty.output,0));tty.output=[]}else{if(val!=0)tty.output.push(val)}},fsync:function(tty){if(tty.output&&tty.output.length>0){err(UTF8ArrayToString(tty.output,0));tty.output=[]}}}};var zeroMemory=(address,size)=>{HEAPU8.fill(0,address,address+size);return address};var alignMemory=(size,alignment)=>{assert(alignment,"alignment argument is required");return Math.ceil(size/alignment)*alignment};var mmapAlloc=size=>{size=alignMemory(size,65536);var ptr=_emscripten_builtin_memalign(65536,size);if(!ptr)return 0;return zeroMemory(ptr,size)};var MEMFS={ops_table:null,mount:function(mount){return MEMFS.createNode(null,"/",16384|511,0)},createNode:function(parent,name,mode,dev){if(FS.isBlkdev(mode)||FS.isFIFO(mode)){throw new FS.ErrnoError(63)}if(!MEMFS.ops_table){MEMFS.ops_table={dir:{node:{getattr:MEMFS.node_ops.getattr,setattr:MEMFS.node_ops.setattr,lookup:MEMFS.node_ops.lookup,mknod:MEMFS.node_ops.mknod,rename:MEMFS.node_ops.rename,unlink:MEMFS.node_ops.unlink,rmdir:MEMFS.node_ops.rmdir,readdir:MEMFS.node_ops.readdir,symlink:MEMFS.node_ops.symlink},stream:{llseek:MEMFS.stream_ops.llseek}},file:{node:{getattr:MEMFS.node_ops.getattr,setattr:MEMFS.node_ops.setattr},stream:{llseek:MEMFS.stream_ops.llseek,read:MEMFS.stream_ops.read,write:MEMFS.stream_ops.write,allocate:MEMFS.stream_ops.allocate,mmap:MEMFS.stream_ops.mmap,msync:MEMFS.stream_ops.msync}},link:{node:{getattr:MEMFS.node_ops.getattr,setattr:MEMFS.node_ops.setattr,readlink:MEMFS.node_ops.readlink},stream:{}},chrdev:{node:{getattr:MEMFS.node_ops.getattr,setattr:MEMFS.node_ops.setattr},stream:FS.chrdev_stream_ops}}}var node=FS.createNode(parent,name,mode,dev);if(FS.isDir(node.mode)){node.node_ops=MEMFS.ops_table.dir.node;node.stream_ops=MEMFS.ops_table.dir.stream;node.contents={}}else if(FS.isFile(node.mode)){node.node_ops=MEMFS.ops_table.file.node;node.stream_ops=MEMFS.ops_table.file.stream;node.usedBytes=0;node.contents=null}else if(FS.isLink(node.mode)){node.node_ops=MEMFS.ops_table.link.node;node.stream_ops=MEMFS.ops_table.link.stream}else if(FS.isChrdev(node.mode)){node.node_ops=MEMFS.ops_table.chrdev.node;node.stream_ops=MEMFS.ops_table.chrdev.stream}node.timestamp=Date.now();if(parent){parent.contents[name]=node;parent.timestamp=node.timestamp}return node},getFileDataAsTypedArray:function(node){if(!node.contents)return new Uint8Array(0);if(node.contents.subarray)return node.contents.subarray(0,node.usedBytes);return new Uint8Array(node.contents)},expandFileStorage:function(node,newCapacity){var prevCapacity=node.contents?node.contents.length:0;if(prevCapacity>=newCapacity)return;var CAPACITY_DOUBLING_MAX=1024*1024;newCapacity=Math.max(newCapacity,prevCapacity*(prevCapacity<CAPACITY_DOUBLING_MAX?2:1.125)>>>0);if(prevCapacity!=0)newCapacity=Math.max(newCapacity,256);var oldContents=node.contents;node.contents=new Uint8Array(newCapacity);if(node.usedBytes>0)node.contents.set(oldContents.subarray(0,node.usedBytes),0)},resizeFileStorage:function(node,newSize){if(node.usedBytes==newSize)return;if(newSize==0){node.contents=null;node.usedBytes=0}else{var oldContents=node.contents;node.contents=new Uint8Array(newSize);if(oldContents){node.contents.set(oldContents.subarray(0,Math.min(newSize,node.usedBytes)))}node.usedBytes=newSize}},node_ops:{getattr:function(node){var attr={};attr.dev=FS.isChrdev(node.mode)?node.id:1;attr.ino=node.id;attr.mode=node.mode;attr.nlink=1;attr.uid=0;attr.gid=0;attr.rdev=node.rdev;if(FS.isDir(node.mode)){attr.size=4096}else if(FS.isFile(node.mode)){attr.size=node.usedBytes}else if(FS.isLink(node.mode)){attr.size=node.link.length}else{attr.size=0}attr.atime=new Date(node.timestamp);attr.mtime=new Date(node.timestamp);attr.ctime=new Date(node.timestamp);attr.blksize=4096;attr.blocks=Math.ceil(attr.size/attr.blksize);return attr},setattr:function(node,attr){if(attr.mode!==undefined){node.mode=attr.mode}if(attr.timestamp!==undefined){node.timestamp=attr.timestamp}if(attr.size!==undefined){MEMFS.resizeFileStorage(node,attr.size)}},lookup:function(parent,name){throw FS.genericErrors[44]},mknod:function(parent,name,mode,dev){return MEMFS.createNode(parent,name,mode,dev)},rename:function(old_node,new_dir,new_name){if(FS.isDir(old_node.mode)){var new_node;try{new_node=FS.lookupNode(new_dir,new_name)}catch(e){}if(new_node){for(var i in new_node.contents){throw new FS.ErrnoError(55)}}}delete old_node.parent.contents[old_node.name];old_node.parent.timestamp=Date.now();old_node.name=new_name;new_dir.contents[new_name]=old_node;new_dir.timestamp=old_node.parent.timestamp;old_node.parent=new_dir},unlink:function(parent,name){delete parent.contents[name];parent.timestamp=Date.now()},rmdir:function(parent,name){var node=FS.lookupNode(parent,name);for(var i in node.contents){throw new FS.ErrnoError(55)}delete parent.contents[name];parent.timestamp=Date.now()},readdir:function(node){var entries=[".",".."];for(var key in node.contents){if(!node.contents.hasOwnProperty(key)){continue}entries.push(key)}return entries},symlink:function(parent,newname,oldpath){var node=MEMFS.createNode(parent,newname,511|40960,0);node.link=oldpath;return node},readlink:function(node){if(!FS.isLink(node.mode)){throw new FS.ErrnoError(28)}return node.link}},stream_ops:{read:function(stream,buffer,offset,length,position){var contents=stream.node.contents;if(position>=stream.node.usedBytes)return 0;var size=Math.min(stream.node.usedBytes-position,length);assert(size>=0);if(size>8&&contents.subarray){buffer.set(contents.subarray(position,position+size),offset)}else{for(var i=0;i<size;i++)buffer[offset+i]=contents[position+i]}return size},write:function(stream,buffer,offset,length,position,canOwn){assert(!(buffer instanceof ArrayBuffer));if(buffer.buffer===HEAP8.buffer){canOwn=false}if(!length)return 0;var node=stream.node;node.timestamp=Date.now();if(buffer.subarray&&(!node.contents||node.contents.subarray)){if(canOwn){assert(position===0,"canOwn must imply no weird position inside the file");node.contents=buffer.subarray(offset,offset+length);node.usedBytes=length;return length}else if(node.usedBytes===0&&position===0){node.contents=buffer.slice(offset,offset+length);node.usedBytes=length;return length}else if(position+length<=node.usedBytes){node.contents.set(buffer.subarray(offset,offset+length),position);return length}}MEMFS.expandFileStorage(node,position+length);if(node.contents.subarray&&buffer.subarray){node.contents.set(buffer.subarray(offset,offset+length),position)}else{for(var i=0;i<length;i++){node.contents[position+i]=buffer[offset+i]}}node.usedBytes=Math.max(node.usedBytes,position+length);return length},llseek:function(stream,offset,whence){var position=offset;if(whence===1){position+=stream.position}else if(whence===2){if(FS.isFile(stream.node.mode)){position+=stream.node.usedBytes}}if(position<0){throw new FS.ErrnoError(28)}return position},allocate:function(stream,offset,length){MEMFS.expandFileStorage(stream.node,offset+length);stream.node.usedBytes=Math.max(stream.node.usedBytes,offset+length)},mmap:function(stream,length,position,prot,flags){if(!FS.isFile(stream.node.mode)){throw new FS.ErrnoError(43)}var ptr;var allocated;var contents=stream.node.contents;if(!(flags&2)&&contents.buffer===HEAP8.buffer){allocated=false;ptr=contents.byteOffset}else{if(position>0||position+length<contents.length){if(contents.subarray){contents=contents.subarray(position,position+length)}else{contents=Array.prototype.slice.call(contents,position,position+length)}}allocated=true;ptr=mmapAlloc(length);if(!ptr){throw new FS.ErrnoError(48)}HEAP8.set(contents,ptr)}return{ptr:ptr,allocated:allocated}},msync:function(stream,buffer,offset,length,mmapFlags){MEMFS.stream_ops.write(stream,buffer,0,length,offset,false);return 0}}};var asyncLoad=(url,onload,onerror,noRunDep)=>{var dep=!noRunDep?getUniqueRunDependency(`al ${url}`):"";readAsync(url,arrayBuffer=>{assert(arrayBuffer,`Loading data file "${url}" failed (no arrayBuffer).`);onload(new Uint8Array(arrayBuffer));if(dep)removeRunDependency(dep)},event=>{if(onerror){onerror()}else{throw`Loading data file "${url}" failed.`}});if(dep)addRunDependency(dep)};var preloadPlugins=Module["preloadPlugins"]||[];function FS_handledByPreloadPlugin(byteArray,fullname,finish,onerror){if(typeof Browser!="undefined")Browser.init();var handled=false;preloadPlugins.forEach(function(plugin){if(handled)return;if(plugin["canHandle"](fullname)){plugin["handle"](byteArray,fullname,finish,onerror);handled=true}});return handled}function FS_createPreloadedFile(parent,name,url,canRead,canWrite,onload,onerror,dontCreateFile,canOwn,preFinish){var fullname=name?PATH_FS.resolve(PATH.join2(parent,name)):parent;var dep=getUniqueRunDependency(`cp ${fullname}`);function processData(byteArray){function finish(byteArray){if(preFinish)preFinish();if(!dontCreateFile){FS.createDataFile(parent,name,byteArray,canRead,canWrite,canOwn)}if(onload)onload();removeRunDependency(dep)}if(FS_handledByPreloadPlugin(byteArray,fullname,finish,()=>{if(onerror)onerror();removeRunDependency(dep)})){return}finish(byteArray)}addRunDependency(dep);if(typeof url=="string"){asyncLoad(url,byteArray=>processData(byteArray),onerror)}else{processData(url)}}function FS_modeStringToFlags(str){var flagModes={"r":0,"r+":2,"w":512|64|1,"w+":512|64|2,"a":1024|64|1,"a+":1024|64|2};var flags=flagModes[str];if(typeof flags=="undefined"){throw new Error(`Unknown file open mode: ${str}`)}return flags}function FS_getMode(canRead,canWrite){var mode=0;if(canRead)mode|=292|73;if(canWrite)mode|=146;return mode}var ERRNO_MESSAGES={0:"Success",1:"Arg list too long",2:"Permission denied",3:"Address already in use",4:"Address not available",5:"Address family not supported by protocol family",6:"No more processes",7:"Socket already connected",8:"Bad file number",9:"Trying to read unreadable message",10:"Mount device busy",11:"Operation canceled",12:"No children",13:"Connection aborted",14:"Connection refused",15:"Connection reset by peer",16:"File locking deadlock error",17:"Destination address required",18:"Math arg out of domain of func",19:"Quota exceeded",20:"File exists",21:"Bad address",22:"File too large",23:"Host is unreachable",24:"Identifier removed",25:"Illegal byte sequence",26:"Connection already in progress",27:"Interrupted system call",28:"Invalid argument",29:"I/O error",30:"Socket is already connected",31:"Is a directory",32:"Too many symbolic links",33:"Too many open files",34:"Too many links",35:"Message too long",36:"Multihop attempted",37:"File or path name too long",38:"Network interface is not configured",39:"Connection reset by network",40:"Network is unreachable",41:"Too many open files in system",42:"No buffer space available",43:"No such device",44:"No such file or directory",45:"Exec format error",46:"No record locks available",47:"The link has been severed",48:"Not enough core",49:"No message of desired type",50:"Protocol not available",51:"No space left on device",52:"Function not implemented",53:"Socket is not connected",54:"Not a directory",55:"Directory not empty",56:"State not recoverable",57:"Socket operation on non-socket",59:"Not a typewriter",60:"No such device or address",61:"Value too large for defined data type",62:"Previous owner died",63:"Not super-user",64:"Broken pipe",65:"Protocol error",66:"Unknown protocol",67:"Protocol wrong type for socket",68:"Math result not representable",69:"Read only file system",70:"Illegal seek",71:"No such process",72:"Stale file handle",73:"Connection timed out",74:"Text file busy",75:"Cross-device link",100:"Device not a stream",101:"Bad font file fmt",102:"Invalid slot",103:"Invalid request code",104:"No anode",105:"Block device required",106:"Channel number out of range",107:"Level 3 halted",108:"Level 3 reset",109:"Link number out of range",110:"Protocol driver not attached",111:"No CSI structure available",112:"Level 2 halted",113:"Invalid exchange",114:"Invalid request descriptor",115:"Exchange full",116:"No data (for no delay io)",117:"Timer expired",118:"Out of streams resources",119:"Machine is not on the network",120:"Package not installed",121:"The object is remote",122:"Advertise error",123:"Srmount error",124:"Communication error on send",125:"Cross mount point (not really error)",126:"Given log. name not unique",127:"f.d. invalid for this operation",128:"Remote address changed",129:"Can   access a needed shared lib",130:"Accessing a corrupted shared lib",131:".lib section in a.out corrupted",132:"Attempting to link in too many libs",133:"Attempting to exec a shared library",135:"Streams pipe error",136:"Too many users",137:"Socket type not supported",138:"Not supported",139:"Protocol family not supported",140:"Can't send after socket shutdown",141:"Too many references",142:"Host is down",148:"No medium (in tape drive)",156:"Level 2 not synchronized"};var ERRNO_CODES={};var FS={root:null,mounts:[],devices:{},streams:[],nextInode:1,nameTable:null,currentPath:"/",initialized:false,ignorePermissions:true,ErrnoError:null,genericErrors:{},filesystems:null,syncFSRequests:0,lookupPath:(path,opts={})=>{path=PATH_FS.resolve(path);if(!path)return{path:"",node:null};var defaults={follow_mount:true,recurse_count:0};opts=Object.assign(defaults,opts);if(opts.recurse_count>8){throw new FS.ErrnoError(32)}var parts=path.split("/").filter(p=>!!p);var current=FS.root;var current_path="/";for(var i=0;i<parts.length;i++){var islast=i===parts.length-1;if(islast&&opts.parent){break}current=FS.lookupNode(current,parts[i]);current_path=PATH.join2(current_path,parts[i]);if(FS.isMountpoint(current)){if(!islast||islast&&opts.follow_mount){current=current.mounted.root}}if(!islast||opts.follow){var count=0;while(FS.isLink(current.mode)){var link=FS.readlink(current_path);current_path=PATH_FS.resolve(PATH.dirname(current_path),link);var lookup=FS.lookupPath(current_path,{recurse_count:opts.recurse_count+1});current=lookup.node;if(count++>40){throw new FS.ErrnoError(32)}}}}return{path:current_path,node:current}},getPath:node=>{var path;while(true){if(FS.isRoot(node)){var mount=node.mount.mountpoint;if(!path)return mount;return mount[mount.length-1]!=="/"?`${mount}/${path}`:mount+path}path=path?`${node.name}/${path}`:node.name;node=node.parent}},hashName:(parentid,name)=>{var hash=0;for(var i=0;i<name.length;i++){hash=(hash<<5)-hash+name.charCodeAt(i)|0}return(parentid+hash>>>0)%FS.nameTable.length},hashAddNode:node=>{var hash=FS.hashName(node.parent.id,node.name);node.name_next=FS.nameTable[hash];FS.nameTable[hash]=node},hashRemoveNode:node=>{var hash=FS.hashName(node.parent.id,node.name);if(FS.nameTable[hash]===node){FS.nameTable[hash]=node.name_next}else{var current=FS.nameTable[hash];while(current){if(current.name_next===node){current.name_next=node.name_next;break}current=current.name_next}}},lookupNode:(parent,name)=>{var errCode=FS.mayLookup(parent);if(errCode){throw new FS.ErrnoError(errCode,parent)}var hash=FS.hashName(parent.id,name);for(var node=FS.nameTable[hash];node;node=node.name_next){var nodeName=node.name;if(node.parent.id===parent.id&&nodeName===name){return node}}return FS.lookup(parent,name)},createNode:(parent,name,mode,rdev)=>{assert(typeof parent=="object");var node=new FS.FSNode(parent,name,mode,rdev);FS.hashAddNode(node);return node},destroyNode:node=>{FS.hashRemoveNode(node)},isRoot:node=>{return node===node.parent},isMountpoint:node=>{return!!node.mounted},isFile:mode=>{return(mode&61440)===32768},isDir:mode=>{return(mode&61440)===16384},isLink:mode=>{return(mode&61440)===40960},isChrdev:mode=>{return(mode&61440)===8192},isBlkdev:mode=>{return(mode&61440)===24576},isFIFO:mode=>{return(mode&61440)===4096},isSocket:mode=>{return(mode&49152)===49152},flagsToPermissionString:flag=>{var perms=["r","w","rw"][flag&3];if(flag&512){perms+="w"}return perms},nodePermissions:(node,perms)=>{if(FS.ignorePermissions){return 0}if(perms.includes("r")&&!(node.mode&292)){return 2}else if(perms.includes("w")&&!(node.mode&146)){return 2}else if(perms.includes("x")&&!(node.mode&73)){return 2}return 0},mayLookup:dir=>{var errCode=FS.nodePermissions(dir,"x");if(errCode)return errCode;if(!dir.node_ops.lookup)return 2;return 0},mayCreate:(dir,name)=>{try{var node=FS.lookupNode(dir,name);return 20}catch(e){}return FS.nodePermissions(dir,"wx")},mayDelete:(dir,name,isdir)=>{var node;try{node=FS.lookupNode(dir,name)}catch(e){return e.errno}var errCode=FS.nodePermissions(dir,"wx");if(errCode){return errCode}if(isdir){if(!FS.isDir(node.mode)){return 54}if(FS.isRoot(node)||FS.getPath(node)===FS.cwd()){return 10}}else{if(FS.isDir(node.mode)){return 31}}return 0},mayOpen:(node,flags)=>{if(!node){return 44}if(FS.isLink(node.mode)){return 32}else if(FS.isDir(node.mode)){if(FS.flagsToPermissionString(flags)!=="r"||flags&512){return 31}}return FS.nodePermissions(node,FS.flagsToPermissionString(flags))},MAX_OPEN_FDS:4096,nextfd:()=>{for(var fd=0;fd<=FS.MAX_OPEN_FDS;fd++){if(!FS.streams[fd]){return fd}}throw new FS.ErrnoError(33)},getStreamChecked:fd=>{var stream=FS.getStream(fd);if(!stream){throw new FS.ErrnoError(8)}return stream},getStream:fd=>FS.streams[fd],createStream:(stream,fd=-1)=>{if(!FS.FSStream){FS.FSStream=function(){this.shared={}};FS.FSStream.prototype={};Object.defineProperties(FS.FSStream.prototype,{object:{get:function(){return this.node},set:function(val){this.node=val}},isRead:{get:function(){return(this.flags&2097155)!==1}},isWrite:{get:function(){return(this.flags&2097155)!==0}},isAppend:{get:function(){return this.flags&1024}},flags:{get:function(){return this.shared.flags},set:function(val){this.shared.flags=val}},position:{get:function(){return this.shared.position},set:function(val){this.shared.position=val}}})}stream=Object.assign(new FS.FSStream,stream);if(fd==-1){fd=FS.nextfd()}stream.fd=fd;FS.streams[fd]=stream;return stream},closeStream:fd=>{FS.streams[fd]=null},chrdev_stream_ops:{open:stream=>{var device=FS.getDevice(stream.node.rdev);stream.stream_ops=device.stream_ops;if(stream.stream_ops.open){stream.stream_ops.open(stream)}},llseek:()=>{throw new FS.ErrnoError(70)}},major:dev=>dev>>8,minor:dev=>dev&255,makedev:(ma,mi)=>ma<<8|mi,registerDevice:(dev,ops)=>{FS.devices[dev]={stream_ops:ops}},getDevice:dev=>FS.devices[dev],getMounts:mount=>{var mounts=[];var check=[mount];while(check.length){var m=check.pop();mounts.push(m);check.push.apply(check,m.mounts)}return mounts},syncfs:(populate,callback)=>{if(typeof populate=="function"){callback=populate;populate=false}FS.syncFSRequests++;if(FS.syncFSRequests>1){err(`warning: ${FS.syncFSRequests} FS.syncfs operations in flight at once, probably just doing extra work`)}var mounts=FS.getMounts(FS.root.mount);var completed=0;function doCallback(errCode){assert(FS.syncFSRequests>0);FS.syncFSRequests--;return callback(errCode)}function done(errCode){if(errCode){if(!done.errored){done.errored=true;return doCallback(errCode)}return}if(++completed>=mounts.length){doCallback(null)}}mounts.forEach(mount=>{if(!mount.type.syncfs){return done(null)}mount.type.syncfs(mount,populate,done)})},mount:(type,opts,mountpoint)=>{if(typeof type=="string"){throw type}var root=mountpoint==="/";var pseudo=!mountpoint;var node;if(root&&FS.root){throw new FS.ErrnoError(10)}else if(!root&&!pseudo){var lookup=FS.lookupPath(mountpoint,{follow_mount:false});mountpoint=lookup.path;node=lookup.node;if(FS.isMountpoint(node)){throw new FS.ErrnoError(10)}if(!FS.isDir(node.mode)){throw new FS.ErrnoError(54)}}var mount={type:type,opts:opts,mountpoint:mountpoint,mounts:[]};var mountRoot=type.mount(mount);mountRoot.mount=mount;mount.root=mountRoot;if(root){FS.root=mountRoot}else if(node){node.mounted=mount;if(node.mount){node.mount.mounts.push(mount)}}return mountRoot},unmount:mountpoint=>{var lookup=FS.lookupPath(mountpoint,{follow_mount:false});if(!FS.isMountpoint(lookup.node)){throw new FS.ErrnoError(28)}var node=lookup.node;var mount=node.mounted;var mounts=FS.getMounts(mount);Object.keys(FS.nameTable).forEach(hash=>{var current=FS.nameTable[hash];while(current){var next=current.name_next;if(mounts.includes(current.mount)){FS.destroyNode(current)}current=next}});node.mounted=null;var idx=node.mount.mounts.indexOf(mount);assert(idx!==-1);node.mount.mounts.splice(idx,1)},lookup:(parent,name)=>{return parent.node_ops.lookup(parent,name)},mknod:(path,mode,dev)=>{var lookup=FS.lookupPath(path,{parent:true});var parent=lookup.node;var name=PATH.basename(path);if(!name||name==="."||name===".."){throw new FS.ErrnoError(28)}var errCode=FS.mayCreate(parent,name);if(errCode){throw new FS.ErrnoError(errCode)}if(!parent.node_ops.mknod){throw new FS.ErrnoError(63)}return parent.node_ops.mknod(parent,name,mode,dev)},create:(path,mode)=>{mode=mode!==undefined?mode:438;mode&=4095;mode|=32768;return FS.mknod(path,mode,0)},mkdir:(path,mode)=>{mode=mode!==undefined?mode:511;mode&=511|512;mode|=16384;return FS.mknod(path,mode,0)},mkdirTree:(path,mode)=>{var dirs=path.split("/");var d="";for(var i=0;i<dirs.length;++i){if(!dirs[i])continue;d+="/"+dirs[i];try{FS.mkdir(d,mode)}catch(e){if(e.errno!=20)throw e}}},mkdev:(path,mode,dev)=>{if(typeof dev=="undefined"){dev=mode;mode=438}mode|=8192;return FS.mknod(path,mode,dev)},symlink:(oldpath,newpath)=>{if(!PATH_FS.resolve(oldpath)){throw new FS.ErrnoError(44)}var lookup=FS.lookupPath(newpath,{parent:true});var parent=lookup.node;if(!parent){throw new FS.ErrnoError(44)}var newname=PATH.basename(newpath);var errCode=FS.mayCreate(parent,newname);if(errCode){throw new FS.ErrnoError(errCode)}if(!parent.node_ops.symlink){throw new FS.ErrnoError(63)}return parent.node_ops.symlink(parent,newname,oldpath)},rename:(old_path,new_path)=>{var old_dirname=PATH.dirname(old_path);var new_dirname=PATH.dirname(new_path);var old_name=PATH.basename(old_path);var new_name=PATH.basename(new_path);var lookup,old_dir,new_dir;lookup=FS.lookupPath(old_path,{parent:true});old_dir=lookup.node;lookup=FS.lookupPath(new_path,{parent:true});new_dir=lookup.node;if(!old_dir||!new_dir)throw new FS.ErrnoError(44);if(old_dir.mount!==new_dir.mount){throw new FS.ErrnoError(75)}var old_node=FS.lookupNode(old_dir,old_name);var relative=PATH_FS.relative(old_path,new_dirname);if(relative.charAt(0)!=="."){throw new FS.ErrnoError(28)}relative=PATH_FS.relative(new_path,old_dirname);if(relative.charAt(0)!=="."){throw new FS.ErrnoError(55)}var new_node;try{new_node=FS.lookupNode(new_dir,new_name)}catch(e){}if(old_node===new_node){return}var isdir=FS.isDir(old_node.mode);var errCode=FS.mayDelete(old_dir,old_name,isdir);if(errCode){throw new FS.ErrnoError(errCode)}errCode=new_node?FS.mayDelete(new_dir,new_name,isdir):FS.mayCreate(new_dir,new_name);if(errCode){throw new FS.ErrnoError(errCode)}if(!old_dir.node_ops.rename){throw new FS.ErrnoError(63)}if(FS.isMountpoint(old_node)||new_node&&FS.isMountpoint(new_node)){throw new FS.ErrnoError(10)}if(new_dir!==old_dir){errCode=FS.nodePermissions(old_dir,"w");if(errCode){throw new FS.ErrnoError(errCode)}}FS.hashRemoveNode(old_node);try{old_dir.node_ops.rename(old_node,new_dir,new_name)}catch(e){throw e}finally{FS.hashAddNode(old_node)}},rmdir:path=>{var lookup=FS.lookupPath(path,{parent:true});var parent=lookup.node;var name=PATH.basename(path);var node=FS.lookupNode(parent,name);var errCode=FS.mayDelete(parent,name,true);if(errCode){throw new FS.ErrnoError(errCode)}if(!parent.node_ops.rmdir){throw new FS.ErrnoError(63)}if(FS.isMountpoint(node)){throw new FS.ErrnoError(10)}parent.node_ops.rmdir(parent,name);FS.destroyNode(node)},readdir:path=>{var lookup=FS.lookupPath(path,{follow:true});var node=lookup.node;if(!node.node_ops.readdir){throw new FS.ErrnoError(54)}return node.node_ops.readdir(node)},unlink:path=>{var lookup=FS.lookupPath(path,{parent:true});var parent=lookup.node;if(!parent){throw new FS.ErrnoError(44)}var name=PATH.basename(path);var node=FS.lookupNode(parent,name);var errCode=FS.mayDelete(parent,name,false);if(errCode){throw new FS.ErrnoError(errCode)}if(!parent.node_ops.unlink){throw new FS.ErrnoError(63)}if(FS.isMountpoint(node)){throw new FS.ErrnoError(10)}parent.node_ops.unlink(parent,name);FS.destroyNode(node)},readlink:path=>{var lookup=FS.lookupPath(path);var link=lookup.node;if(!link){throw new FS.ErrnoError(44)}if(!link.node_ops.readlink){throw new FS.ErrnoError(28)}return PATH_FS.resolve(FS.getPath(link.parent),link.node_ops.readlink(link))},stat:(path,dontFollow)=>{var lookup=FS.lookupPath(path,{follow:!dontFollow});var node=lookup.node;if(!node){throw new FS.ErrnoError(44)}if(!node.node_ops.getattr){throw new FS.ErrnoError(63)}return node.node_ops.getattr(node)},lstat:path=>{return FS.stat(path,true)},chmod:(path,mode,dontFollow)=>{var node;if(typeof path=="string"){var lookup=FS.lookupPath(path,{follow:!dontFollow});node=lookup.node}else{node=path}if(!node.node_ops.setattr){throw new FS.ErrnoError(63)}node.node_ops.setattr(node,{mode:mode&4095|node.mode&~4095,timestamp:Date.now()})},lchmod:(path,mode)=>{FS.chmod(path,mode,true)},fchmod:(fd,mode)=>{var stream=FS.getStreamChecked(fd);FS.chmod(stream.node,mode)},chown:(path,uid,gid,dontFollow)=>{var node;if(typeof path=="string"){var lookup=FS.lookupPath(path,{follow:!dontFollow});node=lookup.node}else{node=path}if(!node.node_ops.setattr){throw new FS.ErrnoError(63)}node.node_ops.setattr(node,{timestamp:Date.now()})},lchown:(path,uid,gid)=>{FS.chown(path,uid,gid,true)},fchown:(fd,uid,gid)=>{var stream=FS.getStreamChecked(fd);FS.chown(stream.node,uid,gid)},truncate:(path,len)=>{if(len<0){throw new FS.ErrnoError(28)}var node;if(typeof path=="string"){var lookup=FS.lookupPath(path,{follow:true});node=lookup.node}else{node=path}if(!node.node_ops.setattr){throw new FS.ErrnoError(63)}if(FS.isDir(node.mode)){throw new FS.ErrnoError(31)}if(!FS.isFile(node.mode)){throw new FS.ErrnoError(28)}var errCode=FS.nodePermissions(node,"w");if(errCode){throw new FS.ErrnoError(errCode)}node.node_ops.setattr(node,{size:len,timestamp:Date.now()})},ftruncate:(fd,len)=>{var stream=FS.getStreamChecked(fd);if((stream.flags&2097155)===0){throw new FS.ErrnoError(28)}FS.truncate(stream.node,len)},utime:(path,atime,mtime)=>{var lookup=FS.lookupPath(path,{follow:true});var node=lookup.node;node.node_ops.setattr(node,{timestamp:Math.max(atime,mtime)})},open:(path,flags,mode)=>{if(path===""){throw new FS.ErrnoError(44)}flags=typeof flags=="string"?FS_modeStringToFlags(flags):flags;mode=typeof mode=="undefined"?438:mode;if(flags&64){mode=mode&4095|32768}else{mode=0}var node;if(typeof path=="object"){node=path}else{path=PATH.normalize(path);try{var lookup=FS.lookupPath(path,{follow:!(flags&131072)});node=lookup.node}catch(e){}}var created=false;if(flags&64){if(node){if(flags&128){throw new FS.ErrnoError(20)}}else{node=FS.mknod(path,mode,0);created=true}}if(!node){throw new FS.ErrnoError(44)}if(FS.isChrdev(node.mode)){flags&=~512}if(flags&65536&&!FS.isDir(node.mode)){throw new FS.ErrnoError(54)}if(!created){var errCode=FS.mayOpen(node,flags);if(errCode){throw new FS.ErrnoError(errCode)}}if(flags&512&&!created){FS.truncate(node,0)}flags&=~(128|512|131072);var stream=FS.createStream({node:node,path:FS.getPath(node),flags:flags,seekable:true,position:0,stream_ops:node.stream_ops,ungotten:[],error:false});if(stream.stream_ops.open){stream.stream_ops.open(stream)}if(Module["logReadFiles"]&&!(flags&1)){if(!FS.readFiles)FS.readFiles={};if(!(path in FS.readFiles)){FS.readFiles[path]=1}}return stream},close:stream=>{if(FS.isClosed(stream)){throw new FS.ErrnoError(8)}if(stream.getdents)stream.getdents=null;try{if(stream.stream_ops.close){stream.stream_ops.close(stream)}}catch(e){throw e}finally{FS.closeStream(stream.fd)}stream.fd=null},isClosed:stream=>{return stream.fd===null},llseek:(stream,offset,whence)=>{if(FS.isClosed(stream)){throw new FS.ErrnoError(8)}if(!stream.seekable||!stream.stream_ops.llseek){throw new FS.ErrnoError(70)}if(whence!=0&&whence!=1&&whence!=2){throw new FS.ErrnoError(28)}stream.position=stream.stream_ops.llseek(stream,offset,whence);stream.ungotten=[];return stream.position},read:(stream,buffer,offset,length,position)=>{if(length<0||position<0){throw new FS.ErrnoError(28)}if(FS.isClosed(stream)){throw new FS.ErrnoError(8)}if((stream.flags&2097155)===1){throw new FS.ErrnoError(8)}if(FS.isDir(stream.node.mode)){throw new FS.ErrnoError(31)}if(!stream.stream_ops.read){throw new FS.ErrnoError(28)}var seeking=typeof position!="undefined";if(!seeking){position=stream.position}else if(!stream.seekable){throw new FS.ErrnoError(70)}var bytesRead=stream.stream_ops.read(stream,buffer,offset,length,position);if(!seeking)stream.position+=bytesRead;return bytesRead},write:(stream,buffer,offset,length,position,canOwn)=>{if(length<0||position<0){throw new FS.ErrnoError(28)}if(FS.isClosed(stream)){throw new FS.ErrnoError(8)}if((stream.flags&2097155)===0){throw new FS.ErrnoError(8)}if(FS.isDir(stream.node.mode)){throw new FS.ErrnoError(31)}if(!stream.stream_ops.write){throw new FS.ErrnoError(28)}if(stream.seekable&&stream.flags&1024){FS.llseek(stream,0,2)}var seeking=typeof position!="undefined";if(!seeking){position=stream.position}else if(!stream.seekable){throw new FS.ErrnoError(70)}var bytesWritten=stream.stream_ops.write(stream,buffer,offset,length,position,canOwn);if(!seeking)stream.position+=bytesWritten;return bytesWritten},allocate:(stream,offset,length)=>{if(FS.isClosed(stream)){throw new FS.ErrnoError(8)}if(offset<0||length<=0){throw new FS.ErrnoError(28)}if((stream.flags&2097155)===0){throw new FS.ErrnoError(8)}if(!FS.isFile(stream.node.mode)&&!FS.isDir(stream.node.mode)){throw new FS.ErrnoError(43)}if(!stream.stream_ops.allocate){throw new FS.ErrnoError(138)}stream.stream_ops.allocate(stream,offset,length)},mmap:(stream,length,position,prot,flags)=>{if((prot&2)!==0&&(flags&2)===0&&(stream.flags&2097155)!==2){throw new FS.ErrnoError(2)}if((stream.flags&2097155)===1){throw new FS.ErrnoError(2)}if(!stream.stream_ops.mmap){throw new FS.ErrnoError(43)}return stream.stream_ops.mmap(stream,length,position,prot,flags)},msync:(stream,buffer,offset,length,mmapFlags)=>{if(!stream.stream_ops.msync){return 0}return stream.stream_ops.msync(stream,buffer,offset,length,mmapFlags)},munmap:stream=>0,ioctl:(stream,cmd,arg)=>{if(!stream.stream_ops.ioctl){throw new FS.ErrnoError(59)}return stream.stream_ops.ioctl(stream,cmd,arg)},readFile:(path,opts={})=>{opts.flags=opts.flags||0;opts.encoding=opts.encoding||"binary";if(opts.encoding!=="utf8"&&opts.encoding!=="binary"){throw new Error(`Invalid encoding type "${opts.encoding}"`)}var ret;var stream=FS.open(path,opts.flags);var stat=FS.stat(path);var length=stat.size;var buf=new Uint8Array(length);FS.read(stream,buf,0,length,0);if(opts.encoding==="utf8"){ret=UTF8ArrayToString(buf,0)}else if(opts.encoding==="binary"){ret=buf}FS.close(stream);return ret},writeFile:(path,data,opts={})=>{opts.flags=opts.flags||577;var stream=FS.open(path,opts.flags,opts.mode);if(typeof data=="string"){var buf=new Uint8Array(lengthBytesUTF8(data)+1);var actualNumBytes=stringToUTF8Array(data,buf,0,buf.length);FS.write(stream,buf,0,actualNumBytes,undefined,opts.canOwn)}else if(ArrayBuffer.isView(data)){FS.write(stream,data,0,data.byteLength,undefined,opts.canOwn)}else{throw new Error("Unsupported data type")}FS.close(stream)},cwd:()=>FS.currentPath,chdir:path=>{var lookup=FS.lookupPath(path,{follow:true});if(lookup.node===null){throw new FS.ErrnoError(44)}if(!FS.isDir(lookup.node.mode)){throw new FS.ErrnoError(54)}var errCode=FS.nodePermissions(lookup.node,"x");if(errCode){throw new FS.ErrnoError(errCode)}FS.currentPath=lookup.path},createDefaultDirectories:()=>{FS.mkdir("/tmp");FS.mkdir("/home");FS.mkdir("/home/<USER>")},createDefaultDevices:()=>{FS.mkdir("/dev");FS.registerDevice(FS.makedev(1,3),{read:()=>0,write:(stream,buffer,offset,length,pos)=>length});FS.mkdev("/dev/null",FS.makedev(1,3));TTY.register(FS.makedev(5,0),TTY.default_tty_ops);TTY.register(FS.makedev(6,0),TTY.default_tty1_ops);FS.mkdev("/dev/tty",FS.makedev(5,0));FS.mkdev("/dev/tty1",FS.makedev(6,0));var randomBuffer=new Uint8Array(1024),randomLeft=0;var randomByte=()=>{if(randomLeft===0){randomLeft=randomFill(randomBuffer).byteLength}return randomBuffer[--randomLeft]};FS.createDevice("/dev","random",randomByte);FS.createDevice("/dev","urandom",randomByte);FS.mkdir("/dev/shm");FS.mkdir("/dev/shm/tmp")},createSpecialDirectories:()=>{FS.mkdir("/proc");var proc_self=FS.mkdir("/proc/self");FS.mkdir("/proc/self/fd");FS.mount({mount:()=>{var node=FS.createNode(proc_self,"fd",16384|511,73);node.node_ops={lookup:(parent,name)=>{var fd=+name;var stream=FS.getStreamChecked(fd);var ret={parent:null,mount:{mountpoint:"fake"},node_ops:{readlink:()=>stream.path}};ret.parent=ret;return ret}};return node}},{},"/proc/self/fd")},createStandardStreams:()=>{if(Module["stdin"]){FS.createDevice("/dev","stdin",Module["stdin"])}else{FS.symlink("/dev/tty","/dev/stdin")}if(Module["stdout"]){FS.createDevice("/dev","stdout",null,Module["stdout"])}else{FS.symlink("/dev/tty","/dev/stdout")}if(Module["stderr"]){FS.createDevice("/dev","stderr",null,Module["stderr"])}else{FS.symlink("/dev/tty1","/dev/stderr")}var stdin=FS.open("/dev/stdin",0);var stdout=FS.open("/dev/stdout",1);var stderr=FS.open("/dev/stderr",1);assert(stdin.fd===0,`invalid handle for stdin (${stdin.fd})`);assert(stdout.fd===1,`invalid handle for stdout (${stdout.fd})`);assert(stderr.fd===2,`invalid handle for stderr (${stderr.fd})`)},ensureErrnoError:()=>{if(FS.ErrnoError)return;FS.ErrnoError=function ErrnoError(errno,node){this.name="ErrnoError";this.node=node;this.setErrno=function(errno){this.errno=errno;for(var key in ERRNO_CODES){if(ERRNO_CODES[key]===errno){this.code=key;break}}};this.setErrno(errno);this.message=ERRNO_MESSAGES[errno];if(this.stack){Object.defineProperty(this,"stack",{value:(new Error).stack,writable:true});this.stack=demangleAll(this.stack)}};FS.ErrnoError.prototype=new Error;FS.ErrnoError.prototype.constructor=FS.ErrnoError;[44].forEach(code=>{FS.genericErrors[code]=new FS.ErrnoError(code);FS.genericErrors[code].stack="<generic error, no stack>"})},staticInit:()=>{FS.ensureErrnoError();FS.nameTable=new Array(4096);FS.mount(MEMFS,{},"/");FS.createDefaultDirectories();FS.createDefaultDevices();FS.createSpecialDirectories();FS.filesystems={"MEMFS":MEMFS}},init:(input,output,error)=>{assert(!FS.init.initialized,"FS.init was previously called. If you want to initialize later with custom parameters, remove any earlier calls (note that one is automatically added to the generated code)");FS.init.initialized=true;FS.ensureErrnoError();Module["stdin"]=input||Module["stdin"];Module["stdout"]=output||Module["stdout"];Module["stderr"]=error||Module["stderr"];FS.createStandardStreams()},quit:()=>{FS.init.initialized=false;_fflush(0);for(var i=0;i<FS.streams.length;i++){var stream=FS.streams[i];if(!stream){continue}FS.close(stream)}},findObject:(path,dontResolveLastLink)=>{var ret=FS.analyzePath(path,dontResolveLastLink);if(!ret.exists){return null}return ret.object},analyzePath:(path,dontResolveLastLink)=>{try{var lookup=FS.lookupPath(path,{follow:!dontResolveLastLink});path=lookup.path}catch(e){}var ret={isRoot:false,exists:false,error:0,name:null,path:null,object:null,parentExists:false,parentPath:null,parentObject:null};try{var lookup=FS.lookupPath(path,{parent:true});ret.parentExists=true;ret.parentPath=lookup.path;ret.parentObject=lookup.node;ret.name=PATH.basename(path);lookup=FS.lookupPath(path,{follow:!dontResolveLastLink});ret.exists=true;ret.path=lookup.path;ret.object=lookup.node;ret.name=lookup.node.name;ret.isRoot=lookup.path==="/"}catch(e){ret.error=e.errno}return ret},createPath:(parent,path,canRead,canWrite)=>{parent=typeof parent=="string"?parent:FS.getPath(parent);var parts=path.split("/").reverse();while(parts.length){var part=parts.pop();if(!part)continue;var current=PATH.join2(parent,part);try{FS.mkdir(current)}catch(e){}parent=current}return current},createFile:(parent,name,properties,canRead,canWrite)=>{var path=PATH.join2(typeof parent=="string"?parent:FS.getPath(parent),name);var mode=FS_getMode(canRead,canWrite);return FS.create(path,mode)},createDataFile:(parent,name,data,canRead,canWrite,canOwn)=>{var path=name;if(parent){parent=typeof parent=="string"?parent:FS.getPath(parent);path=name?PATH.join2(parent,name):parent}var mode=FS_getMode(canRead,canWrite);var node=FS.create(path,mode);if(data){if(typeof data=="string"){var arr=new Array(data.length);for(var i=0,len=data.length;i<len;++i)arr[i]=data.charCodeAt(i);data=arr}FS.chmod(node,mode|146);var stream=FS.open(node,577);FS.write(stream,data,0,data.length,0,canOwn);FS.close(stream);FS.chmod(node,mode)}return node},createDevice:(parent,name,input,output)=>{var path=PATH.join2(typeof parent=="string"?parent:FS.getPath(parent),name);var mode=FS_getMode(!!input,!!output);if(!FS.createDevice.major)FS.createDevice.major=64;var dev=FS.makedev(FS.createDevice.major++,0);FS.registerDevice(dev,{open:stream=>{stream.seekable=false},close:stream=>{if(output&&output.buffer&&output.buffer.length){output(10)}},read:(stream,buffer,offset,length,pos)=>{var bytesRead=0;for(var i=0;i<length;i++){var result;try{result=input()}catch(e){throw new FS.ErrnoError(29)}if(result===undefined&&bytesRead===0){throw new FS.ErrnoError(6)}if(result===null||result===undefined)break;bytesRead++;buffer[offset+i]=result}if(bytesRead){stream.node.timestamp=Date.now()}return bytesRead},write:(stream,buffer,offset,length,pos)=>{for(var i=0;i<length;i++){try{output(buffer[offset+i])}catch(e){throw new FS.ErrnoError(29)}}if(length){stream.node.timestamp=Date.now()}return i}});return FS.mkdev(path,mode,dev)},forceLoadFile:obj=>{if(obj.isDevice||obj.isFolder||obj.link||obj.contents)return true;if(typeof XMLHttpRequest!="undefined"){throw new Error("Lazy loading should have been performed (contents set) in createLazyFile, but it was not. Lazy loading only works in web workers. Use --embed-file or --preload-file in emcc on the main thread.")}else if(read_){try{obj.contents=intArrayFromString(read_(obj.url),true);obj.usedBytes=obj.contents.length}catch(e){throw new FS.ErrnoError(29)}}else{throw new Error("Cannot load without read() or XMLHttpRequest.")}},createLazyFile:(parent,name,url,canRead,canWrite)=>{function LazyUint8Array(){this.lengthKnown=false;this.chunks=[]}LazyUint8Array.prototype.get=function LazyUint8Array_get(idx){if(idx>this.length-1||idx<0){return undefined}var chunkOffset=idx%this.chunkSize;var chunkNum=idx/this.chunkSize|0;return this.getter(chunkNum)[chunkOffset]};LazyUint8Array.prototype.setDataGetter=function LazyUint8Array_setDataGetter(getter){this.getter=getter};LazyUint8Array.prototype.cacheLength=function LazyUint8Array_cacheLength(){var xhr=new XMLHttpRequest;xhr.open("HEAD",url,false);xhr.send(null);if(!(xhr.status>=200&&xhr.status<300||xhr.status===304))throw new Error("Couldn't load "+url+". Status: "+xhr.status);var datalength=Number(xhr.getResponseHeader("Content-length"));var header;var hasByteServing=(header=xhr.getResponseHeader("Accept-Ranges"))&&header==="bytes";var usesGzip=(header=xhr.getResponseHeader("Content-Encoding"))&&header==="gzip";var chunkSize=1024*1024;if(!hasByteServing)chunkSize=datalength;var doXHR=(from,to)=>{if(from>to)throw new Error("invalid range ("+from+", "+to+") or no bytes requested!");if(to>datalength-1)throw new Error("only "+datalength+" bytes available! programmer error!");var xhr=new XMLHttpRequest;xhr.open("GET",url,false);if(datalength!==chunkSize)xhr.setRequestHeader("Range","bytes="+from+"-"+to);xhr.responseType="arraybuffer";if(xhr.overrideMimeType){xhr.overrideMimeType("text/plain; charset=x-user-defined")}xhr.send(null);if(!(xhr.status>=200&&xhr.status<300||xhr.status===304))throw new Error("Couldn't load "+url+". Status: "+xhr.status);if(xhr.response!==undefined){return new Uint8Array(xhr.response||[])}return intArrayFromString(xhr.responseText||"",true)};var lazyArray=this;lazyArray.setDataGetter(chunkNum=>{var start=chunkNum*chunkSize;var end=(chunkNum+1)*chunkSize-1;end=Math.min(end,datalength-1);if(typeof lazyArray.chunks[chunkNum]=="undefined"){lazyArray.chunks[chunkNum]=doXHR(start,end)}if(typeof lazyArray.chunks[chunkNum]=="undefined")throw new Error("doXHR failed!");return lazyArray.chunks[chunkNum]});if(usesGzip||!datalength){chunkSize=datalength=1;datalength=this.getter(0).length;chunkSize=datalength;out("LazyFiles on gzip forces download of the whole file when length is accessed")}this._length=datalength;this._chunkSize=chunkSize;this.lengthKnown=true};if(typeof XMLHttpRequest!="undefined"){if(!ENVIRONMENT_IS_WORKER)throw"Cannot do synchronous binary XHRs outside webworkers in modern browsers. Use --embed-file or --preload-file in emcc";var lazyArray=new LazyUint8Array;Object.defineProperties(lazyArray,{length:{get:function(){if(!this.lengthKnown){this.cacheLength()}return this._length}},chunkSize:{get:function(){if(!this.lengthKnown){this.cacheLength()}return this._chunkSize}}});var properties={isDevice:false,contents:lazyArray}}else{var properties={isDevice:false,url:url}}var node=FS.createFile(parent,name,properties,canRead,canWrite);if(properties.contents){node.contents=properties.contents}else if(properties.url){node.contents=null;node.url=properties.url}Object.defineProperties(node,{usedBytes:{get:function(){return this.contents.length}}});var stream_ops={};var keys=Object.keys(node.stream_ops);keys.forEach(key=>{var fn=node.stream_ops[key];stream_ops[key]=function forceLoadLazyFile(){FS.forceLoadFile(node);return fn.apply(null,arguments)}});function writeChunks(stream,buffer,offset,length,position){var contents=stream.node.contents;if(position>=contents.length)return 0;var size=Math.min(contents.length-position,length);assert(size>=0);if(contents.slice){for(var i=0;i<size;i++){buffer[offset+i]=contents[position+i]}}else{for(var i=0;i<size;i++){buffer[offset+i]=contents.get(position+i)}}return size}stream_ops.read=(stream,buffer,offset,length,position)=>{FS.forceLoadFile(node);return writeChunks(stream,buffer,offset,length,position)};stream_ops.mmap=(stream,length,position,prot,flags)=>{FS.forceLoadFile(node);var ptr=mmapAlloc(length);if(!ptr){throw new FS.ErrnoError(48)}writeChunks(stream,HEAP8,ptr,length,position);return{ptr:ptr,allocated:true}};node.stream_ops=stream_ops;return node},absolutePath:()=>{abort("FS.absolutePath has been removed; use PATH_FS.resolve instead")},createFolder:()=>{abort("FS.createFolder has been removed; use FS.mkdir instead")},createLink:()=>{abort("FS.createLink has been removed; use FS.symlink instead")},joinPath:()=>{abort("FS.joinPath has been removed; use PATH.join instead")},mmapAlloc:()=>{abort("FS.mmapAlloc has been replaced by the top level function mmapAlloc")},standardizePath:()=>{abort("FS.standardizePath has been removed; use PATH.normalize instead")}};var SYSCALLS={DEFAULT_POLLMASK:5,calculateAt:function(dirfd,path,allowEmpty){if(PATH.isAbs(path)){return path}var dir;if(dirfd===-100){dir=FS.cwd()}else{var dirstream=SYSCALLS.getStreamFromFD(dirfd);dir=dirstream.path}if(path.length==0){if(!allowEmpty){throw new FS.ErrnoError(44)}return dir}return PATH.join2(dir,path)},doStat:function(func,path,buf){try{var stat=func(path)}catch(e){if(e&&e.node&&PATH.normalize(path)!==PATH.normalize(FS.getPath(e.node))){return-54}throw e}HEAP32[buf>>2]=stat.dev;HEAP32[buf+4>>2]=stat.mode;HEAPU32[buf+8>>2]=stat.nlink;HEAP32[buf+12>>2]=stat.uid;HEAP32[buf+16>>2]=stat.gid;HEAP32[buf+20>>2]=stat.rdev;tempI64=[stat.size>>>0,(tempDouble=stat.size,+Math.abs(tempDouble)>=1?tempDouble>0?+Math.floor(tempDouble/4294967296)>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],HEAP32[buf+24>>2]=tempI64[0],HEAP32[buf+28>>2]=tempI64[1];HEAP32[buf+32>>2]=4096;HEAP32[buf+36>>2]=stat.blocks;var atime=stat.atime.getTime();var mtime=stat.mtime.getTime();var ctime=stat.ctime.getTime();tempI64=[Math.floor(atime/1e3)>>>0,(tempDouble=Math.floor(atime/1e3),+Math.abs(tempDouble)>=1?tempDouble>0?+Math.floor(tempDouble/4294967296)>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],HEAP32[buf+40>>2]=tempI64[0],HEAP32[buf+44>>2]=tempI64[1];HEAPU32[buf+48>>2]=atime%1e3*1e3;tempI64=[Math.floor(mtime/1e3)>>>0,(tempDouble=Math.floor(mtime/1e3),+Math.abs(tempDouble)>=1?tempDouble>0?+Math.floor(tempDouble/4294967296)>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],HEAP32[buf+56>>2]=tempI64[0],HEAP32[buf+60>>2]=tempI64[1];HEAPU32[buf+64>>2]=mtime%1e3*1e3;tempI64=[Math.floor(ctime/1e3)>>>0,(tempDouble=Math.floor(ctime/1e3),+Math.abs(tempDouble)>=1?tempDouble>0?+Math.floor(tempDouble/4294967296)>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],HEAP32[buf+72>>2]=tempI64[0],HEAP32[buf+76>>2]=tempI64[1];HEAPU32[buf+80>>2]=ctime%1e3*1e3;tempI64=[stat.ino>>>0,(tempDouble=stat.ino,+Math.abs(tempDouble)>=1?tempDouble>0?+Math.floor(tempDouble/4294967296)>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],HEAP32[buf+88>>2]=tempI64[0],HEAP32[buf+92>>2]=tempI64[1];return 0},doMsync:function(addr,stream,len,flags,offset){if(!FS.isFile(stream.node.mode)){throw new FS.ErrnoError(43)}if(flags&2){return 0}var buffer=HEAPU8.slice(addr,addr+len);FS.msync(stream,buffer,offset,len,flags)},varargs:undefined,get:function(){assert(SYSCALLS.varargs!=undefined);SYSCALLS.varargs+=4;var ret=HEAP32[SYSCALLS.varargs-4>>2];return ret},getStr:function(ptr){var ret=UTF8ToString(ptr);return ret},getStreamFromFD:function(fd){var stream=FS.getStreamChecked(fd);return stream}};function ___syscall_fcntl64(fd,cmd,varargs){SYSCALLS.varargs=varargs;try{var stream=SYSCALLS.getStreamFromFD(fd);switch(cmd){case 0:{var arg=SYSCALLS.get();if(arg<0){return-28}var newStream;newStream=FS.createStream(stream,arg);return newStream.fd}case 1:case 2:return 0;case 3:return stream.flags;case 4:{var arg=SYSCALLS.get();stream.flags|=arg;return 0}case 5:{var arg=SYSCALLS.get();var offset=0;HEAP16[arg+offset>>1]=2;return 0}case 6:case 7:return 0;case 16:case 8:return-28;case 9:setErrNo(28);return-1;default:{return-28}}}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return-e.errno}}function ___syscall_fstat64(fd,buf){try{var stream=SYSCALLS.getStreamFromFD(fd);return SYSCALLS.doStat(FS.stat,stream.path,buf)}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return-e.errno}}function convertI32PairToI53Checked(lo,hi){assert(lo==lo>>>0||lo==(lo|0));assert(hi===(hi|0));return hi+2097152>>>0<4194305-!!lo?(lo>>>0)+hi*4294967296:NaN}function ___syscall_ftruncate64(fd,length_low,length_high){try{var length=convertI32PairToI53Checked(length_low,length_high);if(isNaN(length))return-61;FS.ftruncate(fd,length);return 0}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return-e.errno}}function ___syscall_getdents64(fd,dirp,count){try{var stream=SYSCALLS.getStreamFromFD(fd);if(!stream.getdents){stream.getdents=FS.readdir(stream.path)}var struct_size=280;var pos=0;var off=FS.llseek(stream,0,1);var idx=Math.floor(off/struct_size);while(idx<stream.getdents.length&&pos+struct_size<=count){var id;var type;var name=stream.getdents[idx];if(name==="."){id=stream.node.id;type=4}else if(name===".."){var lookup=FS.lookupPath(stream.path,{parent:true});id=lookup.node.id;type=4}else{var child=FS.lookupNode(stream.node,name);id=child.id;type=FS.isChrdev(child.mode)?2:FS.isDir(child.mode)?4:FS.isLink(child.mode)?10:8}assert(id);tempI64=[id>>>0,(tempDouble=id,+Math.abs(tempDouble)>=1?tempDouble>0?+Math.floor(tempDouble/4294967296)>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],HEAP32[dirp+pos>>2]=tempI64[0],HEAP32[dirp+pos+4>>2]=tempI64[1];tempI64=[(idx+1)*struct_size>>>0,(tempDouble=(idx+1)*struct_size,+Math.abs(tempDouble)>=1?tempDouble>0?+Math.floor(tempDouble/4294967296)>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],HEAP32[dirp+pos+8>>2]=tempI64[0],HEAP32[dirp+pos+12>>2]=tempI64[1];HEAP16[dirp+pos+16>>1]=280;HEAP8[dirp+pos+18>>0]=type;stringToUTF8(name,dirp+pos+19,256);pos+=struct_size;idx+=1}FS.llseek(stream,idx*struct_size,0);return pos}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return-e.errno}}function ___syscall_ioctl(fd,op,varargs){SYSCALLS.varargs=varargs;try{var stream=SYSCALLS.getStreamFromFD(fd);switch(op){case 21509:{if(!stream.tty)return-59;return 0}case 21505:{if(!stream.tty)return-59;if(stream.tty.ops.ioctl_tcgets){var termios=stream.tty.ops.ioctl_tcgets(stream);var argp=SYSCALLS.get();HEAP32[argp>>2]=termios.c_iflag||0;HEAP32[argp+4>>2]=termios.c_oflag||0;HEAP32[argp+8>>2]=termios.c_cflag||0;HEAP32[argp+12>>2]=termios.c_lflag||0;for(var i=0;i<32;i++){HEAP8[argp+i+17>>0]=termios.c_cc[i]||0}return 0}return 0}case 21510:case 21511:case 21512:{if(!stream.tty)return-59;return 0}case 21506:case 21507:case 21508:{if(!stream.tty)return-59;if(stream.tty.ops.ioctl_tcsets){var argp=SYSCALLS.get();var c_iflag=HEAP32[argp>>2];var c_oflag=HEAP32[argp+4>>2];var c_cflag=HEAP32[argp+8>>2];var c_lflag=HEAP32[argp+12>>2];var c_cc=[];for(var i=0;i<32;i++){c_cc.push(HEAP8[argp+i+17>>0])}return stream.tty.ops.ioctl_tcsets(stream.tty,op,{c_iflag:c_iflag,c_oflag:c_oflag,c_cflag:c_cflag,c_lflag:c_lflag,c_cc:c_cc})}return 0}case 21519:{if(!stream.tty)return-59;var argp=SYSCALLS.get();HEAP32[argp>>2]=0;return 0}case 21520:{if(!stream.tty)return-59;return-28}case 21531:{var argp=SYSCALLS.get();return FS.ioctl(stream,op,argp)}case 21523:{if(!stream.tty)return-59;if(stream.tty.ops.ioctl_tiocgwinsz){var winsize=stream.tty.ops.ioctl_tiocgwinsz(stream.tty);var argp=SYSCALLS.get();HEAP16[argp>>1]=winsize[0];HEAP16[argp+2>>1]=winsize[1]}return 0}case 21524:{if(!stream.tty)return-59;return 0}case 21515:{if(!stream.tty)return-59;return 0}default:return-28}}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return-e.errno}}function ___syscall_lstat64(path,buf){try{path=SYSCALLS.getStr(path);return SYSCALLS.doStat(FS.lstat,path,buf)}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return-e.errno}}function ___syscall_newfstatat(dirfd,path,buf,flags){try{path=SYSCALLS.getStr(path);var nofollow=flags&256;var allowEmpty=flags&4096;flags=flags&~6400;assert(!flags,`unknown flags in __syscall_newfstatat: ${flags}`);path=SYSCALLS.calculateAt(dirfd,path,allowEmpty);return SYSCALLS.doStat(nofollow?FS.lstat:FS.stat,path,buf)}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return-e.errno}}function ___syscall_openat(dirfd,path,flags,varargs){SYSCALLS.varargs=varargs;try{path=SYSCALLS.getStr(path);path=SYSCALLS.calculateAt(dirfd,path);var mode=varargs?SYSCALLS.get():0;return FS.open(path,flags,mode).fd}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return-e.errno}}function ___syscall_rmdir(path){try{path=SYSCALLS.getStr(path);FS.rmdir(path);return 0}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return-e.errno}}function ___syscall_stat64(path,buf){try{path=SYSCALLS.getStr(path);return SYSCALLS.doStat(FS.stat,path,buf)}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return-e.errno}}function ___syscall_unlinkat(dirfd,path,flags){try{path=SYSCALLS.getStr(path);path=SYSCALLS.calculateAt(dirfd,path);if(flags===0){FS.unlink(path)}else if(flags===512){FS.rmdir(path)}else{abort("Invalid flags passed to unlinkat")}return 0}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return-e.errno}}var nowIsMonotonic=true;var __emscripten_get_now_is_monotonic=()=>nowIsMonotonic;var __emscripten_throw_longjmp=()=>{throw new EmscriptenSjLj};function readI53FromI64(ptr){return HEAPU32[ptr>>2]+HEAP32[ptr+4>>2]*4294967296}var __gmtime_js=(time,tmPtr)=>{var date=new Date(readI53FromI64(time)*1e3);HEAP32[tmPtr>>2]=date.getUTCSeconds();HEAP32[tmPtr+4>>2]=date.getUTCMinutes();HEAP32[tmPtr+8>>2]=date.getUTCHours();HEAP32[tmPtr+12>>2]=date.getUTCDate();HEAP32[tmPtr+16>>2]=date.getUTCMonth();HEAP32[tmPtr+20>>2]=date.getUTCFullYear()-1900;HEAP32[tmPtr+24>>2]=date.getUTCDay();var start=Date.UTC(date.getUTCFullYear(),0,1,0,0,0,0);var yday=(date.getTime()-start)/(1e3*60*60*24)|0;HEAP32[tmPtr+28>>2]=yday};var isLeapYear=year=>{return year%4===0&&(year%100!==0||year%400===0)};var MONTH_DAYS_LEAP_CUMULATIVE=[0,31,60,91,121,152,182,213,244,274,305,335];var MONTH_DAYS_REGULAR_CUMULATIVE=[0,31,59,90,120,151,181,212,243,273,304,334];var ydayFromDate=date=>{var leap=isLeapYear(date.getFullYear());var monthDaysCumulative=leap?MONTH_DAYS_LEAP_CUMULATIVE:MONTH_DAYS_REGULAR_CUMULATIVE;var yday=monthDaysCumulative[date.getMonth()]+date.getDate()-1;return yday};var __localtime_js=(time,tmPtr)=>{var date=new Date(readI53FromI64(time)*1e3);HEAP32[tmPtr>>2]=date.getSeconds();HEAP32[tmPtr+4>>2]=date.getMinutes();HEAP32[tmPtr+8>>2]=date.getHours();HEAP32[tmPtr+12>>2]=date.getDate();HEAP32[tmPtr+16>>2]=date.getMonth();HEAP32[tmPtr+20>>2]=date.getFullYear()-1900;HEAP32[tmPtr+24>>2]=date.getDay();var yday=ydayFromDate(date)|0;HEAP32[tmPtr+28>>2]=yday;HEAP32[tmPtr+36>>2]=-(date.getTimezoneOffset()*60);var start=new Date(date.getFullYear(),0,1);var summerOffset=new Date(date.getFullYear(),6,1).getTimezoneOffset();var winterOffset=start.getTimezoneOffset();var dst=(summerOffset!=winterOffset&&date.getTimezoneOffset()==Math.min(winterOffset,summerOffset))|0;HEAP32[tmPtr+32>>2]=dst};var __mktime_js=tmPtr=>{var date=new Date(HEAP32[tmPtr+20>>2]+1900,HEAP32[tmPtr+16>>2],HEAP32[tmPtr+12>>2],HEAP32[tmPtr+8>>2],HEAP32[tmPtr+4>>2],HEAP32[tmPtr>>2],0);var dst=HEAP32[tmPtr+32>>2];var guessedOffset=date.getTimezoneOffset();var start=new Date(date.getFullYear(),0,1);var summerOffset=new Date(date.getFullYear(),6,1).getTimezoneOffset();var winterOffset=start.getTimezoneOffset();var dstOffset=Math.min(winterOffset,summerOffset);if(dst<0){HEAP32[tmPtr+32>>2]=Number(summerOffset!=winterOffset&&dstOffset==guessedOffset)}else if(dst>0!=(dstOffset==guessedOffset)){var nonDstOffset=Math.max(winterOffset,summerOffset);var trueOffset=dst>0?dstOffset:nonDstOffset;date.setTime(date.getTime()+(trueOffset-guessedOffset)*6e4)}HEAP32[tmPtr+24>>2]=date.getDay();var yday=ydayFromDate(date)|0;HEAP32[tmPtr+28>>2]=yday;HEAP32[tmPtr>>2]=date.getSeconds();HEAP32[tmPtr+4>>2]=date.getMinutes();HEAP32[tmPtr+8>>2]=date.getHours();HEAP32[tmPtr+12>>2]=date.getDate();HEAP32[tmPtr+16>>2]=date.getMonth();HEAP32[tmPtr+20>>2]=date.getYear();return date.getTime()/1e3|0};function __munmap_js(addr,len,prot,flags,fd,offset_low,offset_high){try{var offset=convertI32PairToI53Checked(offset_low,offset_high);if(isNaN(offset))return-61;var stream=SYSCALLS.getStreamFromFD(fd);if(prot&2){SYSCALLS.doMsync(addr,stream,len,flags,offset)}FS.munmap(stream)}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return-e.errno}}var stringToNewUTF8=str=>{var size=lengthBytesUTF8(str)+1;var ret=_malloc(size);if(ret)stringToUTF8(str,ret,size);return ret};var __tzset_js=(timezone,daylight,tzname)=>{var currentYear=(new Date).getFullYear();var winter=new Date(currentYear,0,1);var summer=new Date(currentYear,6,1);var winterOffset=winter.getTimezoneOffset();var summerOffset=summer.getTimezoneOffset();var stdTimezoneOffset=Math.max(winterOffset,summerOffset);HEAPU32[timezone>>2]=stdTimezoneOffset*60;HEAP32[daylight>>2]=Number(winterOffset!=summerOffset);function extractZone(date){var match=date.toTimeString().match(/\(([A-Za-z ]+)\)$/);return match?match[1]:"GMT"}var winterName=extractZone(winter);var summerName=extractZone(summer);var winterNamePtr=stringToNewUTF8(winterName);var summerNamePtr=stringToNewUTF8(summerName);if(summerOffset<winterOffset){HEAPU32[tzname>>2]=winterNamePtr;HEAPU32[tzname+4>>2]=summerNamePtr}else{HEAPU32[tzname>>2]=summerNamePtr;HEAPU32[tzname+4>>2]=winterNamePtr}};var _abort=()=>{abort("native code called abort()")};function _emscripten_date_now(){return Date.now()}var _emscripten_err=str=>err(UTF8ToString(str));var _emscripten_get_now;_emscripten_get_now=()=>performance.now();var _emscripten_memcpy_big=(dest,src,num)=>HEAPU8.copyWithin(dest,src,src+num);var getHeapMax=()=>2147483648;var growMemory=size=>{var b=wasmMemory.buffer;var pages=size-b.byteLength+65535>>>16;try{wasmMemory.grow(pages);updateMemoryViews();return 1}catch(e){err(`growMemory: Attempted to grow heap from ${b.byteLength} bytes to ${size} bytes, but got error: ${e}`)}};var _emscripten_resize_heap=requestedSize=>{var oldSize=HEAPU8.length;requestedSize=requestedSize>>>0;assert(requestedSize>oldSize);var maxHeapSize=getHeapMax();if(requestedSize>maxHeapSize){err(`Cannot enlarge memory, asked to go up to ${requestedSize} bytes, but the limit is ${maxHeapSize} bytes!`);return false}var alignUp=(x,multiple)=>x+(multiple-x%multiple)%multiple;for(var cutDown=1;cutDown<=4;cutDown*=2){var overGrownHeapSize=oldSize*(1+.2/cutDown);overGrownHeapSize=Math.min(overGrownHeapSize,requestedSize+100663296);var newSize=Math.min(maxHeapSize,alignUp(Math.max(requestedSize,overGrownHeapSize),65536));var replacement=growMemory(newSize);if(replacement){return true}}err(`Failed to grow the heap from ${oldSize} bytes to ${newSize} bytes, not enough memory!`);return false};var ENV={};var getExecutableName=()=>{return thisProgram||"./this.program"};var getEnvStrings=()=>{if(!getEnvStrings.strings){var lang=(typeof navigator=="object"&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8";var env={"USER":"web_user","LOGNAME":"web_user","PATH":"/","PWD":"/","HOME":"/home/<USER>","LANG":lang,"_":getExecutableName()};for(var x in ENV){if(ENV[x]===undefined)delete env[x];else env[x]=ENV[x]}var strings=[];for(var x in env){strings.push(`${x}=${env[x]}`)}getEnvStrings.strings=strings}return getEnvStrings.strings};var stringToAscii=(str,buffer)=>{for(var i=0;i<str.length;++i){assert(str.charCodeAt(i)===(str.charCodeAt(i)&255));HEAP8[buffer++>>0]=str.charCodeAt(i)}HEAP8[buffer>>0]=0};var _environ_get=(__environ,environ_buf)=>{var bufSize=0;getEnvStrings().forEach(function(string,i){var ptr=environ_buf+bufSize;HEAPU32[__environ+i*4>>2]=ptr;stringToAscii(string,ptr);bufSize+=string.length+1});return 0};var _environ_sizes_get=(penviron_count,penviron_buf_size)=>{var strings=getEnvStrings();HEAPU32[penviron_count>>2]=strings.length;var bufSize=0;strings.forEach(function(string){bufSize+=string.length+1});HEAPU32[penviron_buf_size>>2]=bufSize;return 0};function _fd_close(fd){try{var stream=SYSCALLS.getStreamFromFD(fd);FS.close(stream);return 0}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return e.errno}}var doReadv=(stream,iov,iovcnt,offset)=>{var ret=0;for(var i=0;i<iovcnt;i++){var ptr=HEAPU32[iov>>2];var len=HEAPU32[iov+4>>2];iov+=8;var curr=FS.read(stream,HEAP8,ptr,len,offset);if(curr<0)return-1;ret+=curr;if(curr<len)break;if(typeof offset!=="undefined"){offset+=curr}}return ret};function _fd_read(fd,iov,iovcnt,pnum){try{var stream=SYSCALLS.getStreamFromFD(fd);var num=doReadv(stream,iov,iovcnt);HEAPU32[pnum>>2]=num;return 0}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return e.errno}}function _fd_seek(fd,offset_low,offset_high,whence,newOffset){try{var offset=convertI32PairToI53Checked(offset_low,offset_high);if(isNaN(offset))return 61;var stream=SYSCALLS.getStreamFromFD(fd);FS.llseek(stream,offset,whence);tempI64=[stream.position>>>0,(tempDouble=stream.position,+Math.abs(tempDouble)>=1?tempDouble>0?+Math.floor(tempDouble/4294967296)>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],HEAP32[newOffset>>2]=tempI64[0],HEAP32[newOffset+4>>2]=tempI64[1];if(stream.getdents&&offset===0&&whence===0)stream.getdents=null;return 0}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return e.errno}}function _fd_sync(fd){try{var stream=SYSCALLS.getStreamFromFD(fd);if(stream.stream_ops&&stream.stream_ops.fsync){return stream.stream_ops.fsync(stream)}return 0}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return e.errno}}var doWritev=(stream,iov,iovcnt,offset)=>{var ret=0;for(var i=0;i<iovcnt;i++){var ptr=HEAPU32[iov>>2];var len=HEAPU32[iov+4>>2];iov+=8;var curr=FS.write(stream,HEAP8,ptr,len,offset);if(curr<0)return-1;ret+=curr;if(typeof offset!=="undefined"){offset+=curr}}return ret};function _fd_write(fd,iov,iovcnt,pnum){try{var stream=SYSCALLS.getStreamFromFD(fd);var num=doWritev(stream,iov,iovcnt);HEAPU32[pnum>>2]=num;return 0}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return e.errno}}var _getentropy=(buffer,size)=>{randomFill(HEAPU8.subarray(buffer,buffer+size));return 0};function _llvm_eh_typeid_for(type){return type}var arraySum=(array,index)=>{var sum=0;for(var i=0;i<=index;sum+=array[i++]){}return sum};var MONTH_DAYS_LEAP=[31,29,31,30,31,30,31,31,30,31,30,31];var MONTH_DAYS_REGULAR=[31,28,31,30,31,30,31,31,30,31,30,31];var addDays=(date,days)=>{var newDate=new Date(date.getTime());while(days>0){var leap=isLeapYear(newDate.getFullYear());var currentMonth=newDate.getMonth();var daysInCurrentMonth=(leap?MONTH_DAYS_LEAP:MONTH_DAYS_REGULAR)[currentMonth];if(days>daysInCurrentMonth-newDate.getDate()){days-=daysInCurrentMonth-newDate.getDate()+1;newDate.setDate(1);if(currentMonth<11){newDate.setMonth(currentMonth+1)}else{newDate.setMonth(0);newDate.setFullYear(newDate.getFullYear()+1)}}else{newDate.setDate(newDate.getDate()+days);return newDate}}return newDate};var writeArrayToMemory=(array,buffer)=>{assert(array.length>=0,"writeArrayToMemory array must have a length (should be an array or typed array)");HEAP8.set(array,buffer)};var _strftime=(s,maxsize,format,tm)=>{var tm_zone=HEAP32[tm+40>>2];var date={tm_sec:HEAP32[tm>>2],tm_min:HEAP32[tm+4>>2],tm_hour:HEAP32[tm+8>>2],tm_mday:HEAP32[tm+12>>2],tm_mon:HEAP32[tm+16>>2],tm_year:HEAP32[tm+20>>2],tm_wday:HEAP32[tm+24>>2],tm_yday:HEAP32[tm+28>>2],tm_isdst:HEAP32[tm+32>>2],tm_gmtoff:HEAP32[tm+36>>2],tm_zone:tm_zone?UTF8ToString(tm_zone):""};var pattern=UTF8ToString(format);var EXPANSION_RULES_1={"%c":"%a %b %d %H:%M:%S %Y","%D":"%m/%d/%y","%F":"%Y-%m-%d","%h":"%b","%r":"%I:%M:%S %p","%R":"%H:%M","%T":"%H:%M:%S","%x":"%m/%d/%y","%X":"%H:%M:%S","%Ec":"%c","%EC":"%C","%Ex":"%m/%d/%y","%EX":"%H:%M:%S","%Ey":"%y","%EY":"%Y","%Od":"%d","%Oe":"%e","%OH":"%H","%OI":"%I","%Om":"%m","%OM":"%M","%OS":"%S","%Ou":"%u","%OU":"%U","%OV":"%V","%Ow":"%w","%OW":"%W","%Oy":"%y"};for(var rule in EXPANSION_RULES_1){pattern=pattern.replace(new RegExp(rule,"g"),EXPANSION_RULES_1[rule])}var WEEKDAYS=["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"];var MONTHS=["January","February","March","April","May","June","July","August","September","October","November","December"];function leadingSomething(value,digits,character){var str=typeof value=="number"?value.toString():value||"";while(str.length<digits){str=character[0]+str}return str}function leadingNulls(value,digits){return leadingSomething(value,digits,"0")}function compareByDay(date1,date2){function sgn(value){return value<0?-1:value>0?1:0}var compare;if((compare=sgn(date1.getFullYear()-date2.getFullYear()))===0){if((compare=sgn(date1.getMonth()-date2.getMonth()))===0){compare=sgn(date1.getDate()-date2.getDate())}}return compare}function getFirstWeekStartDate(janFourth){switch(janFourth.getDay()){case 0:return new Date(janFourth.getFullYear()-1,11,29);case 1:return janFourth;case 2:return new Date(janFourth.getFullYear(),0,3);case 3:return new Date(janFourth.getFullYear(),0,2);case 4:return new Date(janFourth.getFullYear(),0,1);case 5:return new Date(janFourth.getFullYear()-1,11,31);case 6:return new Date(janFourth.getFullYear()-1,11,30)}}function getWeekBasedYear(date){var thisDate=addDays(new Date(date.tm_year+1900,0,1),date.tm_yday);var janFourthThisYear=new Date(thisDate.getFullYear(),0,4);var janFourthNextYear=new Date(thisDate.getFullYear()+1,0,4);var firstWeekStartThisYear=getFirstWeekStartDate(janFourthThisYear);var firstWeekStartNextYear=getFirstWeekStartDate(janFourthNextYear);if(compareByDay(firstWeekStartThisYear,thisDate)<=0){if(compareByDay(firstWeekStartNextYear,thisDate)<=0){return thisDate.getFullYear()+1}return thisDate.getFullYear()}return thisDate.getFullYear()-1}var EXPANSION_RULES_2={"%a":date=>WEEKDAYS[date.tm_wday].substring(0,3),"%A":date=>WEEKDAYS[date.tm_wday],"%b":date=>MONTHS[date.tm_mon].substring(0,3),"%B":date=>MONTHS[date.tm_mon],"%C":date=>{var year=date.tm_year+1900;return leadingNulls(year/100|0,2)},"%d":date=>leadingNulls(date.tm_mday,2),"%e":date=>leadingSomething(date.tm_mday,2," "),"%g":date=>{return getWeekBasedYear(date).toString().substring(2)},"%G":date=>getWeekBasedYear(date),"%H":date=>leadingNulls(date.tm_hour,2),"%I":date=>{var twelveHour=date.tm_hour;if(twelveHour==0)twelveHour=12;else if(twelveHour>12)twelveHour-=12;return leadingNulls(twelveHour,2)},"%j":date=>{return leadingNulls(date.tm_mday+arraySum(isLeapYear(date.tm_year+1900)?MONTH_DAYS_LEAP:MONTH_DAYS_REGULAR,date.tm_mon-1),3)},"%m":date=>leadingNulls(date.tm_mon+1,2),"%M":date=>leadingNulls(date.tm_min,2),"%n":()=>"\n","%p":date=>{if(date.tm_hour>=0&&date.tm_hour<12){return"AM"}return"PM"},"%S":date=>leadingNulls(date.tm_sec,2),"%t":()=>"\t","%u":date=>date.tm_wday||7,"%U":date=>{var days=date.tm_yday+7-date.tm_wday;return leadingNulls(Math.floor(days/7),2)},"%V":date=>{var val=Math.floor((date.tm_yday+7-(date.tm_wday+6)%7)/7);if((date.tm_wday+371-date.tm_yday-2)%7<=2){val++}if(!val){val=52;var dec31=(date.tm_wday+7-date.tm_yday-1)%7;if(dec31==4||dec31==5&&isLeapYear(date.tm_year%400-1)){val++}}else if(val==53){var jan1=(date.tm_wday+371-date.tm_yday)%7;if(jan1!=4&&(jan1!=3||!isLeapYear(date.tm_year)))val=1}return leadingNulls(val,2)},"%w":date=>date.tm_wday,"%W":date=>{var days=date.tm_yday+7-(date.tm_wday+6)%7;return leadingNulls(Math.floor(days/7),2)},"%y":date=>{return(date.tm_year+1900).toString().substring(2)},"%Y":date=>date.tm_year+1900,"%z":date=>{var off=date.tm_gmtoff;var ahead=off>=0;off=Math.abs(off)/60;off=off/60*100+off%60;return(ahead?"+":"-")+String("0000"+off).slice(-4)},"%Z":date=>date.tm_zone,"%%":()=>"%"};pattern=pattern.replace(/%%/g,"\0\0");for(var rule in EXPANSION_RULES_2){if(pattern.includes(rule)){pattern=pattern.replace(new RegExp(rule,"g"),EXPANSION_RULES_2[rule](date))}}pattern=pattern.replace(/\0\0/g,"%");var bytes=intArrayFromString(pattern,false);if(bytes.length>maxsize){return 0}writeArrayToMemory(bytes,s);return bytes.length-1};var _strftime_l=(s,maxsize,format,tm,loc)=>{return _strftime(s,maxsize,format,tm)};var wasmTableMirror=[];var getWasmTableEntry=funcPtr=>{var func=wasmTableMirror[funcPtr];if(!func){if(funcPtr>=wasmTableMirror.length)wasmTableMirror.length=funcPtr+1;wasmTableMirror[funcPtr]=func=wasmTable.get(funcPtr)}assert(wasmTable.get(funcPtr)==func,"JavaScript-side Wasm function table mirror is out of date!");return func};function getCFunc(ident){var func=Module["_"+ident];assert(func,"Cannot call unknown function "+ident+", make sure it is exported");return func}var ccall=function(ident,returnType,argTypes,args,opts){var toC={"string":str=>{var ret=0;if(str!==null&&str!==undefined&&str!==0){ret=stringToUTF8OnStack(str)}return ret},"array":arr=>{var ret=stackAlloc(arr.length);writeArrayToMemory(arr,ret);return ret}};function convertReturnValue(ret){if(returnType==="string"){return UTF8ToString(ret)}if(returnType==="boolean")return Boolean(ret);return ret}var func=getCFunc(ident);var cArgs=[];var stack=0;assert(returnType!=="array",'Return type should not be "array".');if(args){for(var i=0;i<args.length;i++){var converter=toC[argTypes[i]];if(converter){if(stack===0)stack=stackSave();cArgs[i]=converter(args[i])}else{cArgs[i]=args[i]}}}var ret=func.apply(null,cArgs);function onDone(ret){if(stack!==0)stackRestore(stack);return convertReturnValue(ret)}ret=onDone(ret);return ret};function cwrap(ident,returnType,argTypes,opts){return function(){return ccall(ident,returnType,argTypes,arguments,opts)}}var FSNode=function(parent,name,mode,rdev){if(!parent){parent=this}this.parent=parent;this.mount=parent.mount;this.mounted=null;this.id=FS.nextInode++;this.name=name;this.mode=mode;this.node_ops={};this.stream_ops={};this.rdev=rdev};var readMode=292|73;var writeMode=146;Object.defineProperties(FSNode.prototype,{read:{get:function(){return(this.mode&readMode)===readMode},set:function(val){val?this.mode|=readMode:this.mode&=~readMode}},write:{get:function(){return(this.mode&writeMode)===writeMode},set:function(val){val?this.mode|=writeMode:this.mode&=~writeMode}},isFolder:{get:function(){return FS.isDir(this.mode)}},isDevice:{get:function(){return FS.isChrdev(this.mode)}}});FS.FSNode=FSNode;FS.createPreloadedFile=FS_createPreloadedFile;FS.staticInit();ERRNO_CODES={"EPERM":63,"ENOENT":44,"ESRCH":71,"EINTR":27,"EIO":29,"ENXIO":60,"E2BIG":1,"ENOEXEC":45,"EBADF":8,"ECHILD":12,"EAGAIN":6,"EWOULDBLOCK":6,"ENOMEM":48,"EACCES":2,"EFAULT":21,"ENOTBLK":105,"EBUSY":10,"EEXIST":20,"EXDEV":75,"ENODEV":43,"ENOTDIR":54,"EISDIR":31,"EINVAL":28,"ENFILE":41,"EMFILE":33,"ENOTTY":59,"ETXTBSY":74,"EFBIG":22,"ENOSPC":51,"ESPIPE":70,"EROFS":69,"EMLINK":34,"EPIPE":64,"EDOM":18,"ERANGE":68,"ENOMSG":49,"EIDRM":24,"ECHRNG":106,"EL2NSYNC":156,"EL3HLT":107,"EL3RST":108,"ELNRNG":109,"EUNATCH":110,"ENOCSI":111,"EL2HLT":112,"EDEADLK":16,"ENOLCK":46,"EBADE":113,"EBADR":114,"EXFULL":115,"ENOANO":104,"EBADRQC":103,"EBADSLT":102,"EDEADLOCK":16,"EBFONT":101,"ENOSTR":100,"ENODATA":116,"ETIME":117,"ENOSR":118,"ENONET":119,"ENOPKG":120,"EREMOTE":121,"ENOLINK":47,"EADV":122,"ESRMNT":123,"ECOMM":124,"EPROTO":65,"EMULTIHOP":36,"EDOTDOT":125,"EBADMSG":9,"ENOTUNIQ":126,"EBADFD":127,"EREMCHG":128,"ELIBACC":129,"ELIBBAD":130,"ELIBSCN":131,"ELIBMAX":132,"ELIBEXEC":133,"ENOSYS":52,"ENOTEMPTY":55,"ENAMETOOLONG":37,"ELOOP":32,"EOPNOTSUPP":138,"EPFNOSUPPORT":139,"ECONNRESET":15,"ENOBUFS":42,"EAFNOSUPPORT":5,"EPROTOTYPE":67,"ENOTSOCK":57,"ENOPROTOOPT":50,"ESHUTDOWN":140,"ECONNREFUSED":14,"EADDRINUSE":3,"ECONNABORTED":13,"ENETUNREACH":40,"ENETDOWN":38,"ETIMEDOUT":73,"EHOSTDOWN":142,"EHOSTUNREACH":23,"EINPROGRESS":26,"EALREADY":7,"EDESTADDRREQ":17,"EMSGSIZE":35,"EPROTONOSUPPORT":66,"ESOCKTNOSUPPORT":137,"EADDRNOTAVAIL":4,"ENETRESET":39,"EISCONN":30,"ENOTCONN":53,"ETOOMANYREFS":141,"EUSERS":136,"EDQUOT":19,"ESTALE":72,"ENOTSUP":138,"ENOMEDIUM":148,"EILSEQ":25,"EOVERFLOW":61,"ECANCELED":11,"ENOTRECOVERABLE":56,"EOWNERDEAD":62,"ESTRPIPE":135};function checkIncomingModuleAPI(){ignoredModuleProp("fetchSettings")}var wasmImports={"ComPDFKitJS_DefFontListNum":_ComPDFKitJS_DefFontListNum,"ComPDFKitJS_FontCount":_ComPDFKitJS_FontCount,"ComPDFKitJS_GetFontName":_ComPDFKitJS_GetFontName,"ComPDFKitJS_Get_AccessInfoArray":_ComPDFKitJS_Get_AccessInfoArray,"ComPDFKitJS_Get_AnnotArray":_ComPDFKitJS_Get_AnnotArray,"ComPDFKitJS_Get_AnnotDictName":_ComPDFKitJS_Get_AnnotDictName,"ComPDFKitJS_Get_BorderStyle":_ComPDFKitJS_Get_BorderStyle,"ComPDFKitJS_Get_CRLPointArray":_ComPDFKitJS_Get_CRLPointArray,"ComPDFKitJS_Get_CertArray":_ComPDFKitJS_Get_CertArray,"ComPDFKitJS_Get_Cmyk":_ComPDFKitJS_Get_Cmyk,"ComPDFKitJS_Get_CompareResultArray":_ComPDFKitJS_Get_CompareResultArray,"ComPDFKitJS_Get_CursorPoints":_ComPDFKitJS_Get_CursorPoints,"ComPDFKitJS_Get_DiffInfoArray":_ComPDFKitJS_Get_DiffInfoArray,"ComPDFKitJS_Get_EditTextStyle":_ComPDFKitJS_Get_EditTextStyle,"ComPDFKitJS_Get_EndPSOPDFEditCharPlace":_ComPDFKitJS_Get_EndPSOPDFEditCharPlace,"ComPDFKitJS_Get_FontAlignment":_ComPDFKitJS_Get_FontAlignment,"ComPDFKitJS_Get_FontDa":_ComPDFKitJS_Get_FontDa,"ComPDFKitJS_Get_ImageAreaInfo":_ComPDFKitJS_Get_ImageAreaInfo,"ComPDFKitJS_Get_InkPoints":_ComPDFKitJS_Get_InkPoints,"ComPDFKitJS_Get_LinePoint":_ComPDFKitJS_Get_LinePoint,"ComPDFKitJS_Get_LineType":_ComPDFKitJS_Get_LineType,"ComPDFKitJS_Get_MeasureInfo":_ComPDFKitJS_Get_MeasureInfo,"ComPDFKitJS_Get_Measure_Vertices":_ComPDFKitJS_Get_Measure_Vertices,"ComPDFKitJS_Get_OCGS":_ComPDFKitJS_Get_OCGS,"ComPDFKitJS_Get_OCSPUrlArray":_ComPDFKitJS_Get_OCSPUrlArray,"ComPDFKitJS_Get_PDFDestination":_ComPDFKitJS_Get_PDFDestination,"ComPDFKitJS_Get_PDFRange":_ComPDFKitJS_Get_PDFRange,"ComPDFKitJS_Get_PSOPDFEditCharPlace":_ComPDFKitJS_Get_PSOPDFEditCharPlace,"ComPDFKitJS_Get_PageSize":_ComPDFKitJS_Get_PageSize,"ComPDFKitJS_Get_PermissionsInfo":_ComPDFKitJS_Get_PermissionsInfo,"ComPDFKitJS_Get_PolicyArray":_ComPDFKitJS_Get_PolicyArray,"ComPDFKitJS_Get_PrintUsage":_ComPDFKitJS_Get_PrintUsage,"ComPDFKitJS_Get_RGBAColor":_ComPDFKitJS_Get_RGBAColor,"ComPDFKitJS_Get_RGBColor":_ComPDFKitJS_Get_RGBColor,"ComPDFKitJS_Get_Rect":_ComPDFKitJS_Get_Rect,"ComPDFKitJS_Get_RectArray":_ComPDFKitJS_Get_RectArray,"ComPDFKitJS_Get_ResultsCountParams":_ComPDFKitJS_Get_ResultsCountParams,"ComPDFKitJS_Get_SelectPointColor":_ComPDFKitJS_Get_SelectPointColor,"ComPDFKitJS_Get_SepColors":_ComPDFKitJS_Get_SepColors,"ComPDFKitJS_Get_SignerArray":_ComPDFKitJS_Get_SignerArray,"ComPDFKitJS_Get_SnappingMode":_ComPDFKitJS_Get_SnappingMode,"ComPDFKitJS_Get_TextFindItem":_ComPDFKitJS_Get_TextFindItem,"ComPDFKitJS_Get_TextRectArray":_ComPDFKitJS_Get_TextRectArray,"ComPDFKitJS_Get_TextStamp":_ComPDFKitJS_Get_TextStamp,"ComPDFKitJS_Get_Transparency":_ComPDFKitJS_Get_Transparency,"ComPDFKitJS_Get_UTF8String":_ComPDFKitJS_Get_UTF8String,"ComPDFKitJS_Get_WidgetItem":_ComPDFKitJS_Get_WidgetItem,"ComPDFKitJS_Get_WidgetItemArray":_ComPDFKitJS_Get_WidgetItemArray,"ComPDFKitJS_Get_XFDFData":_ComPDFKitJS_Get_XFDFData,"ComPDFKitJS_Get_ZoomUsage":_ComPDFKitJS_Get_ZoomUsage,"ComPDFKitJS_LogString":_ComPDFKitJS_LogString,"ComPDFKitJS_read_Cert":_ComPDFKitJS_read_Cert,"ComPDFKitJS_read_DefFont":_ComPDFKitJS_read_DefFont,"ComPDFKitJS_read_DefFontSize":_ComPDFKitJS_read_DefFontSize,"ComPDFKitJS_read_Font":_ComPDFKitJS_read_Font,"ComPDFKitJS_read_FontSize":_ComPDFKitJS_read_FontSize,"ComPDFKitJS_read_Image":_ComPDFKitJS_read_Image,"ComPDFKitJS_read_file":_ComPDFKitJS_read_file,"ComPDFKitJS_write_Cert":_ComPDFKitJS_write_Cert,"ComPDFKitJS_write_file":_ComPDFKitJS_write_file,"__assert_fail":___assert_fail,"__cxa_begin_catch":___cxa_begin_catch,"__cxa_end_catch":___cxa_end_catch,"__cxa_find_matching_catch_2":___cxa_find_matching_catch_2,"__cxa_find_matching_catch_3":___cxa_find_matching_catch_3,"__cxa_find_matching_catch_4":___cxa_find_matching_catch_4,"__cxa_rethrow":___cxa_rethrow,"__cxa_throw":___cxa_throw,"__cxa_uncaught_exceptions":___cxa_uncaught_exceptions,"__resumeException":___resumeException,"__syscall_fcntl64":___syscall_fcntl64,"__syscall_fstat64":___syscall_fstat64,"__syscall_ftruncate64":___syscall_ftruncate64,"__syscall_getdents64":___syscall_getdents64,"__syscall_ioctl":___syscall_ioctl,"__syscall_lstat64":___syscall_lstat64,"__syscall_newfstatat":___syscall_newfstatat,"__syscall_openat":___syscall_openat,"__syscall_rmdir":___syscall_rmdir,"__syscall_stat64":___syscall_stat64,"__syscall_unlinkat":___syscall_unlinkat,"_emscripten_get_now_is_monotonic":__emscripten_get_now_is_monotonic,"_emscripten_throw_longjmp":__emscripten_throw_longjmp,"_gmtime_js":__gmtime_js,"_localtime_js":__localtime_js,"_mktime_js":__mktime_js,"_munmap_js":__munmap_js,"_tzset_js":__tzset_js,"abort":_abort,"emscripten_date_now":_emscripten_date_now,"emscripten_err":_emscripten_err,"emscripten_get_now":_emscripten_get_now,"emscripten_memcpy_big":_emscripten_memcpy_big,"emscripten_resize_heap":_emscripten_resize_heap,"environ_get":_environ_get,"environ_sizes_get":_environ_sizes_get,"fd_close":_fd_close,"fd_read":_fd_read,"fd_seek":_fd_seek,"fd_sync":_fd_sync,"fd_write":_fd_write,"getentropy":_getentropy,"invoke_diii":invoke_diii,"invoke_fiii":invoke_fiii,"invoke_i":invoke_i,"invoke_ii":invoke_ii,"invoke_iid":invoke_iid,"invoke_iii":invoke_iii,"invoke_iiii":invoke_iiii,"invoke_iiiii":invoke_iiiii,"invoke_iiiiid":invoke_iiiiid,"invoke_iiiiii":invoke_iiiiii,"invoke_iiiiiii":invoke_iiiiiii,"invoke_iiiiiiii":invoke_iiiiiiii,"invoke_iiiiiiiii":invoke_iiiiiiiii,"invoke_iiiiiiiiiii":invoke_iiiiiiiiiii,"invoke_iiiiiiiiiiii":invoke_iiiiiiiiiiii,"invoke_iiiiiiiiiiiii":invoke_iiiiiiiiiiiii,"invoke_iiiiij":invoke_iiiiij,"invoke_iij":invoke_iij,"invoke_iiji":invoke_iiji,"invoke_j":invoke_j,"invoke_ji":invoke_ji,"invoke_jii":invoke_jii,"invoke_jiiii":invoke_jiiii,"invoke_v":invoke_v,"invoke_vi":invoke_vi,"invoke_vii":invoke_vii,"invoke_viid":invoke_viid,"invoke_viif":invoke_viif,"invoke_viii":invoke_viii,"invoke_viiii":invoke_viiii,"invoke_viiiii":invoke_viiiii,"invoke_viiiiii":invoke_viiiiii,"invoke_viiiiiii":invoke_viiiiiii,"invoke_viiiiiiiii":invoke_viiiiiiiii,"invoke_viiiiiiiiii":invoke_viiiiiiiiii,"invoke_viiiiiiiiiiiiiii":invoke_viiiiiiiiiiiiiii,"invoke_viijii":invoke_viijii,"llvm_eh_typeid_for":_llvm_eh_typeid_for,"strftime":_strftime,"strftime_l":_strftime_l};var asm=createWasm();var ___wasm_call_ctors=createExportWrapper("__wasm_call_ctors");var _print_the_answer=Module["_print_the_answer"]=createExportWrapper("print_the_answer");var _FPDF_InitLibraryWithConfig=Module["_FPDF_InitLibraryWithConfig"]=createExportWrapper("FPDF_InitLibraryWithConfig");var _FPDF_LoadDocument=Module["_FPDF_LoadDocument"]=createExportWrapper("FPDF_LoadDocument");var _FPDF_GetLastError=Module["_FPDF_GetLastError"]=createExportWrapper("FPDF_GetLastError");var _FPDFBitmap_CreateEx=Module["_FPDFBitmap_CreateEx"]=createExportWrapper("FPDFBitmap_CreateEx");var _FPDF_GetPageCount=Module["_FPDF_GetPageCount"]=createExportWrapper("FPDF_GetPageCount");var _FPDF_CloseDocument=Module["_FPDF_CloseDocument"]=createExportWrapper("FPDF_CloseDocument");var _FPDF_DestroyLibrary=Module["_FPDF_DestroyLibrary"]=createExportWrapper("FPDF_DestroyLibrary");var _FPDFAnnot_IsSupportedSubtype=Module["_FPDFAnnot_IsSupportedSubtype"]=createExportWrapper("FPDFAnnot_IsSupportedSubtype");var _FPDFPage_CreateAnnot=Module["_FPDFPage_CreateAnnot"]=createExportWrapper("FPDFPage_CreateAnnot");var _FPDFPage_GetAnnotCount=Module["_FPDFPage_GetAnnotCount"]=createExportWrapper("FPDFPage_GetAnnotCount");var _FPDFPage_GetAnnot=Module["_FPDFPage_GetAnnot"]=createExportWrapper("FPDFPage_GetAnnot");var _FPDFPage_GetAnnotIndex=Module["_FPDFPage_GetAnnotIndex"]=createExportWrapper("FPDFPage_GetAnnotIndex");var _FPDFPage_CloseAnnot=Module["_FPDFPage_CloseAnnot"]=createExportWrapper("FPDFPage_CloseAnnot");var _FPDFPage_RemoveAnnot=Module["_FPDFPage_RemoveAnnot"]=createExportWrapper("FPDFPage_RemoveAnnot");var _FPDFAnnot_GetSubtype=Module["_FPDFAnnot_GetSubtype"]=createExportWrapper("FPDFAnnot_GetSubtype");var _FPDFAnnot_IsObjectSupportedSubtype=Module["_FPDFAnnot_IsObjectSupportedSubtype"]=createExportWrapper("FPDFAnnot_IsObjectSupportedSubtype");var _FPDFAnnot_UpdateObject=Module["_FPDFAnnot_UpdateObject"]=createExportWrapper("FPDFAnnot_UpdateObject");var _FPDFAnnot_AddInkStroke=Module["_FPDFAnnot_AddInkStroke"]=createExportWrapper("FPDFAnnot_AddInkStroke");var _FPDFAnnot_RemoveInkList=Module["_FPDFAnnot_RemoveInkList"]=createExportWrapper("FPDFAnnot_RemoveInkList");var _FPDFAnnot_AppendObject=Module["_FPDFAnnot_AppendObject"]=createExportWrapper("FPDFAnnot_AppendObject");var _FPDFAnnot_GetObjectCount=Module["_FPDFAnnot_GetObjectCount"]=createExportWrapper("FPDFAnnot_GetObjectCount");var _FPDFAnnot_GetObject=Module["_FPDFAnnot_GetObject"]=createExportWrapper("FPDFAnnot_GetObject");var _FPDFAnnot_RemoveObject=Module["_FPDFAnnot_RemoveObject"]=createExportWrapper("FPDFAnnot_RemoveObject");var _FPDFAnnot_SetColor=Module["_FPDFAnnot_SetColor"]=createExportWrapper("FPDFAnnot_SetColor");var _FPDFAnnot_GetColor=Module["_FPDFAnnot_GetColor"]=createExportWrapper("FPDFAnnot_GetColor");var _FPDFAnnot_HasAttachmentPoints=Module["_FPDFAnnot_HasAttachmentPoints"]=createExportWrapper("FPDFAnnot_HasAttachmentPoints");var _FPDFAnnot_SetAttachmentPoints=Module["_FPDFAnnot_SetAttachmentPoints"]=createExportWrapper("FPDFAnnot_SetAttachmentPoints");var _FPDFAnnot_AppendAttachmentPoints=Module["_FPDFAnnot_AppendAttachmentPoints"]=createExportWrapper("FPDFAnnot_AppendAttachmentPoints");var _FPDFAnnot_CountAttachmentPoints=Module["_FPDFAnnot_CountAttachmentPoints"]=createExportWrapper("FPDFAnnot_CountAttachmentPoints");var _FPDFAnnot_GetAttachmentPoints=Module["_FPDFAnnot_GetAttachmentPoints"]=createExportWrapper("FPDFAnnot_GetAttachmentPoints");var _FPDFAnnot_SetRect=Module["_FPDFAnnot_SetRect"]=createExportWrapper("FPDFAnnot_SetRect");var _FPDFAnnot_GetRect=Module["_FPDFAnnot_GetRect"]=createExportWrapper("FPDFAnnot_GetRect");var _FPDFAnnot_GetVertices=Module["_FPDFAnnot_GetVertices"]=createExportWrapper("FPDFAnnot_GetVertices");var _FPDFAnnot_GetInkListCount=Module["_FPDFAnnot_GetInkListCount"]=createExportWrapper("FPDFAnnot_GetInkListCount");var _FPDFAnnot_GetInkListPath=Module["_FPDFAnnot_GetInkListPath"]=createExportWrapper("FPDFAnnot_GetInkListPath");var _FPDFAnnot_GetLine=Module["_FPDFAnnot_GetLine"]=createExportWrapper("FPDFAnnot_GetLine");var _FPDFAnnot_SetBorder=Module["_FPDFAnnot_SetBorder"]=createExportWrapper("FPDFAnnot_SetBorder");var _FPDFAnnot_GetBorder=Module["_FPDFAnnot_GetBorder"]=createExportWrapper("FPDFAnnot_GetBorder");var _FPDFAnnot_HasKey=Module["_FPDFAnnot_HasKey"]=createExportWrapper("FPDFAnnot_HasKey");var _FPDFAnnot_GetValueType=Module["_FPDFAnnot_GetValueType"]=createExportWrapper("FPDFAnnot_GetValueType");var _FPDFAnnot_SetStringValue=Module["_FPDFAnnot_SetStringValue"]=createExportWrapper("FPDFAnnot_SetStringValue");var _FPDFAnnot_GetStringValue=Module["_FPDFAnnot_GetStringValue"]=createExportWrapper("FPDFAnnot_GetStringValue");var _FPDFAnnot_GetNumberValue=Module["_FPDFAnnot_GetNumberValue"]=createExportWrapper("FPDFAnnot_GetNumberValue");var _FPDFAnnot_SetAP=Module["_FPDFAnnot_SetAP"]=createExportWrapper("FPDFAnnot_SetAP");var _FPDFAnnot_GetAP=Module["_FPDFAnnot_GetAP"]=createExportWrapper("FPDFAnnot_GetAP");var _FPDFAnnot_GetLinkedAnnot=Module["_FPDFAnnot_GetLinkedAnnot"]=createExportWrapper("FPDFAnnot_GetLinkedAnnot");var _FPDFAnnot_GetFlags=Module["_FPDFAnnot_GetFlags"]=createExportWrapper("FPDFAnnot_GetFlags");var _FPDFAnnot_SetFlags=Module["_FPDFAnnot_SetFlags"]=createExportWrapper("FPDFAnnot_SetFlags");var _FPDFAnnot_GetFormFieldFlags=Module["_FPDFAnnot_GetFormFieldFlags"]=createExportWrapper("FPDFAnnot_GetFormFieldFlags");var _FPDFAnnot_GetFormFieldAtPoint=Module["_FPDFAnnot_GetFormFieldAtPoint"]=createExportWrapper("FPDFAnnot_GetFormFieldAtPoint");var _FPDFAnnot_GetFormFieldName=Module["_FPDFAnnot_GetFormFieldName"]=createExportWrapper("FPDFAnnot_GetFormFieldName");var _FPDFAnnot_GetFormFieldType=Module["_FPDFAnnot_GetFormFieldType"]=createExportWrapper("FPDFAnnot_GetFormFieldType");var _FPDFAnnot_GetFormAdditionalActionJavaScript=Module["_FPDFAnnot_GetFormAdditionalActionJavaScript"]=createExportWrapper("FPDFAnnot_GetFormAdditionalActionJavaScript");var _FPDFAnnot_GetFormFieldAlternateName=Module["_FPDFAnnot_GetFormFieldAlternateName"]=createExportWrapper("FPDFAnnot_GetFormFieldAlternateName");var _FPDFAnnot_GetFormFieldValue=Module["_FPDFAnnot_GetFormFieldValue"]=createExportWrapper("FPDFAnnot_GetFormFieldValue");var _FPDFAnnot_GetOptionCount=Module["_FPDFAnnot_GetOptionCount"]=createExportWrapper("FPDFAnnot_GetOptionCount");var _FPDFAnnot_GetOptionLabel=Module["_FPDFAnnot_GetOptionLabel"]=createExportWrapper("FPDFAnnot_GetOptionLabel");var _FPDFAnnot_IsOptionSelected=Module["_FPDFAnnot_IsOptionSelected"]=createExportWrapper("FPDFAnnot_IsOptionSelected");var _FPDFAnnot_GetFontSize=Module["_FPDFAnnot_GetFontSize"]=createExportWrapper("FPDFAnnot_GetFontSize");var _FPDFAnnot_IsChecked=Module["_FPDFAnnot_IsChecked"]=createExportWrapper("FPDFAnnot_IsChecked");var _FPDFAnnot_SetFocusableSubtypes=Module["_FPDFAnnot_SetFocusableSubtypes"]=createExportWrapper("FPDFAnnot_SetFocusableSubtypes");var _FPDFAnnot_GetFocusableSubtypesCount=Module["_FPDFAnnot_GetFocusableSubtypesCount"]=createExportWrapper("FPDFAnnot_GetFocusableSubtypesCount");var _FPDFAnnot_GetFocusableSubtypes=Module["_FPDFAnnot_GetFocusableSubtypes"]=createExportWrapper("FPDFAnnot_GetFocusableSubtypes");var _FPDFAnnot_GetLink=Module["_FPDFAnnot_GetLink"]=createExportWrapper("FPDFAnnot_GetLink");var _FPDFAnnot_GetFormControlCount=Module["_FPDFAnnot_GetFormControlCount"]=createExportWrapper("FPDFAnnot_GetFormControlCount");var _FPDFAnnot_GetFormControlIndex=Module["_FPDFAnnot_GetFormControlIndex"]=createExportWrapper("FPDFAnnot_GetFormControlIndex");var _FPDFAnnot_GetFormFieldExportValue=Module["_FPDFAnnot_GetFormFieldExportValue"]=createExportWrapper("FPDFAnnot_GetFormFieldExportValue");var _FPDFAnnot_SetURI=Module["_FPDFAnnot_SetURI"]=createExportWrapper("FPDFAnnot_SetURI");var _FPDFDoc_GetAttachmentCount=Module["_FPDFDoc_GetAttachmentCount"]=createExportWrapper("FPDFDoc_GetAttachmentCount");var _FPDFDoc_AddAttachment=Module["_FPDFDoc_AddAttachment"]=createExportWrapper("FPDFDoc_AddAttachment");var _FPDFDoc_GetAttachment=Module["_FPDFDoc_GetAttachment"]=createExportWrapper("FPDFDoc_GetAttachment");var _FPDFDoc_DeleteAttachment=Module["_FPDFDoc_DeleteAttachment"]=createExportWrapper("FPDFDoc_DeleteAttachment");var _FPDFAttachment_GetName=Module["_FPDFAttachment_GetName"]=createExportWrapper("FPDFAttachment_GetName");var _FPDFAttachment_HasKey=Module["_FPDFAttachment_HasKey"]=createExportWrapper("FPDFAttachment_HasKey");var _FPDFAttachment_GetValueType=Module["_FPDFAttachment_GetValueType"]=createExportWrapper("FPDFAttachment_GetValueType");var _FPDFAttachment_SetStringValue=Module["_FPDFAttachment_SetStringValue"]=createExportWrapper("FPDFAttachment_SetStringValue");var _FPDFAttachment_GetStringValue=Module["_FPDFAttachment_GetStringValue"]=createExportWrapper("FPDFAttachment_GetStringValue");var _FPDFAttachment_SetFile=Module["_FPDFAttachment_SetFile"]=createExportWrapper("FPDFAttachment_SetFile");var _FPDFAttachment_GetFile=Module["_FPDFAttachment_GetFile"]=createExportWrapper("FPDFAttachment_GetFile");var _FPDFCatalog_IsTagged=Module["_FPDFCatalog_IsTagged"]=createExportWrapper("FPDFCatalog_IsTagged");var _FPDFAvail_Create=Module["_FPDFAvail_Create"]=createExportWrapper("FPDFAvail_Create");var _FPDFAvail_Destroy=Module["_FPDFAvail_Destroy"]=createExportWrapper("FPDFAvail_Destroy");var _FPDFAvail_IsDocAvail=Module["_FPDFAvail_IsDocAvail"]=createExportWrapper("FPDFAvail_IsDocAvail");var _FPDFAvail_GetDocument=Module["_FPDFAvail_GetDocument"]=createExportWrapper("FPDFAvail_GetDocument");var _FPDFAvail_GetFirstPageNum=Module["_FPDFAvail_GetFirstPageNum"]=createExportWrapper("FPDFAvail_GetFirstPageNum");var _FPDFAvail_IsPageAvail=Module["_FPDFAvail_IsPageAvail"]=createExportWrapper("FPDFAvail_IsPageAvail");var _FPDFAvail_IsFormAvail=Module["_FPDFAvail_IsFormAvail"]=createExportWrapper("FPDFAvail_IsFormAvail");var _FPDFAvail_IsLinearized=Module["_FPDFAvail_IsLinearized"]=createExportWrapper("FPDFAvail_IsLinearized");var _FPDFBookmark_GetFirstChild=Module["_FPDFBookmark_GetFirstChild"]=createExportWrapper("FPDFBookmark_GetFirstChild");var _FPDFBookmark_GetNextSibling=Module["_FPDFBookmark_GetNextSibling"]=createExportWrapper("FPDFBookmark_GetNextSibling");var _FPDFBookmark_GetTitle=Module["_FPDFBookmark_GetTitle"]=createExportWrapper("FPDFBookmark_GetTitle");var _FPDFBookmark_GetCount=Module["_FPDFBookmark_GetCount"]=createExportWrapper("FPDFBookmark_GetCount");var _FPDFBookmark_Find=Module["_FPDFBookmark_Find"]=createExportWrapper("FPDFBookmark_Find");var _FPDFBookmark_GetDest=Module["_FPDFBookmark_GetDest"]=createExportWrapper("FPDFBookmark_GetDest");var _FPDFBookmark_GetAction=Module["_FPDFBookmark_GetAction"]=createExportWrapper("FPDFBookmark_GetAction");var _FPDFAction_GetType=Module["_FPDFAction_GetType"]=createExportWrapper("FPDFAction_GetType");var _FPDFAction_GetDest=Module["_FPDFAction_GetDest"]=createExportWrapper("FPDFAction_GetDest");var _FPDFAction_GetFilePath=Module["_FPDFAction_GetFilePath"]=createExportWrapper("FPDFAction_GetFilePath");var _FPDFAction_GetURIPath=Module["_FPDFAction_GetURIPath"]=createExportWrapper("FPDFAction_GetURIPath");var _FPDFDest_GetDestPageIndex=Module["_FPDFDest_GetDestPageIndex"]=createExportWrapper("FPDFDest_GetDestPageIndex");var _FPDFDest_GetView=Module["_FPDFDest_GetView"]=createExportWrapper("FPDFDest_GetView");var _FPDFDest_GetLocationInPage=Module["_FPDFDest_GetLocationInPage"]=createExportWrapper("FPDFDest_GetLocationInPage");var _FPDFLink_GetLinkAtPoint=Module["_FPDFLink_GetLinkAtPoint"]=createExportWrapper("FPDFLink_GetLinkAtPoint");var _FPDFLink_GetLinkZOrderAtPoint=Module["_FPDFLink_GetLinkZOrderAtPoint"]=createExportWrapper("FPDFLink_GetLinkZOrderAtPoint");var _FPDFLink_GetDest=Module["_FPDFLink_GetDest"]=createExportWrapper("FPDFLink_GetDest");var _FPDFLink_GetAction=Module["_FPDFLink_GetAction"]=createExportWrapper("FPDFLink_GetAction");var _FPDFLink_Enumerate=Module["_FPDFLink_Enumerate"]=createExportWrapper("FPDFLink_Enumerate");var _FPDFLink_GetAnnot=Module["_FPDFLink_GetAnnot"]=createExportWrapper("FPDFLink_GetAnnot");var _FPDFLink_GetAnnotRect=Module["_FPDFLink_GetAnnotRect"]=createExportWrapper("FPDFLink_GetAnnotRect");var _FPDFLink_CountQuadPoints=Module["_FPDFLink_CountQuadPoints"]=createExportWrapper("FPDFLink_CountQuadPoints");var _FPDFLink_GetQuadPoints=Module["_FPDFLink_GetQuadPoints"]=createExportWrapper("FPDFLink_GetQuadPoints");var _FPDF_GetPageAAction=Module["_FPDF_GetPageAAction"]=createExportWrapper("FPDF_GetPageAAction");var _FPDF_GetFileIdentifier=Module["_FPDF_GetFileIdentifier"]=createExportWrapper("FPDF_GetFileIdentifier");var _FPDF_GetMetaText=Module["_FPDF_GetMetaText"]=createExportWrapper("FPDF_GetMetaText");var _FPDF_GetPageLabel=Module["_FPDF_GetPageLabel"]=createExportWrapper("FPDF_GetPageLabel");var _FPDFPageObj_NewImageObj=Module["_FPDFPageObj_NewImageObj"]=createExportWrapper("FPDFPageObj_NewImageObj");var _FPDFImageObj_LoadJpegFile=Module["_FPDFImageObj_LoadJpegFile"]=createExportWrapper("FPDFImageObj_LoadJpegFile");var _FPDFImageObj_LoadJpegFileInline=Module["_FPDFImageObj_LoadJpegFileInline"]=createExportWrapper("FPDFImageObj_LoadJpegFileInline");var _FPDFImageObj_SetMatrix=Module["_FPDFImageObj_SetMatrix"]=createExportWrapper("FPDFImageObj_SetMatrix");var _FPDFImageObj_SetBitmap=Module["_FPDFImageObj_SetBitmap"]=createExportWrapper("FPDFImageObj_SetBitmap");var _FPDFImageObj_GetBitmap=Module["_FPDFImageObj_GetBitmap"]=createExportWrapper("FPDFImageObj_GetBitmap");var _FPDFImageObj_GetRenderedBitmap=Module["_FPDFImageObj_GetRenderedBitmap"]=createExportWrapper("FPDFImageObj_GetRenderedBitmap");var _FPDFImageObj_GetImageDataDecoded=Module["_FPDFImageObj_GetImageDataDecoded"]=createExportWrapper("FPDFImageObj_GetImageDataDecoded");var _FPDFImageObj_GetImageDataRaw=Module["_FPDFImageObj_GetImageDataRaw"]=createExportWrapper("FPDFImageObj_GetImageDataRaw");var _FPDFImageObj_GetImageFilterCount=Module["_FPDFImageObj_GetImageFilterCount"]=createExportWrapper("FPDFImageObj_GetImageFilterCount");var _FPDFImageObj_GetImageFilter=Module["_FPDFImageObj_GetImageFilter"]=createExportWrapper("FPDFImageObj_GetImageFilter");var _FPDFImageObj_GetImageMetadata=Module["_FPDFImageObj_GetImageMetadata"]=createExportWrapper("FPDFImageObj_GetImageMetadata");var _FPDF_CreateNewDocument=Module["_FPDF_CreateNewDocument"]=createExportWrapper("FPDF_CreateNewDocument");var _FPDFPage_Delete=Module["_FPDFPage_Delete"]=createExportWrapper("FPDFPage_Delete");var _FPDFPage_New=Module["_FPDFPage_New"]=createExportWrapper("FPDFPage_New");var _FPDFPage_GetRotation=Module["_FPDFPage_GetRotation"]=createExportWrapper("FPDFPage_GetRotation");var _FPDFPage_InsertObject=Module["_FPDFPage_InsertObject"]=createExportWrapper("FPDFPage_InsertObject");var _FPDFPage_InsertObjectBottom=Module["_FPDFPage_InsertObjectBottom"]=createExportWrapper("FPDFPage_InsertObjectBottom");var _FPDFPage_RemoveObject=Module["_FPDFPage_RemoveObject"]=createExportWrapper("FPDFPage_RemoveObject");var _FPDFPage_CountObjects=Module["_FPDFPage_CountObjects"]=createExportWrapper("FPDFPage_CountObjects");var _FPDFPage_GetObject=Module["_FPDFPage_GetObject"]=createExportWrapper("FPDFPage_GetObject");var _FPDFPage_HasTransparency=Module["_FPDFPage_HasTransparency"]=createExportWrapper("FPDFPage_HasTransparency");var _FPDFPageObj_Destroy=Module["_FPDFPageObj_Destroy"]=createExportWrapper("FPDFPageObj_Destroy");var _FPDFPageObj_CountMarks=Module["_FPDFPageObj_CountMarks"]=createExportWrapper("FPDFPageObj_CountMarks");var _FPDFPageObj_GetMark=Module["_FPDFPageObj_GetMark"]=createExportWrapper("FPDFPageObj_GetMark");var _FPDFPageObj_AddMark=Module["_FPDFPageObj_AddMark"]=createExportWrapper("FPDFPageObj_AddMark");var _FPDFPageObj_RemoveMark=Module["_FPDFPageObj_RemoveMark"]=createExportWrapper("FPDFPageObj_RemoveMark");var _FPDFPageObjMark_GetName=Module["_FPDFPageObjMark_GetName"]=createExportWrapper("FPDFPageObjMark_GetName");var _FPDFPageObjMark_CountParams=Module["_FPDFPageObjMark_CountParams"]=createExportWrapper("FPDFPageObjMark_CountParams");var _FPDFPageObjMark_GetParamKey=Module["_FPDFPageObjMark_GetParamKey"]=createExportWrapper("FPDFPageObjMark_GetParamKey");var _FPDFPageObjMark_GetParamValueType=Module["_FPDFPageObjMark_GetParamValueType"]=createExportWrapper("FPDFPageObjMark_GetParamValueType");var _FPDFPageObjMark_GetParamIntValue=Module["_FPDFPageObjMark_GetParamIntValue"]=createExportWrapper("FPDFPageObjMark_GetParamIntValue");var _FPDFPageObjMark_GetParamStringValue=Module["_FPDFPageObjMark_GetParamStringValue"]=createExportWrapper("FPDFPageObjMark_GetParamStringValue");var _FPDFPageObjMark_GetParamBlobValue=Module["_FPDFPageObjMark_GetParamBlobValue"]=createExportWrapper("FPDFPageObjMark_GetParamBlobValue");var _FPDFPageObj_HasTransparency=Module["_FPDFPageObj_HasTransparency"]=createExportWrapper("FPDFPageObj_HasTransparency");var _FPDFPageObjMark_SetIntParam=Module["_FPDFPageObjMark_SetIntParam"]=createExportWrapper("FPDFPageObjMark_SetIntParam");var _FPDFPageObjMark_SetStringParam=Module["_FPDFPageObjMark_SetStringParam"]=createExportWrapper("FPDFPageObjMark_SetStringParam");var _FPDFPageObjMark_SetBlobParam=Module["_FPDFPageObjMark_SetBlobParam"]=createExportWrapper("FPDFPageObjMark_SetBlobParam");var _FPDFPageObjMark_RemoveParam=Module["_FPDFPageObjMark_RemoveParam"]=createExportWrapper("FPDFPageObjMark_RemoveParam");var _FPDFPageObj_GetType=Module["_FPDFPageObj_GetType"]=createExportWrapper("FPDFPageObj_GetType");var _FPDFPage_GenerateContent=Module["_FPDFPage_GenerateContent"]=createExportWrapper("FPDFPage_GenerateContent");var _FPDFPageObj_Transform=Module["_FPDFPageObj_Transform"]=createExportWrapper("FPDFPageObj_Transform");var _FPDFPageObj_GetMatrix=Module["_FPDFPageObj_GetMatrix"]=createExportWrapper("FPDFPageObj_GetMatrix");var _FPDFPageObj_SetMatrix=Module["_FPDFPageObj_SetMatrix"]=createExportWrapper("FPDFPageObj_SetMatrix");var _FPDFPageObj_SetBlendMode=Module["_FPDFPageObj_SetBlendMode"]=createExportWrapper("FPDFPageObj_SetBlendMode");var _FPDFPage_TransformAnnots=Module["_FPDFPage_TransformAnnots"]=createExportWrapper("FPDFPage_TransformAnnots");var _FPDFPage_SetRotation=Module["_FPDFPage_SetRotation"]=createExportWrapper("FPDFPage_SetRotation");var _FPDFPageObj_SetFillColor=Module["_FPDFPageObj_SetFillColor"]=createExportWrapper("FPDFPageObj_SetFillColor");var _FPDFPageObj_GetFillColor=Module["_FPDFPageObj_GetFillColor"]=createExportWrapper("FPDFPageObj_GetFillColor");var _FPDFPageObj_GetBounds=Module["_FPDFPageObj_GetBounds"]=createExportWrapper("FPDFPageObj_GetBounds");var _FPDFPageObj_GetRotatedBounds=Module["_FPDFPageObj_GetRotatedBounds"]=createExportWrapper("FPDFPageObj_GetRotatedBounds");var _FPDFPageObj_SetStrokeColor=Module["_FPDFPageObj_SetStrokeColor"]=createExportWrapper("FPDFPageObj_SetStrokeColor");var _FPDFPageObj_GetStrokeColor=Module["_FPDFPageObj_GetStrokeColor"]=createExportWrapper("FPDFPageObj_GetStrokeColor");var _FPDFPageObj_SetStrokeWidth=Module["_FPDFPageObj_SetStrokeWidth"]=createExportWrapper("FPDFPageObj_SetStrokeWidth");var _FPDFPageObj_GetStrokeWidth=Module["_FPDFPageObj_GetStrokeWidth"]=createExportWrapper("FPDFPageObj_GetStrokeWidth");var _FPDFPageObj_GetLineJoin=Module["_FPDFPageObj_GetLineJoin"]=createExportWrapper("FPDFPageObj_GetLineJoin");var _FPDFPageObj_SetLineJoin=Module["_FPDFPageObj_SetLineJoin"]=createExportWrapper("FPDFPageObj_SetLineJoin");var _FPDFPageObj_GetLineCap=Module["_FPDFPageObj_GetLineCap"]=createExportWrapper("FPDFPageObj_GetLineCap");var _FPDFPageObj_SetLineCap=Module["_FPDFPageObj_SetLineCap"]=createExportWrapper("FPDFPageObj_SetLineCap");var _FPDFPageObj_GetDashPhase=Module["_FPDFPageObj_GetDashPhase"]=createExportWrapper("FPDFPageObj_GetDashPhase");var _FPDFPageObj_SetDashPhase=Module["_FPDFPageObj_SetDashPhase"]=createExportWrapper("FPDFPageObj_SetDashPhase");var _FPDFPageObj_GetDashCount=Module["_FPDFPageObj_GetDashCount"]=createExportWrapper("FPDFPageObj_GetDashCount");var _FPDFPageObj_GetDashArray=Module["_FPDFPageObj_GetDashArray"]=createExportWrapper("FPDFPageObj_GetDashArray");var _FPDFPageObj_SetDashArray=Module["_FPDFPageObj_SetDashArray"]=createExportWrapper("FPDFPageObj_SetDashArray");var _FPDFFormObj_CountObjects=Module["_FPDFFormObj_CountObjects"]=createExportWrapper("FPDFFormObj_CountObjects");var _FPDFFormObj_GetObject=Module["_FPDFFormObj_GetObject"]=createExportWrapper("FPDFFormObj_GetObject");var _FPDFPageObj_CreateNewPath=Module["_FPDFPageObj_CreateNewPath"]=createExportWrapper("FPDFPageObj_CreateNewPath");var _FPDFPageObj_CreateNewRect=Module["_FPDFPageObj_CreateNewRect"]=createExportWrapper("FPDFPageObj_CreateNewRect");var _FPDFPath_CountSegments=Module["_FPDFPath_CountSegments"]=createExportWrapper("FPDFPath_CountSegments");var _FPDFPath_GetPathSegment=Module["_FPDFPath_GetPathSegment"]=createExportWrapper("FPDFPath_GetPathSegment");var _FPDFPath_MoveTo=Module["_FPDFPath_MoveTo"]=createExportWrapper("FPDFPath_MoveTo");var _FPDFPath_LineTo=Module["_FPDFPath_LineTo"]=createExportWrapper("FPDFPath_LineTo");var _FPDFPath_BezierTo=Module["_FPDFPath_BezierTo"]=createExportWrapper("FPDFPath_BezierTo");var _FPDFPath_Close=Module["_FPDFPath_Close"]=createExportWrapper("FPDFPath_Close");var _FPDFPath_SetDrawMode=Module["_FPDFPath_SetDrawMode"]=createExportWrapper("FPDFPath_SetDrawMode");var _FPDFPath_GetDrawMode=Module["_FPDFPath_GetDrawMode"]=createExportWrapper("FPDFPath_GetDrawMode");var _FPDFPathSegment_GetPoint=Module["_FPDFPathSegment_GetPoint"]=createExportWrapper("FPDFPathSegment_GetPoint");var _FPDFPathSegment_GetType=Module["_FPDFPathSegment_GetType"]=createExportWrapper("FPDFPathSegment_GetType");var _FPDFPathSegment_GetClose=Module["_FPDFPathSegment_GetClose"]=createExportWrapper("FPDFPathSegment_GetClose");var _FPDFPageObj_NewTextObj=Module["_FPDFPageObj_NewTextObj"]=createExportWrapper("FPDFPageObj_NewTextObj");var _FPDFText_SetText=Module["_FPDFText_SetText"]=createExportWrapper("FPDFText_SetText");var _FPDFText_SetCharcodes=Module["_FPDFText_SetCharcodes"]=createExportWrapper("FPDFText_SetCharcodes");var _FPDFText_LoadFont=Module["_FPDFText_LoadFont"]=createExportWrapper("FPDFText_LoadFont");var _FPDFText_LoadStandardFont=Module["_FPDFText_LoadStandardFont"]=createExportWrapper("FPDFText_LoadStandardFont");var _FPDFTextObj_GetFontSize=Module["_FPDFTextObj_GetFontSize"]=createExportWrapper("FPDFTextObj_GetFontSize");var _FPDFTextObj_GetText=Module["_FPDFTextObj_GetText"]=createExportWrapper("FPDFTextObj_GetText");var _FPDFTextObj_GetRenderedBitmap=Module["_FPDFTextObj_GetRenderedBitmap"]=createExportWrapper("FPDFTextObj_GetRenderedBitmap");var _FPDFFont_Close=Module["_FPDFFont_Close"]=createExportWrapper("FPDFFont_Close");var _FPDFPageObj_CreateTextObj=Module["_FPDFPageObj_CreateTextObj"]=createExportWrapper("FPDFPageObj_CreateTextObj");var _FPDFTextObj_GetTextRenderMode=Module["_FPDFTextObj_GetTextRenderMode"]=createExportWrapper("FPDFTextObj_GetTextRenderMode");var _FPDFTextObj_SetTextRenderMode=Module["_FPDFTextObj_SetTextRenderMode"]=createExportWrapper("FPDFTextObj_SetTextRenderMode");var _FPDFTextObj_GetFont=Module["_FPDFTextObj_GetFont"]=createExportWrapper("FPDFTextObj_GetFont");var _FPDFFont_GetFontName=Module["_FPDFFont_GetFontName"]=createExportWrapper("FPDFFont_GetFontName");var _FPDFFont_GetFontData=Module["_FPDFFont_GetFontData"]=createExportWrapper("FPDFFont_GetFontData");var _FPDFFont_GetIsEmbedded=Module["_FPDFFont_GetIsEmbedded"]=createExportWrapper("FPDFFont_GetIsEmbedded");var _FPDFFont_GetFlags=Module["_FPDFFont_GetFlags"]=createExportWrapper("FPDFFont_GetFlags");var _FPDFFont_GetWeight=Module["_FPDFFont_GetWeight"]=createExportWrapper("FPDFFont_GetWeight");var _FPDFFont_GetItalicAngle=Module["_FPDFFont_GetItalicAngle"]=createExportWrapper("FPDFFont_GetItalicAngle");var _FPDFFont_GetAscent=Module["_FPDFFont_GetAscent"]=createExportWrapper("FPDFFont_GetAscent");var _FPDFFont_GetDescent=Module["_FPDFFont_GetDescent"]=createExportWrapper("FPDFFont_GetDescent");var _FPDFFont_GetGlyphWidth=Module["_FPDFFont_GetGlyphWidth"]=createExportWrapper("FPDFFont_GetGlyphWidth");var _FPDFFont_GetGlyphPath=Module["_FPDFFont_GetGlyphPath"]=createExportWrapper("FPDFFont_GetGlyphPath");var _FPDFGlyphPath_CountGlyphSegments=Module["_FPDFGlyphPath_CountGlyphSegments"]=createExportWrapper("FPDFGlyphPath_CountGlyphSegments");var _FPDFGlyphPath_GetGlyphPathSegment=Module["_FPDFGlyphPath_GetGlyphPathSegment"]=createExportWrapper("FPDFGlyphPath_GetGlyphPathSegment");var _FSDK_SetUnSpObjProcessHandler=Module["_FSDK_SetUnSpObjProcessHandler"]=createExportWrapper("FSDK_SetUnSpObjProcessHandler");var _FSDK_SetTimeFunction=Module["_FSDK_SetTimeFunction"]=createExportWrapper("FSDK_SetTimeFunction");var _FSDK_SetLocaltimeFunction=Module["_FSDK_SetLocaltimeFunction"]=createExportWrapper("FSDK_SetLocaltimeFunction");var _FPDFDoc_GetPageMode=Module["_FPDFDoc_GetPageMode"]=createExportWrapper("FPDFDoc_GetPageMode");var _FPDFPage_Flatten=Module["_FPDFPage_Flatten"]=createExportWrapper("FPDFPage_Flatten");var _FPDFPage_HasFormFieldAtPoint=Module["_FPDFPage_HasFormFieldAtPoint"]=createExportWrapper("FPDFPage_HasFormFieldAtPoint");var _FPDFPage_FormFieldZOrderAtPoint=Module["_FPDFPage_FormFieldZOrderAtPoint"]=createExportWrapper("FPDFPage_FormFieldZOrderAtPoint");var _FPDFDOC_InitFormFillEnvironment=Module["_FPDFDOC_InitFormFillEnvironment"]=createExportWrapper("FPDFDOC_InitFormFillEnvironment");var _FPDFDOC_ExitFormFillEnvironment=Module["_FPDFDOC_ExitFormFillEnvironment"]=createExportWrapper("FPDFDOC_ExitFormFillEnvironment");var _FORM_OnMouseMove=Module["_FORM_OnMouseMove"]=createExportWrapper("FORM_OnMouseMove");var _FORM_OnMouseWheel=Module["_FORM_OnMouseWheel"]=createExportWrapper("FORM_OnMouseWheel");var _FORM_OnFocus=Module["_FORM_OnFocus"]=createExportWrapper("FORM_OnFocus");var _FORM_OnLButtonDown=Module["_FORM_OnLButtonDown"]=createExportWrapper("FORM_OnLButtonDown");var _FORM_OnLButtonUp=Module["_FORM_OnLButtonUp"]=createExportWrapper("FORM_OnLButtonUp");var _FORM_OnLButtonDoubleClick=Module["_FORM_OnLButtonDoubleClick"]=createExportWrapper("FORM_OnLButtonDoubleClick");var _FORM_OnRButtonDown=Module["_FORM_OnRButtonDown"]=createExportWrapper("FORM_OnRButtonDown");var _FORM_OnRButtonUp=Module["_FORM_OnRButtonUp"]=createExportWrapper("FORM_OnRButtonUp");var _FORM_OnKeyDown=Module["_FORM_OnKeyDown"]=createExportWrapper("FORM_OnKeyDown");var _FORM_OnKeyUp=Module["_FORM_OnKeyUp"]=createExportWrapper("FORM_OnKeyUp");var _FORM_OnChar=Module["_FORM_OnChar"]=createExportWrapper("FORM_OnChar");var _FORM_GetFocusedText=Module["_FORM_GetFocusedText"]=createExportWrapper("FORM_GetFocusedText");var _FORM_GetSelectedText=Module["_FORM_GetSelectedText"]=createExportWrapper("FORM_GetSelectedText");var _FORM_ReplaceAndKeepSelection=Module["_FORM_ReplaceAndKeepSelection"]=createExportWrapper("FORM_ReplaceAndKeepSelection");var _FORM_ReplaceSelection=Module["_FORM_ReplaceSelection"]=createExportWrapper("FORM_ReplaceSelection");var _FORM_SelectAllText=Module["_FORM_SelectAllText"]=createExportWrapper("FORM_SelectAllText");var _FORM_CanUndo=Module["_FORM_CanUndo"]=createExportWrapper("FORM_CanUndo");var _FORM_CanRedo=Module["_FORM_CanRedo"]=createExportWrapper("FORM_CanRedo");var _FORM_Undo=Module["_FORM_Undo"]=createExportWrapper("FORM_Undo");var _FORM_Redo=Module["_FORM_Redo"]=createExportWrapper("FORM_Redo");var _FORM_ForceToKillFocus=Module["_FORM_ForceToKillFocus"]=createExportWrapper("FORM_ForceToKillFocus");var _FORM_GetFocusedAnnot=Module["_FORM_GetFocusedAnnot"]=createExportWrapper("FORM_GetFocusedAnnot");var _FORM_SetFocusedAnnot=Module["_FORM_SetFocusedAnnot"]=createExportWrapper("FORM_SetFocusedAnnot");var _FPDF_FFLDraw=Module["_FPDF_FFLDraw"]=createExportWrapper("FPDF_FFLDraw");var _FPDF_SetFormFieldHighlightColor=Module["_FPDF_SetFormFieldHighlightColor"]=createExportWrapper("FPDF_SetFormFieldHighlightColor");var _FPDF_SetFormFieldHighlightAlpha=Module["_FPDF_SetFormFieldHighlightAlpha"]=createExportWrapper("FPDF_SetFormFieldHighlightAlpha");var _FPDF_RemoveFormFieldHighlight=Module["_FPDF_RemoveFormFieldHighlight"]=createExportWrapper("FPDF_RemoveFormFieldHighlight");var _FORM_OnAfterLoadPage=Module["_FORM_OnAfterLoadPage"]=createExportWrapper("FORM_OnAfterLoadPage");var _FORM_OnBeforeClosePage=Module["_FORM_OnBeforeClosePage"]=createExportWrapper("FORM_OnBeforeClosePage");var _FORM_DoDocumentJSAction=Module["_FORM_DoDocumentJSAction"]=createExportWrapper("FORM_DoDocumentJSAction");var _FORM_DoDocumentOpenAction=Module["_FORM_DoDocumentOpenAction"]=createExportWrapper("FORM_DoDocumentOpenAction");var _FORM_DoDocumentAAction=Module["_FORM_DoDocumentAAction"]=createExportWrapper("FORM_DoDocumentAAction");var _FORM_DoPageAAction=Module["_FORM_DoPageAAction"]=createExportWrapper("FORM_DoPageAAction");var _FORM_SetIndexSelected=Module["_FORM_SetIndexSelected"]=createExportWrapper("FORM_SetIndexSelected");var _FORM_IsIndexSelected=Module["_FORM_IsIndexSelected"]=createExportWrapper("FORM_IsIndexSelected");var _FPDFDoc_GetJavaScriptActionCount=Module["_FPDFDoc_GetJavaScriptActionCount"]=createExportWrapper("FPDFDoc_GetJavaScriptActionCount");var _FPDFDoc_GetJavaScriptAction=Module["_FPDFDoc_GetJavaScriptAction"]=createExportWrapper("FPDFDoc_GetJavaScriptAction");var _FPDFDoc_CloseJavaScriptAction=Module["_FPDFDoc_CloseJavaScriptAction"]=createExportWrapper("FPDFDoc_CloseJavaScriptAction");var _FPDFJavaScriptAction_GetName=Module["_FPDFJavaScriptAction_GetName"]=createExportWrapper("FPDFJavaScriptAction_GetName");var _FPDFJavaScriptAction_GetScript=Module["_FPDFJavaScriptAction_GetScript"]=createExportWrapper("FPDFJavaScriptAction_GetScript");var _FPDF_ImportPagesByIndex=Module["_FPDF_ImportPagesByIndex"]=createExportWrapper("FPDF_ImportPagesByIndex");var _FPDF_ImportPages=Module["_FPDF_ImportPages"]=createExportWrapper("FPDF_ImportPages");var _FPDF_ImportNPagesToOne=Module["_FPDF_ImportNPagesToOne"]=createExportWrapper("FPDF_ImportNPagesToOne");var _FPDF_NewXObjectFromPage=Module["_FPDF_NewXObjectFromPage"]=createExportWrapper("FPDF_NewXObjectFromPage");var _FPDF_CloseXObject=Module["_FPDF_CloseXObject"]=createExportWrapper("FPDF_CloseXObject");var _FPDF_NewFormObjectFromXObject=Module["_FPDF_NewFormObjectFromXObject"]=createExportWrapper("FPDF_NewFormObjectFromXObject");var _FPDF_CopyViewerPreferences=Module["_FPDF_CopyViewerPreferences"]=createExportWrapper("FPDF_CopyViewerPreferences");var _FPDF_RenderPageBitmapWithColorScheme_Start=Module["_FPDF_RenderPageBitmapWithColorScheme_Start"]=createExportWrapper("FPDF_RenderPageBitmapWithColorScheme_Start");var _FPDF_RenderPageBitmap_Start=Module["_FPDF_RenderPageBitmap_Start"]=createExportWrapper("FPDF_RenderPageBitmap_Start");var _FPDF_RenderPage_Continue=Module["_FPDF_RenderPage_Continue"]=createExportWrapper("FPDF_RenderPage_Continue");var _FPDF_RenderPage_Close=Module["_FPDF_RenderPage_Close"]=createExportWrapper("FPDF_RenderPage_Close");var _FPDF_SaveAsCopy=Module["_FPDF_SaveAsCopy"]=createExportWrapper("FPDF_SaveAsCopy");var _FPDF_SaveWithVersion=Module["_FPDF_SaveWithVersion"]=createExportWrapper("FPDF_SaveWithVersion");var _FPDFText_GetCharIndexFromTextIndex=Module["_FPDFText_GetCharIndexFromTextIndex"]=createExportWrapper("FPDFText_GetCharIndexFromTextIndex");var _FPDFText_GetTextIndexFromCharIndex=Module["_FPDFText_GetTextIndexFromCharIndex"]=createExportWrapper("FPDFText_GetTextIndexFromCharIndex");var _FPDF_GetSignatureCount=Module["_FPDF_GetSignatureCount"]=createExportWrapper("FPDF_GetSignatureCount");var _FPDF_GetSignatureObject=Module["_FPDF_GetSignatureObject"]=createExportWrapper("FPDF_GetSignatureObject");var _FPDFSignatureObj_GetContents=Module["_FPDFSignatureObj_GetContents"]=createExportWrapper("FPDFSignatureObj_GetContents");var _FPDFSignatureObj_GetByteRange=Module["_FPDFSignatureObj_GetByteRange"]=createExportWrapper("FPDFSignatureObj_GetByteRange");var _FPDFSignatureObj_GetSubFilter=Module["_FPDFSignatureObj_GetSubFilter"]=createExportWrapper("FPDFSignatureObj_GetSubFilter");var _FPDFSignatureObj_GetReason=Module["_FPDFSignatureObj_GetReason"]=createExportWrapper("FPDFSignatureObj_GetReason");var _FPDFSignatureObj_GetTime=Module["_FPDFSignatureObj_GetTime"]=createExportWrapper("FPDFSignatureObj_GetTime");var _FPDFSignatureObj_GetDocMDPPermission=Module["_FPDFSignatureObj_GetDocMDPPermission"]=createExportWrapper("FPDFSignatureObj_GetDocMDPPermission");var _FPDF_StructTree_GetForPage=Module["_FPDF_StructTree_GetForPage"]=createExportWrapper("FPDF_StructTree_GetForPage");var _FPDF_StructTree_Close=Module["_FPDF_StructTree_Close"]=createExportWrapper("FPDF_StructTree_Close");var _FPDF_StructTree_CountChildren=Module["_FPDF_StructTree_CountChildren"]=createExportWrapper("FPDF_StructTree_CountChildren");var _FPDF_StructTree_GetChildAtIndex=Module["_FPDF_StructTree_GetChildAtIndex"]=createExportWrapper("FPDF_StructTree_GetChildAtIndex");var _FPDF_StructElement_GetAltText=Module["_FPDF_StructElement_GetAltText"]=createExportWrapper("FPDF_StructElement_GetAltText");var _FPDF_StructElement_GetActualText=Module["_FPDF_StructElement_GetActualText"]=createExportWrapper("FPDF_StructElement_GetActualText");var _FPDF_StructElement_GetID=Module["_FPDF_StructElement_GetID"]=createExportWrapper("FPDF_StructElement_GetID");var _FPDF_StructElement_GetLang=Module["_FPDF_StructElement_GetLang"]=createExportWrapper("FPDF_StructElement_GetLang");var _FPDF_StructElement_GetAttributeCount=Module["_FPDF_StructElement_GetAttributeCount"]=createExportWrapper("FPDF_StructElement_GetAttributeCount");var _FPDF_StructElement_GetAttributeAtIndex=Module["_FPDF_StructElement_GetAttributeAtIndex"]=createExportWrapper("FPDF_StructElement_GetAttributeAtIndex");var _FPDF_StructElement_GetStringAttribute=Module["_FPDF_StructElement_GetStringAttribute"]=createExportWrapper("FPDF_StructElement_GetStringAttribute");var _FPDF_StructElement_GetMarkedContentID=Module["_FPDF_StructElement_GetMarkedContentID"]=createExportWrapper("FPDF_StructElement_GetMarkedContentID");var _FPDF_StructElement_GetType=Module["_FPDF_StructElement_GetType"]=createExportWrapper("FPDF_StructElement_GetType");var _FPDF_StructElement_GetObjType=Module["_FPDF_StructElement_GetObjType"]=createExportWrapper("FPDF_StructElement_GetObjType");var _FPDF_StructElement_GetTitle=Module["_FPDF_StructElement_GetTitle"]=createExportWrapper("FPDF_StructElement_GetTitle");var _FPDF_StructElement_CountChildren=Module["_FPDF_StructElement_CountChildren"]=createExportWrapper("FPDF_StructElement_CountChildren");var _FPDF_StructElement_GetChildAtIndex=Module["_FPDF_StructElement_GetChildAtIndex"]=createExportWrapper("FPDF_StructElement_GetChildAtIndex");var _FPDF_StructElement_GetParent=Module["_FPDF_StructElement_GetParent"]=createExportWrapper("FPDF_StructElement_GetParent");var _FPDF_StructElement_Attr_GetCount=Module["_FPDF_StructElement_Attr_GetCount"]=createExportWrapper("FPDF_StructElement_Attr_GetCount");var _FPDF_StructElement_Attr_GetName=Module["_FPDF_StructElement_Attr_GetName"]=createExportWrapper("FPDF_StructElement_Attr_GetName");var _FPDF_StructElement_Attr_GetType=Module["_FPDF_StructElement_Attr_GetType"]=createExportWrapper("FPDF_StructElement_Attr_GetType");var _FPDF_StructElement_Attr_GetBooleanValue=Module["_FPDF_StructElement_Attr_GetBooleanValue"]=createExportWrapper("FPDF_StructElement_Attr_GetBooleanValue");var _FPDF_StructElement_Attr_GetNumberValue=Module["_FPDF_StructElement_Attr_GetNumberValue"]=createExportWrapper("FPDF_StructElement_Attr_GetNumberValue");var _FPDF_StructElement_Attr_GetStringValue=Module["_FPDF_StructElement_Attr_GetStringValue"]=createExportWrapper("FPDF_StructElement_Attr_GetStringValue");var _FPDF_StructElement_Attr_GetBlobValue=Module["_FPDF_StructElement_Attr_GetBlobValue"]=createExportWrapper("FPDF_StructElement_Attr_GetBlobValue");var _FPDF_StructElement_GetMarkedContentIdCount=Module["_FPDF_StructElement_GetMarkedContentIdCount"]=createExportWrapper("FPDF_StructElement_GetMarkedContentIdCount");var _FPDF_StructElement_GetMarkedContentIdAtIndex=Module["_FPDF_StructElement_GetMarkedContentIdAtIndex"]=createExportWrapper("FPDF_StructElement_GetMarkedContentIdAtIndex");var _FPDF_AddInstalledFont=Module["_FPDF_AddInstalledFont"]=createExportWrapper("FPDF_AddInstalledFont");var _FPDF_SetSystemFontInfo=Module["_FPDF_SetSystemFontInfo"]=createExportWrapper("FPDF_SetSystemFontInfo");var _FPDF_GetDefaultTTFMap=Module["_FPDF_GetDefaultTTFMap"]=createExportWrapper("FPDF_GetDefaultTTFMap");var _FPDF_GetDefaultSystemFontInfo=Module["_FPDF_GetDefaultSystemFontInfo"]=createExportWrapper("FPDF_GetDefaultSystemFontInfo");var _FPDF_FreeDefaultSystemFontInfo=Module["_FPDF_FreeDefaultSystemFontInfo"]=createExportWrapper("FPDF_FreeDefaultSystemFontInfo");var _FPDFText_LoadPage=Module["_FPDFText_LoadPage"]=createExportWrapper("FPDFText_LoadPage");var _FPDFText_ClosePage=Module["_FPDFText_ClosePage"]=createExportWrapper("FPDFText_ClosePage");var _FPDFText_CountChars=Module["_FPDFText_CountChars"]=createExportWrapper("FPDFText_CountChars");var _FPDFText_GetUnicode=Module["_FPDFText_GetUnicode"]=createExportWrapper("FPDFText_GetUnicode");var _FPDFText_IsGenerated=Module["_FPDFText_IsGenerated"]=createExportWrapper("FPDFText_IsGenerated");var _FPDFText_HasUnicodeMapError=Module["_FPDFText_HasUnicodeMapError"]=createExportWrapper("FPDFText_HasUnicodeMapError");var _FPDFText_GetFontSize=Module["_FPDFText_GetFontSize"]=createExportWrapper("FPDFText_GetFontSize");var _FPDFText_GetFontInfo=Module["_FPDFText_GetFontInfo"]=createExportWrapper("FPDFText_GetFontInfo");var _FPDFText_GetFontWeight=Module["_FPDFText_GetFontWeight"]=createExportWrapper("FPDFText_GetFontWeight");var _FPDFText_GetTextRenderMode=Module["_FPDFText_GetTextRenderMode"]=createExportWrapper("FPDFText_GetTextRenderMode");var _FPDFText_GetFillColor=Module["_FPDFText_GetFillColor"]=createExportWrapper("FPDFText_GetFillColor");var _FPDFText_GetStrokeColor=Module["_FPDFText_GetStrokeColor"]=createExportWrapper("FPDFText_GetStrokeColor");var _FPDFText_GetCharAngle=Module["_FPDFText_GetCharAngle"]=createExportWrapper("FPDFText_GetCharAngle");var _FPDFText_GetCharBox=Module["_FPDFText_GetCharBox"]=createExportWrapper("FPDFText_GetCharBox");var _FPDFText_GetLooseCharBox=Module["_FPDFText_GetLooseCharBox"]=createExportWrapper("FPDFText_GetLooseCharBox");var _FPDFText_GetMatrix=Module["_FPDFText_GetMatrix"]=createExportWrapper("FPDFText_GetMatrix");var _FPDFText_GetCharOrigin=Module["_FPDFText_GetCharOrigin"]=createExportWrapper("FPDFText_GetCharOrigin");var _FPDFText_GetCharIndexAtPos=Module["_FPDFText_GetCharIndexAtPos"]=createExportWrapper("FPDFText_GetCharIndexAtPos");var _FPDFText_GetText=Module["_FPDFText_GetText"]=createExportWrapper("FPDFText_GetText");var _FPDFText_CountRects=Module["_FPDFText_CountRects"]=createExportWrapper("FPDFText_CountRects");var _FPDFText_GetRect=Module["_FPDFText_GetRect"]=createExportWrapper("FPDFText_GetRect");var _FPDFText_GetBoundedText=Module["_FPDFText_GetBoundedText"]=createExportWrapper("FPDFText_GetBoundedText");var _FPDFText_FindStart=Module["_FPDFText_FindStart"]=createExportWrapper("FPDFText_FindStart");var _FPDFText_FindNext=Module["_FPDFText_FindNext"]=createExportWrapper("FPDFText_FindNext");var _FPDFText_FindPrev=Module["_FPDFText_FindPrev"]=createExportWrapper("FPDFText_FindPrev");var _FPDFText_GetSchResultIndex=Module["_FPDFText_GetSchResultIndex"]=createExportWrapper("FPDFText_GetSchResultIndex");var _FPDFText_GetSchCount=Module["_FPDFText_GetSchCount"]=createExportWrapper("FPDFText_GetSchCount");var _FPDFText_FindClose=Module["_FPDFText_FindClose"]=createExportWrapper("FPDFText_FindClose");var _FPDFLink_LoadWebLinks=Module["_FPDFLink_LoadWebLinks"]=createExportWrapper("FPDFLink_LoadWebLinks");var _FPDFLink_CountWebLinks=Module["_FPDFLink_CountWebLinks"]=createExportWrapper("FPDFLink_CountWebLinks");var _FPDFLink_GetURL=Module["_FPDFLink_GetURL"]=createExportWrapper("FPDFLink_GetURL");var _FPDFLink_CountRects=Module["_FPDFLink_CountRects"]=createExportWrapper("FPDFLink_CountRects");var _FPDFLink_GetRect=Module["_FPDFLink_GetRect"]=createExportWrapper("FPDFLink_GetRect");var _FPDFLink_GetTextRange=Module["_FPDFLink_GetTextRange"]=createExportWrapper("FPDFLink_GetTextRange");var _FPDFLink_CloseWebLinks=Module["_FPDFLink_CloseWebLinks"]=createExportWrapper("FPDFLink_CloseWebLinks");var _FPDFPage_GetDecodedThumbnailData=Module["_FPDFPage_GetDecodedThumbnailData"]=createExportWrapper("FPDFPage_GetDecodedThumbnailData");var _FPDFPage_GetRawThumbnailData=Module["_FPDFPage_GetRawThumbnailData"]=createExportWrapper("FPDFPage_GetRawThumbnailData");var _FPDFPage_GetThumbnailAsBitmap=Module["_FPDFPage_GetThumbnailAsBitmap"]=createExportWrapper("FPDFPage_GetThumbnailAsBitmap");var _FPDFPage_SetMediaBox=Module["_FPDFPage_SetMediaBox"]=createExportWrapper("FPDFPage_SetMediaBox");var _FPDFPage_SetCropBox=Module["_FPDFPage_SetCropBox"]=createExportWrapper("FPDFPage_SetCropBox");var _FPDFPage_SetBleedBox=Module["_FPDFPage_SetBleedBox"]=createExportWrapper("FPDFPage_SetBleedBox");var _FPDFPage_SetTrimBox=Module["_FPDFPage_SetTrimBox"]=createExportWrapper("FPDFPage_SetTrimBox");var _FPDFPage_SetArtBox=Module["_FPDFPage_SetArtBox"]=createExportWrapper("FPDFPage_SetArtBox");var _FPDFPage_GetMediaBox=Module["_FPDFPage_GetMediaBox"]=createExportWrapper("FPDFPage_GetMediaBox");var _FPDFPage_GetCropBox=Module["_FPDFPage_GetCropBox"]=createExportWrapper("FPDFPage_GetCropBox");var _FPDFPage_GetBleedBox=Module["_FPDFPage_GetBleedBox"]=createExportWrapper("FPDFPage_GetBleedBox");var _FPDFPage_GetTrimBox=Module["_FPDFPage_GetTrimBox"]=createExportWrapper("FPDFPage_GetTrimBox");var _FPDFPage_GetArtBox=Module["_FPDFPage_GetArtBox"]=createExportWrapper("FPDFPage_GetArtBox");var _FPDFPage_TransFormWithClip=Module["_FPDFPage_TransFormWithClip"]=createExportWrapper("FPDFPage_TransFormWithClip");var _FPDFPageObj_TransformClipPath=Module["_FPDFPageObj_TransformClipPath"]=createExportWrapper("FPDFPageObj_TransformClipPath");var _FPDFPageObj_GetClipPath=Module["_FPDFPageObj_GetClipPath"]=createExportWrapper("FPDFPageObj_GetClipPath");var _FPDFClipPath_CountPaths=Module["_FPDFClipPath_CountPaths"]=createExportWrapper("FPDFClipPath_CountPaths");var _FPDFClipPath_CountPathSegments=Module["_FPDFClipPath_CountPathSegments"]=createExportWrapper("FPDFClipPath_CountPathSegments");var _FPDFClipPath_GetPathSegment=Module["_FPDFClipPath_GetPathSegment"]=createExportWrapper("FPDFClipPath_GetPathSegment");var _FPDF_CreateClipPath=Module["_FPDF_CreateClipPath"]=createExportWrapper("FPDF_CreateClipPath");var _FPDF_DestroyClipPath=Module["_FPDF_DestroyClipPath"]=createExportWrapper("FPDF_DestroyClipPath");var _FPDFPage_InsertClipPath=Module["_FPDFPage_InsertClipPath"]=createExportWrapper("FPDFPage_InsertClipPath");var _FPDF_InitLibrary=Module["_FPDF_InitLibrary"]=createExportWrapper("FPDF_InitLibrary");var _malloc=Module["_malloc"]=createExportWrapper("malloc");var _free=Module["_free"]=createExportWrapper("free");var setTempRet0=createExportWrapper("setTempRet0");var _FPDF_SetSandBoxPolicy=Module["_FPDF_SetSandBoxPolicy"]=createExportWrapper("FPDF_SetSandBoxPolicy");var _FPDF_GetFormType=Module["_FPDF_GetFormType"]=createExportWrapper("FPDF_GetFormType");var _FPDF_LoadXFA=Module["_FPDF_LoadXFA"]=createExportWrapper("FPDF_LoadXFA");var _FPDF_LoadMemDocument=Module["_FPDF_LoadMemDocument"]=createExportWrapper("FPDF_LoadMemDocument");var _FPDF_LoadMemDocument64=Module["_FPDF_LoadMemDocument64"]=createExportWrapper("FPDF_LoadMemDocument64");var _FPDF_LoadCustomDocument=Module["_FPDF_LoadCustomDocument"]=createExportWrapper("FPDF_LoadCustomDocument");var _FPDF_GetFileVersion=Module["_FPDF_GetFileVersion"]=createExportWrapper("FPDF_GetFileVersion");var _FPDF_DocumentHasValidCrossReferenceTable=Module["_FPDF_DocumentHasValidCrossReferenceTable"]=createExportWrapper("FPDF_DocumentHasValidCrossReferenceTable");var _FPDF_GetDocPermissions=Module["_FPDF_GetDocPermissions"]=createExportWrapper("FPDF_GetDocPermissions");var _FPDF_GetSecurityHandlerRevision=Module["_FPDF_GetSecurityHandlerRevision"]=createExportWrapper("FPDF_GetSecurityHandlerRevision");var _FPDF_LoadPage=Module["_FPDF_LoadPage"]=createExportWrapper("FPDF_LoadPage");var _FPDF_GetPageWidthF=Module["_FPDF_GetPageWidthF"]=createExportWrapper("FPDF_GetPageWidthF");var _FPDF_GetPageWidth=Module["_FPDF_GetPageWidth"]=createExportWrapper("FPDF_GetPageWidth");var _FPDF_GetPageHeightF=Module["_FPDF_GetPageHeightF"]=createExportWrapper("FPDF_GetPageHeightF");var _FPDF_GetPageHeight=Module["_FPDF_GetPageHeight"]=createExportWrapper("FPDF_GetPageHeight");var _FPDF_GetPageBoundingBox=Module["_FPDF_GetPageBoundingBox"]=createExportWrapper("FPDF_GetPageBoundingBox");var _FPDF_RenderPageBitmap=Module["_FPDF_RenderPageBitmap"]=createExportWrapper("FPDF_RenderPageBitmap");var _FPDF_RenderPageBitmapWithMatrix=Module["_FPDF_RenderPageBitmapWithMatrix"]=createExportWrapper("FPDF_RenderPageBitmapWithMatrix");var _FPDF_ClosePage=Module["_FPDF_ClosePage"]=createExportWrapper("FPDF_ClosePage");var _FPDF_DeviceToPage=Module["_FPDF_DeviceToPage"]=createExportWrapper("FPDF_DeviceToPage");var _FPDF_PageToDevice=Module["_FPDF_PageToDevice"]=createExportWrapper("FPDF_PageToDevice");var _FPDFBitmap_Create=Module["_FPDFBitmap_Create"]=createExportWrapper("FPDFBitmap_Create");var _FPDFBitmap_GetFormat=Module["_FPDFBitmap_GetFormat"]=createExportWrapper("FPDFBitmap_GetFormat");var _FPDFBitmap_FillRect=Module["_FPDFBitmap_FillRect"]=createExportWrapper("FPDFBitmap_FillRect");var _FPDFBitmap_GetBuffer=Module["_FPDFBitmap_GetBuffer"]=createExportWrapper("FPDFBitmap_GetBuffer");var _FPDFBitmap_GetWidth=Module["_FPDFBitmap_GetWidth"]=createExportWrapper("FPDFBitmap_GetWidth");var _FPDFBitmap_GetHeight=Module["_FPDFBitmap_GetHeight"]=createExportWrapper("FPDFBitmap_GetHeight");var _FPDFBitmap_GetStride=Module["_FPDFBitmap_GetStride"]=createExportWrapper("FPDFBitmap_GetStride");var _FPDFBitmap_Destroy=Module["_FPDFBitmap_Destroy"]=createExportWrapper("FPDFBitmap_Destroy");var _FPDF_GetPageSizeByIndexF=Module["_FPDF_GetPageSizeByIndexF"]=createExportWrapper("FPDF_GetPageSizeByIndexF");var _FPDF_GetPageSizeByIndex=Module["_FPDF_GetPageSizeByIndex"]=createExportWrapper("FPDF_GetPageSizeByIndex");var _FPDF_VIEWERREF_GetPrintScaling=Module["_FPDF_VIEWERREF_GetPrintScaling"]=createExportWrapper("FPDF_VIEWERREF_GetPrintScaling");var _FPDF_VIEWERREF_GetNumCopies=Module["_FPDF_VIEWERREF_GetNumCopies"]=createExportWrapper("FPDF_VIEWERREF_GetNumCopies");var _FPDF_VIEWERREF_GetPrintPageRange=Module["_FPDF_VIEWERREF_GetPrintPageRange"]=createExportWrapper("FPDF_VIEWERREF_GetPrintPageRange");var _FPDF_VIEWERREF_GetPrintPageRangeCount=Module["_FPDF_VIEWERREF_GetPrintPageRangeCount"]=createExportWrapper("FPDF_VIEWERREF_GetPrintPageRangeCount");var _FPDF_VIEWERREF_GetPrintPageRangeElement=Module["_FPDF_VIEWERREF_GetPrintPageRangeElement"]=createExportWrapper("FPDF_VIEWERREF_GetPrintPageRangeElement");var _FPDF_VIEWERREF_GetDuplex=Module["_FPDF_VIEWERREF_GetDuplex"]=createExportWrapper("FPDF_VIEWERREF_GetDuplex");var _FPDF_VIEWERREF_GetName=Module["_FPDF_VIEWERREF_GetName"]=createExportWrapper("FPDF_VIEWERREF_GetName");var _FPDF_CountNamedDests=Module["_FPDF_CountNamedDests"]=createExportWrapper("FPDF_CountNamedDests");var _FPDF_GetNamedDestByName=Module["_FPDF_GetNamedDestByName"]=createExportWrapper("FPDF_GetNamedDestByName");var _FPDF_GetNamedDest=Module["_FPDF_GetNamedDest"]=createExportWrapper("FPDF_GetNamedDest");var _FPDF_GetXFAPacketCount=Module["_FPDF_GetXFAPacketCount"]=createExportWrapper("FPDF_GetXFAPacketCount");var _FPDF_GetXFAPacketName=Module["_FPDF_GetXFAPacketName"]=createExportWrapper("FPDF_GetXFAPacketName");var _FPDF_GetXFAPacketContent=Module["_FPDF_GetXFAPacketContent"]=createExportWrapper("FPDF_GetXFAPacketContent");var _FPDF_GetTrailerEnds=Module["_FPDF_GetTrailerEnds"]=createExportWrapper("FPDF_GetTrailerEnds");var _GetComPDFkitLicenseErrorcode=Module["_GetComPDFkitLicenseErrorcode"]=createExportWrapper("GetComPDFkitLicenseErrorcode");var _CheckPermission=Module["_CheckPermission"]=createExportWrapper("CheckPermission");var _InitComPDFkit=Module["_InitComPDFkit"]=createExportWrapper("InitComPDFkit");var _InitComPDFkitFont=Module["_InitComPDFkitFont"]=createExportWrapper("InitComPDFkitFont");var _InitDocument=Module["_InitDocument"]=createExportWrapper("InitDocument");var _LoadDocumentByStream=Module["_LoadDocumentByStream"]=createExportWrapper("LoadDocumentByStream");var _ClearDocument=Module["_ClearDocument"]=createExportWrapper("ClearDocument");var _CreateDocument=Module["_CreateDocument"]=createExportWrapper("CreateDocument");var _GetPageCount=Module["_GetPageCount"]=createExportWrapper("GetPageCount");var _GetPage=Module["_GetPage"]=createExportWrapper("GetPage");var _InsertPage=Module["_InsertPage"]=createExportWrapper("InsertPage");var _ParsePage=Module["_ParsePage"]=createExportWrapper("ParsePage");var _ClearPage=Module["_ClearPage"]=createExportWrapper("ClearPage");var _GetPageRotation=Module["_GetPageRotation"]=createExportWrapper("GetPageRotation");var _RenderPageBitmap=Module["_RenderPageBitmap"]=createExportWrapper("RenderPageBitmap");var _RenderPageBitmapWithMatrix=Module["_RenderPageBitmapWithMatrix"]=createExportWrapper("RenderPageBitmapWithMatrix");var _RenderLayerBitmap=Module["_RenderLayerBitmap"]=createExportWrapper("RenderLayerBitmap");var _RenderPageBitmapWithMatrix2=Module["_RenderPageBitmapWithMatrix2"]=createExportWrapper("RenderPageBitmapWithMatrix2");var _GetPageWithoutParse=Module["_GetPageWithoutParse"]=createExportWrapper("GetPageWithoutParse");var _GetPageSizeForIndex=Module["_GetPageSizeForIndex"]=createExportWrapper("GetPageSizeForIndex");var _GetPageSize=Module["_GetPageSize"]=createExportWrapper("GetPageSize");var _FlattenPage=Module["_FlattenPage"]=createExportWrapper("FlattenPage");var _SaveDocumentByStream=Module["_SaveDocumentByStream"]=createExportWrapper("SaveDocumentByStream");var _SetPassword=Module["_SetPassword"]=createExportWrapper("SetPassword");var _GetPermissions=Module["_GetPermissions"]=createExportWrapper("GetPermissions");var _GetPermissionsInfo=Module["_GetPermissionsInfo"]=createExportWrapper("GetPermissionsInfo");var _CheckOwnerPassword=Module["_CheckOwnerPassword"]=createExportWrapper("CheckOwnerPassword");var _CheckOwnerUnlocked=Module["_CheckOwnerUnlocked"]=createExportWrapper("CheckOwnerUnlocked");var _UpdateAnnotAp=Module["_UpdateAnnotAp"]=createExportWrapper("UpdateAnnotAp");var _UpdateAnnotApWithInk=Module["_UpdateAnnotApWithInk"]=createExportWrapper("UpdateAnnotApWithInk");var _RemoveAP=Module["_RemoveAP"]=createExportWrapper("RemoveAP");var _HasAnnotAp=Module["_HasAnnotAp"]=createExportWrapper("HasAnnotAp");var _GetAnnotApRotation=Module["_GetAnnotApRotation"]=createExportWrapper("GetAnnotApRotation");var _GetAnnotFlag=Module["_GetAnnotFlag"]=createExportWrapper("GetAnnotFlag");var _SetAnnotFlag=Module["_SetAnnotFlag"]=createExportWrapper("SetAnnotFlag");var _GetAnnotCount=Module["_GetAnnotCount"]=createExportWrapper("GetAnnotCount");var _InitAnnot=Module["_InitAnnot"]=createExportWrapper("InitAnnot");var _CreateAnnot=Module["_CreateAnnot"]=createExportWrapper("CreateAnnot");var _GetAnnotType=Module["_GetAnnotType"]=createExportWrapper("GetAnnotType");var _GetAnnotRect=Module["_GetAnnotRect"]=createExportWrapper("GetAnnotRect");var _SetAnnotRect=Module["_SetAnnotRect"]=createExportWrapper("SetAnnotRect");var _GetAnnotBorderWidth=Module["_GetAnnotBorderWidth"]=createExportWrapper("GetAnnotBorderWidth");var _SetAnnotBorderWidth=Module["_SetAnnotBorderWidth"]=createExportWrapper("SetAnnotBorderWidth");var _GetAnnotBorderStyleDashSize=Module["_GetAnnotBorderStyleDashSize"]=createExportWrapper("GetAnnotBorderStyleDashSize");var _GetAnnotBorderStyle=Module["_GetAnnotBorderStyle"]=createExportWrapper("GetAnnotBorderStyle");var _SetAnnotBorderStyle=Module["_SetAnnotBorderStyle"]=createExportWrapper("SetAnnotBorderStyle");var _GetAnnotAuthor=Module["_GetAnnotAuthor"]=createExportWrapper("GetAnnotAuthor");var _SetAnnotAuthor=Module["_SetAnnotAuthor"]=createExportWrapper("SetAnnotAuthor");var _GetAnnotTitle=Module["_GetAnnotTitle"]=createExportWrapper("GetAnnotTitle");var _SetAnnotTitle=Module["_SetAnnotTitle"]=createExportWrapper("SetAnnotTitle");var _GetAnnotCreationDate=Module["_GetAnnotCreationDate"]=createExportWrapper("GetAnnotCreationDate");var _SetAnnotCreationDate=Module["_SetAnnotCreationDate"]=createExportWrapper("SetAnnotCreationDate");var _GetAnnotRecentlyModifyDate=Module["_GetAnnotRecentlyModifyDate"]=createExportWrapper("GetAnnotRecentlyModifyDate");var _SetAnnotRecentlyModifyDate=Module["_SetAnnotRecentlyModifyDate"]=createExportWrapper("SetAnnotRecentlyModifyDate");var _GetAnnotContent=Module["_GetAnnotContent"]=createExportWrapper("GetAnnotContent");var _SetAnnotContent=Module["_SetAnnotContent"]=createExportWrapper("SetAnnotContent");var _GetAnnotRGBColor=Module["_GetAnnotRGBColor"]=createExportWrapper("GetAnnotRGBColor");var _SetAnnotRGBColor=Module["_SetAnnotRGBColor"]=createExportWrapper("SetAnnotRGBColor");var _GetAnnotTransparency=Module["_GetAnnotTransparency"]=createExportWrapper("GetAnnotTransparency");var _SetAnnotTransparency=Module["_SetAnnotTransparency"]=createExportWrapper("SetAnnotTransparency");var _GetAnnotFilledRGBColor=Module["_GetAnnotFilledRGBColor"]=createExportWrapper("GetAnnotFilledRGBColor");var _SetAnnotFilledRGBColor=Module["_SetAnnotFilledRGBColor"]=createExportWrapper("SetAnnotFilledRGBColor");var _GetAnnotFilledTransparency=Module["_GetAnnotFilledTransparency"]=createExportWrapper("GetAnnotFilledTransparency");var _SetAnnotFilledTransparency=Module["_SetAnnotFilledTransparency"]=createExportWrapper("SetAnnotFilledTransparency");var _ClearAnnotFilledRGBColor=Module["_ClearAnnotFilledRGBColor"]=createExportWrapper("ClearAnnotFilledRGBColor");var _ClearAnnotFilledTransparency=Module["_ClearAnnotFilledTransparency"]=createExportWrapper("ClearAnnotFilledTransparency");var _GetAnnotBorderRGBColor=Module["_GetAnnotBorderRGBColor"]=createExportWrapper("GetAnnotBorderRGBColor");var _SetAnnotBorderRGBColor=Module["_SetAnnotBorderRGBColor"]=createExportWrapper("SetAnnotBorderRGBColor");var _GetAnnotBgRGBColor=Module["_GetAnnotBgRGBColor"]=createExportWrapper("GetAnnotBgRGBColor");var _SetAnnotBgRGBColor=Module["_SetAnnotBgRGBColor"]=createExportWrapper("SetAnnotBgRGBColor");var _GetLinePoint=Module["_GetLinePoint"]=createExportWrapper("GetLinePoint");var _SetLinePoint=Module["_SetLinePoint"]=createExportWrapper("SetLinePoint");var _GetLineTailType=Module["_GetLineTailType"]=createExportWrapper("GetLineTailType");var _SetLineTailType=Module["_SetLineTailType"]=createExportWrapper("SetLineTailType");var _GetLineType=Module["_GetLineType"]=createExportWrapper("GetLineType");var _SetLineType=Module["_SetLineType"]=createExportWrapper("SetLineType");var _GetAnnotFont=Module["_GetAnnotFont"]=createExportWrapper("GetAnnotFont");var _SetAnnotFont=Module["_SetAnnotFont"]=createExportWrapper("SetAnnotFont");var _GetAnnotFontDa=Module["_GetAnnotFontDa"]=createExportWrapper("GetAnnotFontDa");var _SetAnnotFontDa=Module["_SetAnnotFontDa"]=createExportWrapper("SetAnnotFontDa");var _GetFreeTextType=Module["_GetFreeTextType"]=createExportWrapper("GetFreeTextType");var _SetFreeTextType=Module["_SetFreeTextType"]=createExportWrapper("SetFreeTextType");var _GetFreeTextFont=Module["_GetFreeTextFont"]=createExportWrapper("GetFreeTextFont");var _GetFreeTextContent=Module["_GetFreeTextContent"]=createExportWrapper("GetFreeTextContent");var _SetFreeText=Module["_SetFreeText"]=createExportWrapper("SetFreeText");var _GetFreeTextBgColor=Module["_GetFreeTextBgColor"]=createExportWrapper("GetFreeTextBgColor");var _SetFreeTextBgColor=Module["_SetFreeTextBgColor"]=createExportWrapper("SetFreeTextBgColor");var _ClearFreeTextBgColor=Module["_ClearFreeTextBgColor"]=createExportWrapper("ClearFreeTextBgColor");var _GetFreeTextBorderColor=Module["_GetFreeTextBorderColor"]=createExportWrapper("GetFreeTextBorderColor");var _SetFreeTextBorderColor=Module["_SetFreeTextBorderColor"]=createExportWrapper("SetFreeTextBorderColor");var _SetFreeTextContent=Module["_SetFreeTextContent"]=createExportWrapper("SetFreeTextContent");var _GetFreetextTextRect=Module["_GetFreetextTextRect"]=createExportWrapper("GetFreetextTextRect");var _GetInkPathCount=Module["_GetInkPathCount"]=createExportWrapper("GetInkPathCount");var _GetInkPointsCount=Module["_GetInkPointsCount"]=createExportWrapper("GetInkPointsCount");var _GetInkPoints=Module["_GetInkPoints"]=createExportWrapper("GetInkPoints");var _SetInkPath=Module["_SetInkPath"]=createExportWrapper("SetInkPath");var _GetQuadPointsCount=Module["_GetQuadPointsCount"]=createExportWrapper("GetQuadPointsCount");var _GetQuadPoints=Module["_GetQuadPoints"]=createExportWrapper("GetQuadPoints");var _SetQuadPoints=Module["_SetQuadPoints"]=createExportWrapper("SetQuadPoints");var _GetLinkActionType=Module["_GetLinkActionType"]=createExportWrapper("GetLinkActionType");var _GetLinkUri=Module["_GetLinkUri"]=createExportWrapper("GetLinkUri");var _GetLinkDest=Module["_GetLinkDest"]=createExportWrapper("GetLinkDest");var _SetLinkUri=Module["_SetLinkUri"]=createExportWrapper("SetLinkUri");var _SetLinkDest=Module["_SetLinkDest"]=createExportWrapper("SetLinkDest");var _SetLinkAction=Module["_SetLinkAction"]=createExportWrapper("SetLinkAction");var _GetLinkAction=Module["_GetLinkAction"]=createExportWrapper("GetLinkAction");var _GetLinkDestination=Module["_GetLinkDestination"]=createExportWrapper("GetLinkDestination");var _SetLinkDestination=Module["_SetLinkDestination"]=createExportWrapper("SetLinkDestination");var _SetStandardStamp=Module["_SetStandardStamp"]=createExportWrapper("SetStandardStamp");var _SetTextStamp=Module["_SetTextStamp"]=createExportWrapper("SetTextStamp");var _SetImageStampWithPixel=Module["_SetImageStampWithPixel"]=createExportWrapper("SetImageStampWithPixel");var _SetImageStampByStream=Module["_SetImageStampByStream"]=createExportWrapper("SetImageStampByStream");var _SetImageStamp=Module["_SetImageStamp"]=createExportWrapper("SetImageStamp");var _SetImageCStamp=Module["_SetImageCStamp"]=createExportWrapper("SetImageCStamp");var _GetStampType=Module["_GetStampType"]=createExportWrapper("GetStampType");var _GetStandardStamp=Module["_GetStandardStamp"]=createExportWrapper("GetStandardStamp");var _GetTextStamp=Module["_GetTextStamp"]=createExportWrapper("GetTextStamp");var _IsStampSignature=Module["_IsStampSignature"]=createExportWrapper("IsStampSignature");var _SetStampSignature=Module["_SetStampSignature"]=createExportWrapper("SetStampSignature");var _GetTextAlignment=Module["_GetTextAlignment"]=createExportWrapper("GetTextAlignment");var _SetTextAlignment=Module["_SetTextAlignment"]=createExportWrapper("SetTextAlignment");var _HasSoundSource=Module["_HasSoundSource"]=createExportWrapper("HasSoundSource");var _GetMediaSrcType=Module["_GetMediaSrcType"]=createExportWrapper("GetMediaSrcType");var _GetRichMediaUrl=Module["_GetRichMediaUrl"]=createExportWrapper("GetRichMediaUrl");var _GetOverLayText=Module["_GetOverLayText"]=createExportWrapper("GetOverLayText");var _SetOverLayText=Module["_SetOverLayText"]=createExportWrapper("SetOverLayText");var _GetOCColor=Module["_GetOCColor"]=createExportWrapper("GetOCColor");var _SetOCColor=Module["_SetOCColor"]=createExportWrapper("SetOCColor");var _ClearOCColor=Module["_ClearOCColor"]=createExportWrapper("ClearOCColor");var _GetROTextColor=Module["_GetROTextColor"]=createExportWrapper("GetROTextColor");var _ApplyRedaction=Module["_ApplyRedaction"]=createExportWrapper("ApplyRedaction");var _ApplyRedactionPage=Module["_ApplyRedactionPage"]=createExportWrapper("ApplyRedactionPage");var _ErasureRedactionFromRect=Module["_ErasureRedactionFromRect"]=createExportWrapper("ErasureRedactionFromRect");var _SetMarkupTranslation=Module["_SetMarkupTranslation"]=createExportWrapper("SetMarkupTranslation");var _GetMarkupTranslation=Module["_GetMarkupTranslation"]=createExportWrapper("GetMarkupTranslation");var _RenderAnnot=Module["_RenderAnnot"]=createExportWrapper("RenderAnnot");var _RemoveAnnot=Module["_RemoveAnnot"]=createExportWrapper("RemoveAnnot");var _SetTitleUTF8=Module["_SetTitleUTF8"]=createExportWrapper("SetTitleUTF8");var _GetTitleUTF8=Module["_GetTitleUTF8"]=createExportWrapper("GetTitleUTF8");var _GetAnnotDictName=Module["_GetAnnotDictName"]=createExportWrapper("GetAnnotDictName");var _SetAnnotDictName=Module["_SetAnnotDictName"]=createExportWrapper("SetAnnotDictName");var _GetAnnotDictArray=Module["_GetAnnotDictArray"]=createExportWrapper("GetAnnotDictArray");var _SetAnnotDictArray=Module["_SetAnnotDictArray"]=createExportWrapper("SetAnnotDictArray");var _CreateReplyAnnotation=Module["_CreateReplyAnnotation"]=createExportWrapper("CreateReplyAnnotation");var _CreateReplyStateAnnotation=Module["_CreateReplyStateAnnotation"]=createExportWrapper("CreateReplyStateAnnotation");var _GetReplies=Module["_GetReplies"]=createExportWrapper("GetReplies");var _SetMarkedAnnotState=Module["_SetMarkedAnnotState"]=createExportWrapper("SetMarkedAnnotState");var _SetReviewAnnotState=Module["_SetReviewAnnotState"]=createExportWrapper("SetReviewAnnotState");var _GetState=Module["_GetState"]=createExportWrapper("GetState");var _SetState=Module["_SetState"]=createExportWrapper("SetState");var _IsReplyAnnot=Module["_IsReplyAnnot"]=createExportWrapper("IsReplyAnnot");var _IsReviewStateAnnot=Module["_IsReviewStateAnnot"]=createExportWrapper("IsReviewStateAnnot");var _IsMarkedStateAnnot=Module["_IsMarkedStateAnnot"]=createExportWrapper("IsMarkedStateAnnot");var _RemoveFromPageIncludeReplyAnnot=Module["_RemoveFromPageIncludeReplyAnnot"]=createExportWrapper("RemoveFromPageIncludeReplyAnnot");var _CreateWidget=Module["_CreateWidget"]=createExportWrapper("CreateWidget");var _GetWidgetType=Module["_GetWidgetType"]=createExportWrapper("GetWidgetType");var _GetWidgetFieldFlag=Module["_GetWidgetFieldFlag"]=createExportWrapper("GetWidgetFieldFlag");var _SetWidgetFieldFlag=Module["_SetWidgetFieldFlag"]=createExportWrapper("SetWidgetFieldFlag");var _GetWidgetFieldName=Module["_GetWidgetFieldName"]=createExportWrapper("GetWidgetFieldName");var _SetWidgetFieldName=Module["_SetWidgetFieldName"]=createExportWrapper("SetWidgetFieldName");var _GetWidgetFieldType=Module["_GetWidgetFieldType"]=createExportWrapper("GetWidgetFieldType");var _SetWidgetFieldType=Module["_SetWidgetFieldType"]=createExportWrapper("SetWidgetFieldType");var _GetWidgetBgRGBColor=Module["_GetWidgetBgRGBColor"]=createExportWrapper("GetWidgetBgRGBColor");var _GetWidgetBackgroundRGBColor=Module["_GetWidgetBackgroundRGBColor"]=createExportWrapper("GetWidgetBackgroundRGBColor");var _SetWidgetBackgroundRGBColor=Module["_SetWidgetBackgroundRGBColor"]=createExportWrapper("SetWidgetBackgroundRGBColor");var _ClearWidgetBackgroundRGBColor=Module["_ClearWidgetBackgroundRGBColor"]=createExportWrapper("ClearWidgetBackgroundRGBColor");var _GetWidgetAlpha=Module["_GetWidgetAlpha"]=createExportWrapper("GetWidgetAlpha");var _GetWidgetBorderRGBColor=Module["_GetWidgetBorderRGBColor"]=createExportWrapper("GetWidgetBorderRGBColor");var _SetWidgetBorderRGBColor=Module["_SetWidgetBorderRGBColor"]=createExportWrapper("SetWidgetBorderRGBColor");var _ClearWidgetBorderRGBColor=Module["_ClearWidgetBorderRGBColor"]=createExportWrapper("ClearWidgetBorderRGBColor");var _GetWidgetBorderStyle=Module["_GetWidgetBorderStyle"]=createExportWrapper("GetWidgetBorderStyle");var _SetWidgetBorderStyle=Module["_SetWidgetBorderStyle"]=createExportWrapper("SetWidgetBorderStyle");var _GetWidgetCheckStyle=Module["_GetWidgetCheckStyle"]=createExportWrapper("GetWidgetCheckStyle");var _SetWidgetCheckStyle=Module["_SetWidgetCheckStyle"]=createExportWrapper("SetWidgetCheckStyle");var _GetWidgetCheckRGBColor=Module["_GetWidgetCheckRGBColor"]=createExportWrapper("GetWidgetCheckRGBColor");var _SetWidgetCheckRGBColor=Module["_SetWidgetCheckRGBColor"]=createExportWrapper("SetWidgetCheckRGBColor");var _GetTextFieldContent=Module["_GetTextFieldContent"]=createExportWrapper("GetTextFieldContent");var _SetTextFieldContent=Module["_SetTextFieldContent"]=createExportWrapper("SetTextFieldContent");var _GetRequiredFlag=Module["_GetRequiredFlag"]=createExportWrapper("GetRequiredFlag");var _SetRequiredFlag=Module["_SetRequiredFlag"]=createExportWrapper("SetRequiredFlag");var _IsMultiLine=Module["_IsMultiLine"]=createExportWrapper("IsMultiLine");var _SetMultiLine=Module["_SetMultiLine"]=createExportWrapper("SetMultiLine");var _IsPassword=Module["_IsPassword"]=createExportWrapper("IsPassword");var _GetTextFieldSpe=Module["_GetTextFieldSpe"]=createExportWrapper("GetTextFieldSpe");var _SetTextFieldSpe=Module["_SetTextFieldSpe"]=createExportWrapper("SetTextFieldSpe");var _GetWidgetTextJustification=Module["_GetWidgetTextJustification"]=createExportWrapper("GetWidgetTextJustification");var _SetWidgetTextJustification=Module["_SetWidgetTextJustification"]=createExportWrapper("SetWidgetTextJustification");var _GetButtonTitle=Module["_GetButtonTitle"]=createExportWrapper("GetButtonTitle");var _SetButtonTitle=Module["_SetButtonTitle"]=createExportWrapper("SetButtonTitle");var _SetButtonAction=Module["_SetButtonAction"]=createExportWrapper("SetButtonAction");var _GetButtonAction=Module["_GetButtonAction"]=createExportWrapper("GetButtonAction");var _GetBtnActionType=Module["_GetBtnActionType"]=createExportWrapper("GetBtnActionType");var _GetBtnActionDestPage=Module["_GetBtnActionDestPage"]=createExportWrapper("GetBtnActionDestPage");var _GetBtnActionNamedAction=Module["_GetBtnActionNamedAction"]=createExportWrapper("GetBtnActionNamedAction");var _GetBtnActionURI=Module["_GetBtnActionURI"]=createExportWrapper("GetBtnActionURI");var _GetItemsSelectedValue=Module["_GetItemsSelectedValue"]=createExportWrapper("GetItemsSelectedValue");var _GetItemsSelectedItem=Module["_GetItemsSelectedItem"]=createExportWrapper("GetItemsSelectedItem");var _SetItemsSelectedIndex=Module["_SetItemsSelectedIndex"]=createExportWrapper("SetItemsSelectedIndex");var _GetItemsCount=Module["_GetItemsCount"]=createExportWrapper("GetItemsCount");var _GetWidgetItems=Module["_GetWidgetItems"]=createExportWrapper("GetWidgetItems");var _AddOptionItem=Module["_AddOptionItem"]=createExportWrapper("AddOptionItem");var _RemoveOptionItem=Module["_RemoveOptionItem"]=createExportWrapper("RemoveOptionItem");var _IsMultiSelectListBox=Module["_IsMultiSelectListBox"]=createExportWrapper("IsMultiSelectListBox");var _IsEditable=Module["_IsEditable"]=createExportWrapper("IsEditable");var _GetCheckBoxIsChecked=Module["_GetCheckBoxIsChecked"]=createExportWrapper("GetCheckBoxIsChecked");var _SetCheckBoxIsChecked=Module["_SetCheckBoxIsChecked"]=createExportWrapper("SetCheckBoxIsChecked");var _GetRadioButtonIsChecked=Module["_GetRadioButtonIsChecked"]=createExportWrapper("GetRadioButtonIsChecked");var _SetRadioButtonIsChecked=Module["_SetRadioButtonIsChecked"]=createExportWrapper("SetRadioButtonIsChecked");var _IsSigned=Module["_IsSigned"]=createExportWrapper("IsSigned");var _UpdateApWithText=Module["_UpdateApWithText"]=createExportWrapper("UpdateApWithText");var _UpdateAPWithImageData=Module["_UpdateAPWithImageData"]=createExportWrapper("UpdateAPWithImageData");var _UpdateAPWithImage=Module["_UpdateAPWithImage"]=createExportWrapper("UpdateAPWithImage");var _UpdateApWithInk=Module["_UpdateApWithInk"]=createExportWrapper("UpdateApWithInk");var _SignWithName=Module["_SignWithName"]=createExportWrapper("SignWithName");var _ResetForm=Module["_ResetForm"]=createExportWrapper("ResetForm");var _UpdateFormAp=Module["_UpdateFormAp"]=createExportWrapper("UpdateFormAp");var _UpdateApWithDigitalSigConfig=Module["_UpdateApWithDigitalSigConfig"]=createExportWrapper("UpdateApWithDigitalSigConfig");var _InitTextFind=Module["_InitTextFind"]=createExportWrapper("InitTextFind");var _ClearTextFind=Module["_ClearTextFind"]=createExportWrapper("ClearTextFind");var _FindStart=Module["_FindStart"]=createExportWrapper("FindStart");var _FindNext=Module["_FindNext"]=createExportWrapper("FindNext");var _InitCompareDrawing=Module["_InitCompareDrawing"]=createExportWrapper("InitCompareDrawing");var _InitCompareDrawing2=Module["_InitCompareDrawing2"]=createExportWrapper("InitCompareDrawing2");var _ReleaseCompareDrawing=Module["_ReleaseCompareDrawing"]=createExportWrapper("ReleaseCompareDrawing");var _CompareContent=Module["_CompareContent"]=createExportWrapper("CompareContent");var _SetColorCompare=Module["_SetColorCompare"]=createExportWrapper("SetColorCompare");var _SetColorCompared=Module["_SetColorCompared"]=createExportWrapper("SetColorCompared");var _SetStrokeAlphaCompare=Module["_SetStrokeAlphaCompare"]=createExportWrapper("SetStrokeAlphaCompare");var _SetStrokeAlphaCompared=Module["_SetStrokeAlphaCompared"]=createExportWrapper("SetStrokeAlphaCompared");var _SetFillAlphaCompare=Module["_SetFillAlphaCompare"]=createExportWrapper("SetFillAlphaCompare");var _SetFillAlphaCompared=Module["_SetFillAlphaCompared"]=createExportWrapper("SetFillAlphaCompared");var _SetNoFill=Module["_SetNoFill"]=createExportWrapper("SetNoFill");var _SetBlendMode=Module["_SetBlendMode"]=createExportWrapper("SetBlendMode");var _GetDocGenerate=Module["_GetDocGenerate"]=createExportWrapper("GetDocGenerate");var _InitCompare=Module["_InitCompare"]=createExportWrapper("InitCompare");var _ReleaseCompare=Module["_ReleaseCompare"]=createExportWrapper("ReleaseCompare");var _DoCompare=Module["_DoCompare"]=createExportWrapper("DoCompare");var _GetCompareResultsCount=Module["_GetCompareResultsCount"]=createExportWrapper("GetCompareResultsCount");var _GetCompareResults=Module["_GetCompareResults"]=createExportWrapper("GetCompareResults");var _GetResultsCountParams=Module["_GetResultsCountParams"]=createExportWrapper("GetResultsCountParams");var _ReleaseCompareResults=Module["_ReleaseCompareResults"]=createExportWrapper("ReleaseCompareResults");var _SetReplaceResColor=Module["_SetReplaceResColor"]=createExportWrapper("SetReplaceResColor");var _SetInsertResColor=Module["_SetInsertResColor"]=createExportWrapper("SetInsertResColor");var _SetDeleteResColor=Module["_SetDeleteResColor"]=createExportWrapper("SetDeleteResColor");var _SetReplaceResTransparency=Module["_SetReplaceResTransparency"]=createExportWrapper("SetReplaceResTransparency");var _SetInsertResTransparency=Module["_SetInsertResTransparency"]=createExportWrapper("SetInsertResTransparency");var _SetDeleteResTransparency=Module["_SetDeleteResTransparency"]=createExportWrapper("SetDeleteResTransparency");var _GenerateNewDoc=Module["_GenerateNewDoc"]=createExportWrapper("GenerateNewDoc");var _GetOutlineRoot=Module["_GetOutlineRoot"]=createExportWrapper("GetOutlineRoot");var _NewOutlineRoot=Module["_NewOutlineRoot"]=createExportWrapper("NewOutlineRoot");var _InsertChildAtIndex=Module["_InsertChildAtIndex"]=createExportWrapper("InsertChildAtIndex");var _MoveChildAtIndex=Module["_MoveChildAtIndex"]=createExportWrapper("MoveChildAtIndex");var _RemoveFromParent=Module["_RemoveFromParent"]=createExportWrapper("RemoveFromParent");var _ClearOutline=Module["_ClearOutline"]=createExportWrapper("ClearOutline");var _GetFirst=Module["_GetFirst"]=createExportWrapper("GetFirst");var _GetNext=Module["_GetNext"]=createExportWrapper("GetNext");var _IsValid=Module["_IsValid"]=createExportWrapper("IsValid");var _GetTitle=Module["_GetTitle"]=createExportWrapper("GetTitle");var _GetOutlineActionType=Module["_GetOutlineActionType"]=createExportWrapper("GetOutlineActionType");var _GetDestPage=Module["_GetDestPage"]=createExportWrapper("GetDestPage");var _GetUri=Module["_GetUri"]=createExportWrapper("GetUri");var _SetTitle=Module["_SetTitle"]=createExportWrapper("SetTitle");var _SetDestPage=Module["_SetDestPage"]=createExportWrapper("SetDestPage");var _SetUri=Module["_SetUri"]=createExportWrapper("SetUri");var _GetOutlineAction=Module["_GetOutlineAction"]=createExportWrapper("GetOutlineAction");var _SetOutlineAction=Module["_SetOutlineAction"]=createExportWrapper("SetOutlineAction");var _GetOutlineDestination=Module["_GetOutlineDestination"]=createExportWrapper("GetOutlineDestination");var _SetOutlineDestination=Module["_SetOutlineDestination"]=createExportWrapper("SetOutlineDestination");var _XFDFExportAnnotations=Module["_XFDFExportAnnotations"]=createExportWrapper("XFDFExportAnnotations");var _XFDFImportAnnotaitons=Module["_XFDFImportAnnotaitons"]=createExportWrapper("XFDFImportAnnotaitons");var _XFDFExportWidgets=Module["_XFDFExportWidgets"]=createExportWrapper("XFDFExportWidgets");var _XFDFImportWidgets=Module["_XFDFImportWidgets"]=createExportWrapper("XFDFImportWidgets");var _ExportCustomWidgets=Module["_ExportCustomWidgets"]=createExportWrapper("ExportCustomWidgets");var _ImportCustomWidgets=Module["_ImportCustomWidgets"]=createExportWrapper("ImportCustomWidgets");var _XFDFExportCustomWidgets=Module["_XFDFExportCustomWidgets"]=createExportWrapper("XFDFExportCustomWidgets");var _InitEditPage=Module["_InitEditPage"]=createExportWrapper("InitEditPage");var _ClearEditPage=Module["_ClearEditPage"]=createExportWrapper("ClearEditPage");var _BeginEdit=Module["_BeginEdit"]=createExportWrapper("BeginEdit");var _EndEdit=Module["_EndEdit"]=createExportWrapper("EndEdit");var _DoSomethingAfterEnd=Module["_DoSomethingAfterEnd"]=createExportWrapper("DoSomethingAfterEnd");var _CanRedo=Module["_CanRedo"]=createExportWrapper("CanRedo");var _CanUndo=Module["_CanUndo"]=createExportWrapper("CanUndo");var _Redo=Module["_Redo"]=createExportWrapper("Redo");var _Undo=Module["_Undo"]=createExportWrapper("Undo");var _GetEditAreaCount=Module["_GetEditAreaCount"]=createExportWrapper("GetEditAreaCount");var _GetEditArea=Module["_GetEditArea"]=createExportWrapper("GetEditArea");var _RemoveEditArea=Module["_RemoveEditArea"]=createExportWrapper("RemoveEditArea");var _RemoveEditAreaByIndex=Module["_RemoveEditAreaByIndex"]=createExportWrapper("RemoveEditAreaByIndex");var _CreateNewTextArea=Module["_CreateNewTextArea"]=createExportWrapper("CreateNewTextArea");var _CreateNewImageArea=Module["_CreateNewImageArea"]=createExportWrapper("CreateNewImageArea");var _CreateNewImageAreaByStream=Module["_CreateNewImageAreaByStream"]=createExportWrapper("CreateNewImageAreaByStream");var _CopyArea=Module["_CopyArea"]=createExportWrapper("CopyArea");var _PasteArea=Module["_PasteArea"]=createExportWrapper("PasteArea");var _GetCopyAreaContent=Module["_GetCopyAreaContent"]=createExportWrapper("GetCopyAreaContent");var _DestroyAreaInfo=Module["_DestroyAreaInfo"]=createExportWrapper("DestroyAreaInfo");var _IsTextArea=Module["_IsTextArea"]=createExportWrapper("IsTextArea");var _IsImageArea=Module["_IsImageArea"]=createExportWrapper("IsImageArea");var _GetFrame=Module["_GetFrame"]=createExportWrapper("GetFrame");var _SetFrame=Module["_SetFrame"]=createExportWrapper("SetFrame");var _GetBeginAndEndCharPlace=Module["_GetBeginAndEndCharPlace"]=createExportWrapper("GetBeginAndEndCharPlace");var _GetPrevCharPlace=Module["_GetPrevCharPlace"]=createExportWrapper("GetPrevCharPlace");var _SetLineLeading=Module["_SetLineLeading"]=createExportWrapper("SetLineLeading");var _GetLineLeading=Module["_GetLineLeading"]=createExportWrapper("GetLineLeading");var _SetSectionAfterLeading=Module["_SetSectionAfterLeading"]=createExportWrapper("SetSectionAfterLeading");var _GetSectionAfterLeading=Module["_GetSectionAfterLeading"]=createExportWrapper("GetSectionAfterLeading");var _SetCharacterSpacing=Module["_SetCharacterSpacing"]=createExportWrapper("SetCharacterSpacing");var _GetCharacterSpacing=Module["_GetCharacterSpacing"]=createExportWrapper("GetCharacterSpacing");var _SetIndentation=Module["_SetIndentation"]=createExportWrapper("SetIndentation");var _GetIndentation=Module["_GetIndentation"]=createExportWrapper("GetIndentation");var _GetNextCharPlace=Module["_GetNextCharPlace"]=createExportWrapper("GetNextCharPlace");var _GetUpCharPlace=Module["_GetUpCharPlace"]=createExportWrapper("GetUpCharPlace");var _GetDownCharPlace=Module["_GetDownCharPlace"]=createExportWrapper("GetDownCharPlace");var _SelectCharItemAtPos=Module["_SelectCharItemAtPos"]=createExportWrapper("SelectCharItemAtPos");var _SelectCharsRangeAtPos=Module["_SelectCharsRangeAtPos"]=createExportWrapper("SelectCharsRangeAtPos");var _RefreshRange=Module["_RefreshRange"]=createExportWrapper("RefreshRange");var _GetSelectLineRectsCounts=Module["_GetSelectLineRectsCounts"]=createExportWrapper("GetSelectLineRectsCounts");var _GetSelectLineRects=Module["_GetSelectLineRects"]=createExportWrapper("GetSelectLineRects");var _GetRectForWordAtPos=Module["_GetRectForWordAtPos"]=createExportWrapper("GetRectForWordAtPos");var _GetRectForLineAtPos=Module["_GetRectForLineAtPos"]=createExportWrapper("GetRectForLineAtPos");var _GetSectionAtPos=Module["_GetSectionAtPos"]=createExportWrapper("GetSectionAtPos");var _GetTextByRange=Module["_GetTextByRange"]=createExportWrapper("GetTextByRange");var _BackSpaceChar=Module["_BackSpaceChar"]=createExportWrapper("BackSpaceChar");var _DeleteChar=Module["_DeleteChar"]=createExportWrapper("DeleteChar");var _DeleteChars=Module["_DeleteChars"]=createExportWrapper("DeleteChars");var _GetLineBeginCharPlace=Module["_GetLineBeginCharPlace"]=createExportWrapper("GetLineBeginCharPlace");var _GetLineEndCharPlace=Module["_GetLineEndCharPlace"]=createExportWrapper("GetLineEndCharPlace");var _GetSectionBeginCharPlace=Module["_GetSectionBeginCharPlace"]=createExportWrapper("GetSectionBeginCharPlace");var _GetSectionEndCharPlace=Module["_GetSectionEndCharPlace"]=createExportWrapper("GetSectionEndCharPlace");var _GetNextWordCharPlace=Module["_GetNextWordCharPlace"]=createExportWrapper("GetNextWordCharPlace");var _GetPreWordCharPlace=Module["_GetPreWordCharPlace"]=createExportWrapper("GetPreWordCharPlace");var _GetTextStyle=Module["_GetTextStyle"]=createExportWrapper("GetTextStyle");var _GetTextCursorPoints=Module["_GetTextCursorPoints"]=createExportWrapper("GetTextCursorPoints");var _SetCharsFontSize=Module["_SetCharsFontSize"]=createExportWrapper("SetCharsFontSize");var _SetCharsFontColor=Module["_SetCharsFontColor"]=createExportWrapper("SetCharsFontColor");var _SetCharsFontTransparency=Module["_SetCharsFontTransparency"]=createExportWrapper("SetCharsFontTransparency");var _SetCharsFontRederingMode=Module["_SetCharsFontRederingMode"]=createExportWrapper("SetCharsFontRederingMode");var _SetCharsFontBold=Module["_SetCharsFontBold"]=createExportWrapper("SetCharsFontBold");var _IsCharsFontBold=Module["_IsCharsFontBold"]=createExportWrapper("IsCharsFontBold");var _ClearCharsFontBold=Module["_ClearCharsFontBold"]=createExportWrapper("ClearCharsFontBold");var _SetCharsFontItalic=Module["_SetCharsFontItalic"]=createExportWrapper("SetCharsFontItalic");var _IsCharsFontItalic=Module["_IsCharsFontItalic"]=createExportWrapper("IsCharsFontItalic");var _ClearCharsFontItalic=Module["_ClearCharsFontItalic"]=createExportWrapper("ClearCharsFontItalic");var _SetFontFromNativeTrueTypeFont=Module["_SetFontFromNativeTrueTypeFont"]=createExportWrapper("SetFontFromNativeTrueTypeFont");var _GetBaseFontName=Module["_GetBaseFontName"]=createExportWrapper("GetBaseFontName");var _InsertText=Module["_InsertText"]=createExportWrapper("InsertText");var _SetTextAligningBlock=Module["_SetTextAligningBlock"]=createExportWrapper("SetTextAligningBlock");var _SetTextAligningSection=Module["_SetTextAligningSection"]=createExportWrapper("SetTextAligningSection");var _SetTextAligningRange=Module["_SetTextAligningRange"]=createExportWrapper("SetTextAligningRange");var _GetTextSectionAlignType=Module["_GetTextSectionAlignType"]=createExportWrapper("GetTextSectionAlignType");var _TransformImage=Module["_TransformImage"]=createExportWrapper("TransformImage");var _ScaleImage=Module["_ScaleImage"]=createExportWrapper("ScaleImage");var _GetImageRotate=Module["_GetImageRotate"]=createExportWrapper("GetImageRotate");var _RotateImage=Module["_RotateImage"]=createExportWrapper("RotateImage");var _HorizontalMirrorImage=Module["_HorizontalMirrorImage"]=createExportWrapper("HorizontalMirrorImage");var _VerticalMirrorImage=Module["_VerticalMirrorImage"]=createExportWrapper("VerticalMirrorImage");var _CutWithRect=Module["_CutWithRect"]=createExportWrapper("CutWithRect");var _GetClipRect=Module["_GetClipRect"]=createExportWrapper("GetClipRect");var _SetClipRect=Module["_SetClipRect"]=createExportWrapper("SetClipRect");var _SetImageTransparency=Module["_SetImageTransparency"]=createExportWrapper("SetImageTransparency");var _GetImageTransparency=Module["_GetImageTransparency"]=createExportWrapper("GetImageTransparency");var _GetImageAreaSize=Module["_GetImageAreaSize"]=createExportWrapper("GetImageAreaSize");var _GetImageAreaData=Module["_GetImageAreaData"]=createExportWrapper("GetImageAreaData");var _ExtractOriginalImage=Module["_ExtractOriginalImage"]=createExportWrapper("ExtractOriginalImage");var _ExtractThumbImage=Module["_ExtractThumbImage"]=createExportWrapper("ExtractThumbImage");var _ReplaceImageArea=Module["_ReplaceImageArea"]=createExportWrapper("ReplaceImageArea");var _ReplaceImageAreaByStream=Module["_ReplaceImageAreaByStream"]=createExportWrapper("ReplaceImageAreaByStream");var _InsertPageWithImageByStream=Module["_InsertPageWithImageByStream"]=createExportWrapper("InsertPageWithImageByStream");var _MovePage=Module["_MovePage"]=createExportWrapper("MovePage");var _ExchangePage=Module["_ExchangePage"]=createExportWrapper("ExchangePage");var _RemovePage=Module["_RemovePage"]=createExportWrapper("RemovePage");var _RotatePage=Module["_RotatePage"]=createExportWrapper("RotatePage");var _ImportPages=Module["_ImportPages"]=createExportWrapper("ImportPages");var _ImportPagesAtIndex=Module["_ImportPagesAtIndex"]=createExportWrapper("ImportPagesAtIndex");var _InitText=Module["_InitText"]=createExportWrapper("InitText");var _ClearText=Module["_ClearText"]=createExportWrapper("ClearText");var _GetCountChars=Module["_GetCountChars"]=createExportWrapper("GetCountChars");var _GetRectsCount=Module["_GetRectsCount"]=createExportWrapper("GetRectsCount");var _GetRects=Module["_GetRects"]=createExportWrapper("GetRects");var _GetTextContent=Module["_GetTextContent"]=createExportWrapper("GetTextContent");var _GetCharBoxes=Module["_GetCharBoxes"]=createExportWrapper("GetCharBoxes");var _GetCharsRangeAtPos=Module["_GetCharsRangeAtPos"]=createExportWrapper("GetCharsRangeAtPos");var _GetSelectionForWordAtPos=Module["_GetSelectionForWordAtPos"]=createExportWrapper("GetSelectionForWordAtPos");var _GetCharIndexAtPos=Module["_GetCharIndexAtPos"]=createExportWrapper("GetCharIndexAtPos");var _Test=Module["_Test"]=createExportWrapper("Test");var _InitAction=Module["_InitAction"]=createExportWrapper("InitAction");var _ClearAction=Module["_ClearAction"]=createExportWrapper("ClearAction");var _GetActionType=Module["_GetActionType"]=createExportWrapper("GetActionType");var _SetActionType=Module["_SetActionType"]=createExportWrapper("SetActionType");var _GetDestination=Module["_GetDestination"]=createExportWrapper("GetDestination");var _SetDestination=Module["_SetDestination"]=createExportWrapper("SetDestination");var _GetUriPath=Module["_GetUriPath"]=createExportWrapper("GetUriPath");var _SetUriPath=Module["_SetUriPath"]=createExportWrapper("SetUriPath");var _GetSignatureCount=Module["_GetSignatureCount"]=createExportWrapper("GetSignatureCount");var _InitSignature=Module["_InitSignature"]=createExportWrapper("InitSignature");var _InitSignature2=Module["_InitSignature2"]=createExportWrapper("InitSignature2");var _GeneratePKCS12Cert=Module["_GeneratePKCS12Cert"]=createExportWrapper("GeneratePKCS12Cert");var _GetPKCS12Cert=Module["_GetPKCS12Cert"]=createExportWrapper("GetPKCS12Cert");var _AddSignSaveDocument=Module["_AddSignSaveDocument"]=createExportWrapper("AddSignSaveDocument");var _AddSignModifyDocument=Module["_AddSignModifyDocument"]=createExportWrapper("AddSignModifyDocument");var _RemoveSignature=Module["_RemoveSignature"]=createExportWrapper("RemoveSignature");var _ReleaseSignature=Module["_ReleaseSignature"]=createExportWrapper("ReleaseSignature");var _GetSignatureDiffInfo=Module["_GetSignatureDiffInfo"]=createExportWrapper("GetSignatureDiffInfo");var _GetSignatureFieldName=Module["_GetSignatureFieldName"]=createExportWrapper("GetSignatureFieldName");var _GetSignatureRect=Module["_GetSignatureRect"]=createExportWrapper("GetSignatureRect");var _GetSignatureName=Module["_GetSignatureName"]=createExportWrapper("GetSignatureName");var _GetSignatureTime=Module["_GetSignatureTime"]=createExportWrapper("GetSignatureTime");var _GetPKCS7Time=Module["_GetPKCS7Time"]=createExportWrapper("GetPKCS7Time");var _GetSignatureFilter=Module["_GetSignatureFilter"]=createExportWrapper("GetSignatureFilter");var _GetSignatureSubFilter=Module["_GetSignatureSubFilter"]=createExportWrapper("GetSignatureSubFilter");var _SetSignaturePermissions=Module["_SetSignaturePermissions"]=createExportWrapper("SetSignaturePermissions");var _GetSignaturePermissions=Module["_GetSignaturePermissions"]=createExportWrapper("GetSignaturePermissions");var _SetSignatureReason=Module["_SetSignatureReason"]=createExportWrapper("SetSignatureReason");var _GetSignatureReason=Module["_GetSignatureReason"]=createExportWrapper("GetSignatureReason");var _SetSignatureLocation=Module["_SetSignatureLocation"]=createExportWrapper("SetSignatureLocation");var _GetSignatureLocation=Module["_GetSignatureLocation"]=createExportWrapper("GetSignatureLocation");var _GetSignaturePageIndex=Module["_GetSignaturePageIndex"]=createExportWrapper("GetSignaturePageIndex");var _VerifyTimestamp=Module["_VerifyTimestamp"]=createExportWrapper("VerifyTimestamp");var _IsSameAnnotDict=Module["_IsSameAnnotDict"]=createExportWrapper("IsSameAnnotDict");var _GetSigner=Module["_GetSigner"]=createExportWrapper("GetSigner");var _VerifySigner=Module["_VerifySigner"]=createExportWrapper("VerifySigner");var _VerifyDocument=Module["_VerifyDocument"]=createExportWrapper("VerifyDocument");var _GetTimeStampSignature=Module["_GetTimeStampSignature"]=createExportWrapper("GetTimeStampSignature");var _GetSignCert=Module["_GetSignCert"]=createExportWrapper("GetSignCert");var _GetSignCertChain=Module["_GetSignCertChain"]=createExportWrapper("GetSignCertChain");var _GetSignVersion=Module["_GetSignVersion"]=createExportWrapper("GetSignVersion");var _GetSignAlgOid=Module["_GetSignAlgOid"]=createExportWrapper("GetSignAlgOid");var _GetSignSubject=Module["_GetSignSubject"]=createExportWrapper("GetSignSubject");var _GetSignIssuer=Module["_GetSignIssuer"]=createExportWrapper("GetSignIssuer");var _GetSignSerialNumber=Module["_GetSignSerialNumber"]=createExportWrapper("GetSignSerialNumber");var _GetSignValidDate=Module["_GetSignValidDate"]=createExportWrapper("GetSignValidDate");var _GetAuthorityInfoAccess=Module["_GetAuthorityInfoAccess"]=createExportWrapper("GetAuthorityInfoAccess");var _GetAuthorityKeyIdentifier=Module["_GetAuthorityKeyIdentifier"]=createExportWrapper("GetAuthorityKeyIdentifier");var _GetSubjectKeyIdentifier=Module["_GetSubjectKeyIdentifier"]=createExportWrapper("GetSubjectKeyIdentifier");var _GetKeyUsage=Module["_GetKeyUsage"]=createExportWrapper("GetKeyUsage");var _GetCertificatePolicies=Module["_GetCertificatePolicies"]=createExportWrapper("GetCertificatePolicies");var _GetCRLDistributionPoints=Module["_GetCRLDistributionPoints"]=createExportWrapper("GetCRLDistributionPoints");var _GetBasicConstraints=Module["_GetBasicConstraints"]=createExportWrapper("GetBasicConstraints");var _GetPublicKey=Module["_GetPublicKey"]=createExportWrapper("GetPublicKey");var _GetX509Data=Module["_GetX509Data"]=createExportWrapper("GetX509Data");var _GetSHA1Digest=Module["_GetSHA1Digest"]=createExportWrapper("GetSHA1Digest");var _GetMD5Digest=Module["_GetMD5Digest"]=createExportWrapper("GetMD5Digest");var _GetOCSPUrl=Module["_GetOCSPUrl"]=createExportWrapper("GetOCSPUrl");var _VerifyGetChain=Module["_VerifyGetChain"]=createExportWrapper("VerifyGetChain");var _ExportToFilePath=Module["_ExportToFilePath"]=createExportWrapper("ExportToFilePath");var _bEqualCert=Module["_bEqualCert"]=createExportWrapper("bEqualCert");var _GetX509ByPKCS12Cert=Module["_GetX509ByPKCS12Cert"]=createExportWrapper("GetX509ByPKCS12Cert");var _CheckPKCS12Password=Module["_CheckPKCS12Password"]=createExportWrapper("CheckPKCS12Password");var _WidgetAddToFields=Module["_WidgetAddToFields"]=createExportWrapper("WidgetAddToFields");var _WidgetRemoveFromFields=Module["_WidgetRemoveFromFields"]=createExportWrapper("WidgetRemoveFromFields");var _createPageBitmap=Module["_createPageBitmap"]=createExportWrapper("createPageBitmap");var _destroyPageBitmap=Module["_destroyPageBitmap"]=createExportWrapper("destroyPageBitmap");var _getSpecialColorProportion=Module["_getSpecialColorProportion"]=createExportWrapper("getSpecialColorProportion");var _getCmykColor=Module["_getCmykColor"]=createExportWrapper("getCmykColor");var _getWatermarkCount=Module["_getWatermarkCount"]=createExportWrapper("getWatermarkCount");var _createWatermark=Module["_createWatermark"]=createExportWrapper("createWatermark");var _getWatermark=Module["_getWatermark"]=createExportWrapper("getWatermark");var _setWatermarkScale=Module["_setWatermarkScale"]=createExportWrapper("setWatermarkScale");var _setWatermarkRotation=Module["_setWatermarkRotation"]=createExportWrapper("setWatermarkRotation");var _setWatermarkOpacity=Module["_setWatermarkOpacity"]=createExportWrapper("setWatermarkOpacity");var _setWatermarkVertalign=Module["_setWatermarkVertalign"]=createExportWrapper("setWatermarkVertalign");var _setWatermarkHorizalign=Module["_setWatermarkHorizalign"]=createExportWrapper("setWatermarkHorizalign");var _setWatermarkVertOffset=Module["_setWatermarkVertOffset"]=createExportWrapper("setWatermarkVertOffset");var _setWatermarkHorizOffset=Module["_setWatermarkHorizOffset"]=createExportWrapper("setWatermarkHorizOffset");var _setWatermarkPages=Module["_setWatermarkPages"]=createExportWrapper("setWatermarkPages");var _setWatermarkFront=Module["_setWatermarkFront"]=createExportWrapper("setWatermarkFront");var _getWatermarkScale=Module["_getWatermarkScale"]=createExportWrapper("getWatermarkScale");var _getWatermarkRotation=Module["_getWatermarkRotation"]=createExportWrapper("getWatermarkRotation");var _getWatermarkOpacity=Module["_getWatermarkOpacity"]=createExportWrapper("getWatermarkOpacity");var _getWatermarkVertalign=Module["_getWatermarkVertalign"]=createExportWrapper("getWatermarkVertalign");var _getWatermarkHorizalign=Module["_getWatermarkHorizalign"]=createExportWrapper("getWatermarkHorizalign");var _getWatermarkVertOffset=Module["_getWatermarkVertOffset"]=createExportWrapper("getWatermarkVertOffset");var _getWatermarkHorizOffset=Module["_getWatermarkHorizOffset"]=createExportWrapper("getWatermarkHorizOffset");var _getWatermarkPages=Module["_getWatermarkPages"]=createExportWrapper("getWatermarkPages");var _isWatermarkFront=Module["_isWatermarkFront"]=createExportWrapper("isWatermarkFront");var _isWatermarkFullScreen=Module["_isWatermarkFullScreen"]=createExportWrapper("isWatermarkFullScreen");var _setWatermarkFullScreen=Module["_setWatermarkFullScreen"]=createExportWrapper("setWatermarkFullScreen");var _setWatermarkHorizontalSpacing=Module["_setWatermarkHorizontalSpacing"]=createExportWrapper("setWatermarkHorizontalSpacing");var _getWatermarkHorizontalSpacing=Module["_getWatermarkHorizontalSpacing"]=createExportWrapper("getWatermarkHorizontalSpacing");var _setWatermarkVerticalSpacing=Module["_setWatermarkVerticalSpacing"]=createExportWrapper("setWatermarkVerticalSpacing");var _getWatermarkVerticalSpaceSpacing=Module["_getWatermarkVerticalSpaceSpacing"]=createExportWrapper("getWatermarkVerticalSpaceSpacing");var _setWatermarkText=Module["_setWatermarkText"]=createExportWrapper("setWatermarkText");var _setWatermarkFontName=Module["_setWatermarkFontName"]=createExportWrapper("setWatermarkFontName");var _setWatermarkFontSize=Module["_setWatermarkFontSize"]=createExportWrapper("setWatermarkFontSize");var _setWatermarkTextRGBColor=Module["_setWatermarkTextRGBColor"]=createExportWrapper("setWatermarkTextRGBColor");var _getWatermarkText=Module["_getWatermarkText"]=createExportWrapper("getWatermarkText");var _getWatermarkFontName=Module["_getWatermarkFontName"]=createExportWrapper("getWatermarkFontName");var _getWatermarkFontSize=Module["_getWatermarkFontSize"]=createExportWrapper("getWatermarkFontSize");var _getWatermarkTextRGBColor=Module["_getWatermarkTextRGBColor"]=createExportWrapper("getWatermarkTextRGBColor");var _setWatermarkImage=Module["_setWatermarkImage"]=createExportWrapper("setWatermarkImage");var _setWatermarkImageByPixels=Module["_setWatermarkImageByPixels"]=createExportWrapper("setWatermarkImageByPixels");var _saveWatermarkImage=Module["_saveWatermarkImage"]=createExportWrapper("saveWatermarkImage");var _clearWatermark=Module["_clearWatermark"]=createExportWrapper("clearWatermark");var _updateWatermark=Module["_updateWatermark"]=createExportWrapper("updateWatermark");var _releaseWatermark=Module["_releaseWatermark"]=createExportWrapper("releaseWatermark");var _initRuler=Module["_initRuler"]=createExportWrapper("initRuler");var _destroyRuler=Module["_destroyRuler"]=createExportWrapper("destroyRuler");var _GetMeasureType=Module["_GetMeasureType"]=createExportWrapper("GetMeasureType");var _UpdateAnnotMeasure=Module["_UpdateAnnotMeasure"]=createExportWrapper("UpdateAnnotMeasure");var _setRulerLabel=Module["_setRulerLabel"]=createExportWrapper("setRulerLabel");var _getRulerLabel=Module["_getRulerLabel"]=createExportWrapper("getRulerLabel");var _setMeasureScale=Module["_setMeasureScale"]=createExportWrapper("setMeasureScale");var _getMeasureScale=Module["_getMeasureScale"]=createExportWrapper("getMeasureScale");var _setDecimalSymbol=Module["_setDecimalSymbol"]=createExportWrapper("setDecimalSymbol");var _setThousandSymbol=Module["_setThousandSymbol"]=createExportWrapper("setThousandSymbol");var _setDisplay=Module["_setDisplay"]=createExportWrapper("setDisplay");var _setPrecision=Module["_setPrecision"]=createExportWrapper("setPrecision");var _setUnitPrefix=Module["_setUnitPrefix"]=createExportWrapper("setUnitPrefix");var _setUnitSuffix=Module["_setUnitSuffix"]=createExportWrapper("setUnitSuffix");var _setUnitPosition=Module["_setUnitPosition"]=createExportWrapper("setUnitPosition");var _SetMeasureInfo=Module["_SetMeasureInfo"]=createExportWrapper("SetMeasureInfo");var _getFactor=Module["_getFactor"]=createExportWrapper("getFactor");var _getUnit=Module["_getUnit"]=createExportWrapper("getUnit");var _getDecimalSymbol=Module["_getDecimalSymbol"]=createExportWrapper("getDecimalSymbol");var _getThousandSymbol=Module["_getThousandSymbol"]=createExportWrapper("getThousandSymbol");var _getDisplay=Module["_getDisplay"]=createExportWrapper("getDisplay");var _getPrecision=Module["_getPrecision"]=createExportWrapper("getPrecision");var _getUnitPrefix=Module["_getUnitPrefix"]=createExportWrapper("getUnitPrefix");var _getUnitSuffix=Module["_getUnitSuffix"]=createExportWrapper("getUnitSuffix");var _getUnitPosition=Module["_getUnitPosition"]=createExportWrapper("getUnitPosition");var _getMeasureInfo=Module["_getMeasureInfo"]=createExportWrapper("getMeasureInfo");var _getFormatValue=Module["_getFormatValue"]=createExportWrapper("getFormatValue");var _initSnapShotForPage=Module["_initSnapShotForPage"]=createExportWrapper("initSnapShotForPage");var _closeSnapShot=Module["_closeSnapShot"]=createExportWrapper("closeSnapShot");var _snapToNearest=Module["_snapToNearest"]=createExportWrapper("snapToNearest");var _getMeasurementResults=Module["_getMeasurementResults"]=createExportWrapper("getMeasurementResults");var _setLeadLength=Module["_setLeadLength"]=createExportWrapper("setLeadLength");var _setLeadOffset=Module["_setLeadOffset"]=createExportWrapper("setLeadOffset");var _setLeadExtension=Module["_setLeadExtension"]=createExportWrapper("setLeadExtension");var _getLeadLength=Module["_getLeadLength"]=createExportWrapper("getLeadLength");var _getLeadOffset=Module["_getLeadOffset"]=createExportWrapper("getLeadOffset");var _getLeadExtension=Module["_getLeadExtension"]=createExportWrapper("getLeadExtension");var _setLabelTop=Module["_setLabelTop"]=createExportWrapper("setLabelTop");var _setLabelInside=Module["_setLabelInside"]=createExportWrapper("setLabelInside");var _modifyDistance=Module["_modifyDistance"]=createExportWrapper("modifyDistance");var _getCountResult=Module["_getCountResult"]=createExportWrapper("getCountResult");var _setVertices=Module["_setVertices"]=createExportWrapper("setVertices");var _getVertices=Module["_getVertices"]=createExportWrapper("getVertices");var _isMeasured=Module["_isMeasured"]=createExportWrapper("isMeasured");var _getCaptionTypes=Module["_getCaptionTypes"]=createExportWrapper("getCaptionTypes");var _setMeasureTextTransparency=Module["_setMeasureTextTransparency"]=createExportWrapper("setMeasureTextTransparency");var _getMeasureTextTransparency=Module["_getMeasureTextTransparency"]=createExportWrapper("getMeasureTextTransparency");var _clearPolygonTextTransparency=Module["_clearPolygonTextTransparency"]=createExportWrapper("clearPolygonTextTransparency");var _GetArcRect=Module["_GetArcRect"]=createExportWrapper("GetArcRect");var _SetArcPoints=Module["_SetArcPoints"]=createExportWrapper("SetArcPoints");var _getLayerTree=Module["_getLayerTree"]=createExportWrapper("getLayerTree");var _getRootNode=Module["_getRootNode"]=createExportWrapper("getRootNode");var _getOCPDDict=Module["_getOCPDDict"]=createExportWrapper("getOCPDDict");var _setBaseState=Module["_setBaseState"]=createExportWrapper("setBaseState");var _getBaseState=Module["_getBaseState"]=createExportWrapper("getBaseState");var _isEmpty=Module["_isEmpty"]=createExportWrapper("isEmpty");var _addChildInner=Module["_addChildInner"]=createExportWrapper("addChildInner");var _addChild=Module["_addChild"]=createExportWrapper("addChild");var _removeChild=Module["_removeChild"]=createExportWrapper("removeChild");var _getChildrenCount=Module["_getChildrenCount"]=createExportWrapper("getChildrenCount");var _getChild=Module["_getChild"]=createExportWrapper("getChild");var _setName=Module["_setName"]=createExportWrapper("setName");var _getName=Module["_getName"]=createExportWrapper("getName");var _isLocked=Module["_isLocked"]=createExportWrapper("isLocked");var _setLocked=Module["_setLocked"]=createExportWrapper("setLocked");var _getViewUsage=Module["_getViewUsage"]=createExportWrapper("getViewUsage");var _getExportUsage=Module["_getExportUsage"]=createExportWrapper("getExportUsage");var _getPrintUsage=Module["_getPrintUsage"]=createExportWrapper("getPrintUsage");var _getZoomUsage=Module["_getZoomUsage"]=createExportWrapper("getZoomUsage");var _setViewUsage=Module["_setViewUsage"]=createExportWrapper("setViewUsage");var _setExportUsage=Module["_setExportUsage"]=createExportWrapper("setExportUsage");var _setPrintUsage=Module["_setPrintUsage"]=createExportWrapper("setPrintUsage");var _setZoomUsage=Module["_setZoomUsage"]=createExportWrapper("setZoomUsage");var _removeUsage=Module["_removeUsage"]=createExportWrapper("removeUsage");var _getLayerNodeDict=Module["_getLayerNodeDict"]=createExportWrapper("getLayerNodeDict");var _setLayerNodeDict=Module["_setLayerNodeDict"]=createExportWrapper("setLayerNodeDict");var _setDefaultVisible=Module["_setDefaultVisible"]=createExportWrapper("setDefaultVisible");var _hasLayer=Module["_hasLayer"]=createExportWrapper("hasLayer");var _hasIntent=Module["_hasIntent"]=createExportWrapper("hasIntent");var _isInPage=Module["_isInPage"]=createExportWrapper("isInPage");var _checkOCGVisible=Module["_checkOCGVisible"]=createExportWrapper("checkOCGVisible");var _getOCGS=Module["_getOCGS"]=createExportWrapper("getOCGS");var _exportLayer=Module["_exportLayer"]=createExportWrapper("exportLayer");var _exportDocumentLayer=Module["_exportDocumentLayer"]=createExportWrapper("exportDocumentLayer");var _getPsoMatrix=Module["_getPsoMatrix"]=createExportWrapper("getPsoMatrix");var _importLayerFromPage=Module["_importLayerFromPage"]=createExportWrapper("importLayerFromPage");var _importLayerFromImage=Module["_importLayerFromImage"]=createExportWrapper("importLayerFromImage");var _getPDFSeparationColor=Module["_getPDFSeparationColor"]=createExportWrapper("getPDFSeparationColor");var _getSelectPointColor=Module["_getSelectPointColor"]=createExportWrapper("getSelectPointColor");var _renderPageAndIgnoreColorByIndex=Module["_renderPageAndIgnoreColorByIndex"]=createExportWrapper("renderPageAndIgnoreColorByIndex");var _initColorList=Module["_initColorList"]=createExportWrapper("initColorList");var _getColorsList=Module["_getColorsList"]=createExportWrapper("getColorsList");var ___errno_location=createExportWrapper("__errno_location");var _fflush=Module["_fflush"]=createExportWrapper("fflush");var ___cxa_free_exception=createExportWrapper("__cxa_free_exception");var _ntohs=createExportWrapper("ntohs");var _htonl=createExportWrapper("htonl");var _htons=createExportWrapper("htons");var _emscripten_builtin_memalign=createExportWrapper("emscripten_builtin_memalign");var _setThrew=createExportWrapper("setThrew");var _emscripten_stack_init=function(){return(_emscripten_stack_init=Module["asm"]["emscripten_stack_init"]).apply(null,arguments)};var _emscripten_stack_get_free=function(){return(_emscripten_stack_get_free=Module["asm"]["emscripten_stack_get_free"]).apply(null,arguments)};var _emscripten_stack_get_base=function(){return(_emscripten_stack_get_base=Module["asm"]["emscripten_stack_get_base"]).apply(null,arguments)};var _emscripten_stack_get_end=function(){return(_emscripten_stack_get_end=Module["asm"]["emscripten_stack_get_end"]).apply(null,arguments)};var stackSave=createExportWrapper("stackSave");var stackRestore=createExportWrapper("stackRestore");var stackAlloc=createExportWrapper("stackAlloc");var _emscripten_stack_get_current=function(){return(_emscripten_stack_get_current=Module["asm"]["emscripten_stack_get_current"]).apply(null,arguments)};var ___cxa_decrement_exception_refcount=createExportWrapper("__cxa_decrement_exception_refcount");var ___cxa_increment_exception_refcount=createExportWrapper("__cxa_increment_exception_refcount");var ___cxa_demangle=createExportWrapper("__cxa_demangle");var ___get_exception_message=Module["___get_exception_message"]=createExportWrapper("__get_exception_message");var ___cxa_can_catch=createExportWrapper("__cxa_can_catch");var ___cxa_is_pointer_type=createExportWrapper("__cxa_is_pointer_type");var dynCall_ji=Module["dynCall_ji"]=createExportWrapper("dynCall_ji");var dynCall_jij=Module["dynCall_jij"]=createExportWrapper("dynCall_jij");var dynCall_iiiij=Module["dynCall_iiiij"]=createExportWrapper("dynCall_iiiij");var dynCall_iij=Module["dynCall_iij"]=createExportWrapper("dynCall_iij");var dynCall_iiij=Module["dynCall_iiij"]=createExportWrapper("dynCall_iiij");var dynCall_j=Module["dynCall_j"]=createExportWrapper("dynCall_j");var dynCall_jji=Module["dynCall_jji"]=createExportWrapper("dynCall_jji");var dynCall_iji=Module["dynCall_iji"]=createExportWrapper("dynCall_iji");var dynCall_viijii=Module["dynCall_viijii"]=createExportWrapper("dynCall_viijii");var dynCall_iiji=Module["dynCall_iiji"]=createExportWrapper("dynCall_iiji");var dynCall_jiji=Module["dynCall_jiji"]=createExportWrapper("dynCall_jiji");var dynCall_iiiijii=Module["dynCall_iiiijii"]=createExportWrapper("dynCall_iiiijii");var dynCall_vij=Module["dynCall_vij"]=createExportWrapper("dynCall_vij");var dynCall_jii=Module["dynCall_jii"]=createExportWrapper("dynCall_jii");var dynCall_viiij=Module["dynCall_viiij"]=createExportWrapper("dynCall_viiij");var dynCall_jiiii=Module["dynCall_jiiii"]=createExportWrapper("dynCall_jiiii");var dynCall_iiiiij=Module["dynCall_iiiiij"]=createExportWrapper("dynCall_iiiiij");var dynCall_iiiiijj=Module["dynCall_iiiiijj"]=createExportWrapper("dynCall_iiiiijj");var dynCall_iiiiiijj=Module["dynCall_iiiiiijj"]=createExportWrapper("dynCall_iiiiiijj");var dynCall_viji=Module["dynCall_viji"]=createExportWrapper("dynCall_viji");function invoke_viii(index,a1,a2,a3){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3)}catch(e){stackRestore(sp);if(!(e instanceof EmscriptenEH))throw e;_setThrew(1,0)}}function invoke_iii(index,a1,a2){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2)}catch(e){stackRestore(sp);if(!(e instanceof EmscriptenEH))throw e;_setThrew(1,0)}}function invoke_vi(index,a1){var sp=stackSave();try{getWasmTableEntry(index)(a1)}catch(e){stackRestore(sp);if(!(e instanceof EmscriptenEH))throw e;_setThrew(1,0)}}function invoke_iiii(index,a1,a2,a3){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3)}catch(e){stackRestore(sp);if(!(e instanceof EmscriptenEH))throw e;_setThrew(1,0)}}function invoke_ii(index,a1){var sp=stackSave();try{return getWasmTableEntry(index)(a1)}catch(e){stackRestore(sp);if(!(e instanceof EmscriptenEH))throw e;_setThrew(1,0)}}function invoke_viiii(index,a1,a2,a3,a4){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3,a4)}catch(e){stackRestore(sp);if(!(e instanceof EmscriptenEH))throw e;_setThrew(1,0)}}function invoke_iiiii(index,a1,a2,a3,a4){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3,a4)}catch(e){stackRestore(sp);if(!(e instanceof EmscriptenEH))throw e;_setThrew(1,0)}}function invoke_v(index){var sp=stackSave();try{getWasmTableEntry(index)()}catch(e){stackRestore(sp);if(!(e instanceof EmscriptenEH))throw e;_setThrew(1,0)}}function invoke_vii(index,a1,a2){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2)}catch(e){stackRestore(sp);if(!(e instanceof EmscriptenEH))throw e;_setThrew(1,0)}}function invoke_iiiiiiii(index,a1,a2,a3,a4,a5,a6,a7){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6,a7)}catch(e){stackRestore(sp);if(!(e instanceof EmscriptenEH))throw e;_setThrew(1,0)}}function invoke_iiiiiii(index,a1,a2,a3,a4,a5,a6){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6)}catch(e){stackRestore(sp);if(!(e instanceof EmscriptenEH))throw e;_setThrew(1,0)}}function invoke_iiiiii(index,a1,a2,a3,a4,a5){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3,a4,a5)}catch(e){stackRestore(sp);if(!(e instanceof EmscriptenEH))throw e;_setThrew(1,0)}}function invoke_i(index){var sp=stackSave();try{return getWasmTableEntry(index)()}catch(e){stackRestore(sp);if(!(e instanceof EmscriptenEH))throw e;_setThrew(1,0)}}function invoke_iid(index,a1,a2){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2)}catch(e){stackRestore(sp);if(!(e instanceof EmscriptenEH))throw e;_setThrew(1,0)}}function invoke_iiiiiiiii(index,a1,a2,a3,a4,a5,a6,a7,a8){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6,a7,a8)}catch(e){stackRestore(sp);if(!(e instanceof EmscriptenEH))throw e;_setThrew(1,0)}}function invoke_viiiiii(index,a1,a2,a3,a4,a5,a6){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6)}catch(e){stackRestore(sp);if(!(e instanceof EmscriptenEH))throw e;_setThrew(1,0)}}function invoke_viiiiiii(index,a1,a2,a3,a4,a5,a6,a7){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6,a7)}catch(e){stackRestore(sp);if(!(e instanceof EmscriptenEH))throw e;_setThrew(1,0)}}function invoke_viiiii(index,a1,a2,a3,a4,a5){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3,a4,a5)}catch(e){stackRestore(sp);if(!(e instanceof EmscriptenEH))throw e;_setThrew(1,0)}}function invoke_viiiiiiiii(index,a1,a2,a3,a4,a5,a6,a7,a8,a9){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6,a7,a8,a9)}catch(e){stackRestore(sp);if(!(e instanceof EmscriptenEH))throw e;_setThrew(1,0)}}function invoke_iiiiid(index,a1,a2,a3,a4,a5){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3,a4,a5)}catch(e){stackRestore(sp);if(!(e instanceof EmscriptenEH))throw e;_setThrew(1,0)}}function invoke_iiiiiiiiiii(index,a1,a2,a3,a4,a5,a6,a7,a8,a9,a10){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6,a7,a8,a9,a10)}catch(e){stackRestore(sp);if(!(e instanceof EmscriptenEH))throw e;_setThrew(1,0)}}function invoke_iiiiiiiiiiiii(index,a1,a2,a3,a4,a5,a6,a7,a8,a9,a10,a11,a12){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6,a7,a8,a9,a10,a11,a12)}catch(e){stackRestore(sp);if(!(e instanceof EmscriptenEH))throw e;_setThrew(1,0)}}function invoke_fiii(index,a1,a2,a3){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3)}catch(e){stackRestore(sp);if(!(e instanceof EmscriptenEH))throw e;_setThrew(1,0)}}function invoke_diii(index,a1,a2,a3){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3)}catch(e){stackRestore(sp);if(!(e instanceof EmscriptenEH))throw e;_setThrew(1,0)}}function invoke_iiiiiiiiiiii(index,a1,a2,a3,a4,a5,a6,a7,a8,a9,a10,a11){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6,a7,a8,a9,a10,a11)}catch(e){stackRestore(sp);if(!(e instanceof EmscriptenEH))throw e;_setThrew(1,0)}}function invoke_viiiiiiiiii(index,a1,a2,a3,a4,a5,a6,a7,a8,a9,a10){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6,a7,a8,a9,a10)}catch(e){stackRestore(sp);if(!(e instanceof EmscriptenEH))throw e;_setThrew(1,0)}}function invoke_viiiiiiiiiiiiiii(index,a1,a2,a3,a4,a5,a6,a7,a8,a9,a10,a11,a12,a13,a14,a15){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6,a7,a8,a9,a10,a11,a12,a13,a14,a15)}catch(e){stackRestore(sp);if(!(e instanceof EmscriptenEH))throw e;_setThrew(1,0)}}function invoke_viif(index,a1,a2,a3){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3)}catch(e){stackRestore(sp);if(!(e instanceof EmscriptenEH))throw e;_setThrew(1,0)}}function invoke_viid(index,a1,a2,a3){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3)}catch(e){stackRestore(sp);if(!(e instanceof EmscriptenEH))throw e;_setThrew(1,0)}}function invoke_iiji(index,a1,a2,a3,a4){var sp=stackSave();try{return dynCall_iiji(index,a1,a2,a3,a4)}catch(e){stackRestore(sp);if(!(e instanceof EmscriptenEH))throw e;_setThrew(1,0)}}function invoke_iij(index,a1,a2,a3){var sp=stackSave();try{return dynCall_iij(index,a1,a2,a3)}catch(e){stackRestore(sp);if(!(e instanceof EmscriptenEH))throw e;_setThrew(1,0)}}function invoke_jii(index,a1,a2){var sp=stackSave();try{return dynCall_jii(index,a1,a2)}catch(e){stackRestore(sp);if(!(e instanceof EmscriptenEH))throw e;_setThrew(1,0)}}function invoke_ji(index,a1){var sp=stackSave();try{return dynCall_ji(index,a1)}catch(e){stackRestore(sp);if(!(e instanceof EmscriptenEH))throw e;_setThrew(1,0)}}function invoke_j(index){var sp=stackSave();try{return dynCall_j(index)}catch(e){stackRestore(sp);if(!(e instanceof EmscriptenEH))throw e;_setThrew(1,0)}}function invoke_viijii(index,a1,a2,a3,a4,a5,a6){var sp=stackSave();try{dynCall_viijii(index,a1,a2,a3,a4,a5,a6)}catch(e){stackRestore(sp);if(!(e instanceof EmscriptenEH))throw e;_setThrew(1,0)}}function invoke_iiiiij(index,a1,a2,a3,a4,a5,a6){var sp=stackSave();try{return dynCall_iiiiij(index,a1,a2,a3,a4,a5,a6)}catch(e){stackRestore(sp);if(!(e instanceof EmscriptenEH))throw e;_setThrew(1,0)}}function invoke_jiiii(index,a1,a2,a3,a4){var sp=stackSave();try{return dynCall_jiiii(index,a1,a2,a3,a4)}catch(e){stackRestore(sp);if(!(e instanceof EmscriptenEH))throw e;_setThrew(1,0)}}Module["ccall"]=ccall;Module["cwrap"]=cwrap;Module["stringToNewUTF8"]=stringToNewUTF8;var missingLibrarySymbols=["exitJS","inetPton4","inetNtop4","inetPton6","inetNtop6","readSockaddr","writeSockaddr","getHostByName","traverseStack","getCallstack","emscriptenLog","convertPCtoSourceLocation","readEmAsmArgs","jstoi_q","jstoi_s","listenOnce","autoResumeAudioContext","dynCallLegacy","getDynCaller","dynCall","handleException","runtimeKeepalivePush","runtimeKeepalivePop","callUserCallback","maybeExit","safeSetTimeout","asmjsMangle","HandleAllocator","getNativeTypeSize","STACK_SIZE","STACK_ALIGN","POINTER_SIZE","ASSERTIONS","writeI53ToI64","writeI53ToI64Clamped","writeI53ToI64Signaling","writeI53ToU64Clamped","writeI53ToU64Signaling","readI53FromU64","convertI32PairToI53","convertU32PairToI53","uleb128Encode","sigToWasmTypes","generateFuncType","convertJsFunctionToWasm","getEmptyTableSlot","updateTableMap","getFunctionAddress","addFunction","removeFunction","reallyNegative","unSign","strLen","reSign","formatString","intArrayToString","AsciiToString","UTF16ToString","stringToUTF16","lengthBytesUTF16","UTF32ToString","stringToUTF32","lengthBytesUTF32","registerKeyEventCallback","maybeCStringToJsString","findEventTarget","findCanvasEventTarget","getBoundingClientRect","fillMouseEventData","registerMouseEventCallback","registerWheelEventCallback","registerUiEventCallback","registerFocusEventCallback","fillDeviceOrientationEventData","registerDeviceOrientationEventCallback","fillDeviceMotionEventData","registerDeviceMotionEventCallback","screenOrientation","fillOrientationChangeEventData","registerOrientationChangeEventCallback","fillFullscreenChangeEventData","registerFullscreenChangeEventCallback","JSEvents_requestFullscreen","JSEvents_resizeCanvasForFullscreen","registerRestoreOldStyle","hideEverythingExceptGivenElement","restoreHiddenElements","setLetterbox","softFullscreenResizeWebGLRenderTarget","doRequestFullscreen","fillPointerlockChangeEventData","registerPointerlockChangeEventCallback","registerPointerlockErrorEventCallback","requestPointerLock","fillVisibilityChangeEventData","registerVisibilityChangeEventCallback","registerTouchEventCallback","fillGamepadEventData","registerGamepadEventCallback","registerBeforeUnloadEventCallback","fillBatteryEventData","battery","registerBatteryEventCallback","setCanvasElementSize","getCanvasElementSize","checkWasiClock","wasiRightsToMuslOFlags","wasiOFlagsToMuslOFlags","createDyncallWrapper","setImmediateWrapped","clearImmediateWrapped","polyfillSetImmediate","getPromise","makePromise","idsToPromises","makePromiseCallback","setMainLoop","getSocketFromFD","getSocketAddress","_setNetworkCallback","heapObjectForWebGLType","heapAccessShiftForWebGLHeap","webgl_enable_ANGLE_instanced_arrays","webgl_enable_OES_vertex_array_object","webgl_enable_WEBGL_draw_buffers","webgl_enable_WEBGL_multi_draw","emscriptenWebGLGet","computeUnpackAlignedImageSize","colorChannelsInGlTextureFormat","emscriptenWebGLGetTexPixelData","__glGenObject","emscriptenWebGLGetUniform","webglGetUniformLocation","webglPrepareUniformLocationsBeforeFirstUse","webglGetLeftBracePos","emscriptenWebGLGetVertexAttrib","__glGetActiveAttribOrUniform","writeGLArray","registerWebGlEventCallback","runAndAbortIfError","SDL_unicode","SDL_ttfContext","SDL_audio","GLFW_Window","ALLOC_NORMAL","ALLOC_STACK","allocate","writeStringToMemory","writeAsciiToMemory"];missingLibrarySymbols.forEach(missingLibrarySymbol);var unexportedSymbols=["run","addOnPreRun","addOnInit","addOnPreMain","addOnExit","addOnPostRun","addRunDependency","removeRunDependency","FS_createFolder","FS_createPath","FS_createDataFile","FS_createLazyFile","FS_createLink","FS_createDevice","FS_unlink","out","err","callMain","abort","keepRuntimeAlive","wasmMemory","stackAlloc","stackSave","stackRestore","getTempRet0","setTempRet0","writeStackCookie","checkStackCookie","ptrToString","zeroMemory","getHeapMax","growMemory","ENV","MONTH_DAYS_REGULAR","MONTH_DAYS_LEAP","MONTH_DAYS_REGULAR_CUMULATIVE","MONTH_DAYS_LEAP_CUMULATIVE","isLeapYear","ydayFromDate","arraySum","addDays","ERRNO_CODES","ERRNO_MESSAGES","setErrNo","DNS","Protocols","Sockets","initRandomFill","randomFill","timers","warnOnce","UNWIND_CACHE","readEmAsmArgsArray","getExecutableName","asyncLoad","alignMemory","mmapAlloc","readI53FromI64","convertI32PairToI53Checked","getCFunc","freeTableIndexes","functionsInTableMap","setValue","getValue","PATH","PATH_FS","UTF8Decoder","UTF8ArrayToString","UTF8ToString","stringToUTF8Array","stringToUTF8","lengthBytesUTF8","intArrayFromString","stringToAscii","UTF16Decoder","stringToUTF8OnStack","writeArrayToMemory","JSEvents","specialHTMLTargets","currentFullscreenStrategy","restoreOldWindowedStyle","demangle","demangleAll","jsStackTrace","stackTrace","ExitStatus","getEnvStrings","doReadv","doWritev","promiseMap","uncaughtExceptionCount","exceptionLast","exceptionCaught","ExceptionInfo","getExceptionMessageCommon","incrementExceptionRefcount","decrementExceptionRefcount","getExceptionMessage","Browser","wget","SYSCALLS","preloadPlugins","FS_createPreloadedFile","FS_modeStringToFlags","FS_getMode","FS","MEMFS","TTY","PIPEFS","SOCKFS","tempFixedLengthArray","miniTempWebGLFloatBuffers","miniTempWebGLIntBuffers","GL","emscripten_webgl_power_preferences","AL","GLUT","EGL","GLEW","IDBStore","SDL","SDL_gfx","GLFW","allocateUTF8","allocateUTF8OnStack"];unexportedSymbols.forEach(unexportedRuntimeSymbol);var calledRun;dependenciesFulfilled=function runCaller(){if(!calledRun)run();if(!calledRun)dependenciesFulfilled=runCaller};function stackCheckInit(){_emscripten_stack_init();writeStackCookie()}function run(){if(runDependencies>0){return}stackCheckInit();preRun();if(runDependencies>0){return}function doRun(){if(calledRun)return;calledRun=true;Module["calledRun"]=true;if(ABORT)return;initRuntime();if(Module["onRuntimeInitialized"])Module["onRuntimeInitialized"]();assert(!Module["_main"],'compiled without a main, but one is present. if you added it from JS, use Module["onRuntimeInitialized"]');postRun()}if(Module["setStatus"]){Module["setStatus"]("Running...");setTimeout(function(){setTimeout(function(){Module["setStatus"]("")},1);doRun()},1)}else{doRun()}checkStackCookie()}if(Module["preInit"]){if(typeof Module["preInit"]=="function")Module["preInit"]=[Module["preInit"]];while(Module["preInit"].length>0){Module["preInit"].pop()()}}run();
